package com.yy.framework.config.properties;

import com.yy.common.enums.CaptchaCategory;
import com.yy.common.enums.CaptchaType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 验证码 配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "captcha")
public class CaptchaProperties {

	/**
	 * 验证码类型
 	 */
    private CaptchaType type;

	/**
	 * 验证码类别
	 */
    private CaptchaCategory category;

	/**
	 * 数字验证码位数
	 */
    private Integer numberLength;

	/**
	 * 字符验证码长度
	 */
    private Integer charLength;
}
