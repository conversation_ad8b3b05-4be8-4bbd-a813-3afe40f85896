package com.yy.framework.converter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.Charset;

public class JsonpStringHttpMessageConverter extends StringHttpMessageConverter {

    private String callbackParamName;

    public JsonpStringHttpMessageConverter(Charset defaultCharset) {
        super(defaultCharset);
    }

    @Override
    protected void writeInternal(String str, HttpOutputMessage outputMessage) throws IOException {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String callbackParam = request.getParameter(callbackParamName);
        if (StringUtils.isEmpty(callbackParam)) {
            super.writeInternal(str, outputMessage);
        } else {
            String result = callbackParam + "(" + str + ")";
            super.writeInternal(result, outputMessage);
        }
    }

    public String getCallbackParamName() {
        return callbackParamName;
    }

    public void setCallbackParamName(String callbackParamName) {
        this.callbackParamName = callbackParamName;
    }
}
