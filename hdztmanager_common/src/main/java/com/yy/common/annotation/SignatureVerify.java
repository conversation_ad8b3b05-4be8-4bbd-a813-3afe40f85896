package com.yy.common.annotation;

import java.lang.annotation.*;

/**
 * 签名验证注解
 * 用于标记需要进行AppId和签名验证的接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SignatureVerify {
    
    /**
     * 是否必须验证签名，默认为true
     */
    boolean required() default true;
    
    /**
     * 签名有效期（秒），默认300秒（5分钟）
     */
    long expireTime() default 300;
    
    /**
     * 描述信息
     */
    String value() default "";
}
