package com.yy.common.mybatis.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yy.common.utils.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@MappedTypes({Map.class})
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR})
public class MapTypeHandler<K, V> implements TypeHandler<Map<K, V>> {

    @Override
    public void setParameter(PreparedStatement ps, int i, Map<K, V> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setString(i, null);
        } else {
            ps.setString(i, JSON.toJSONString(parameter));
        }
    }

    @Override
    public Map<K, V> getResult(ResultSet rs, String columnName) throws SQLException {
        String content = rs.getString(columnName);
        if (StringUtils.startsWith(content, StringUtils.OPEN_BRACE)) {
            return JSON.parseObject(content, new TypeReference<HashMap<K, V>>(){});
        }

        return Collections.emptyMap();
    }

    @Override
    public Map<K, V> getResult(ResultSet rs, int columnIndex) throws SQLException {
        String content = rs.getString(columnIndex);
        if (StringUtils.startsWith(content, StringUtils.OPEN_BRACE)) {
            return JSON.parseObject(content, new TypeReference<HashMap<K, V>>(){});
        }

        return Collections.emptyMap();
    }

    @Override
    public Map<K, V> getResult(CallableStatement cs, int columnIndex) throws SQLException {
        String content = cs.getString(columnIndex);
        if (StringUtils.startsWith(content, StringUtils.OPEN_BRACE)) {
            return JSON.parseObject(content, new TypeReference<HashMap<K, V>>(){});
        }

        return Collections.emptyMap();
    }
}
