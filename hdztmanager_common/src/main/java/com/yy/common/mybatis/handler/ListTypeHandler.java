package com.yy.common.mybatis.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR})
public class ListTypeHandler<T> implements TypeHandler<List<T>> {

    public static final String OPEN_BRACKET = "[";

    @Override
    public void setParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setString(i, null);
        } else {
            ps.setString(i, JSON.toJSONString(parameter));
        }
    }

    @Override
    public List<T> getResult(ResultSet rs, String columnName) throws SQLException {
        String content = rs.getString(columnName);
        if (StringUtils.isEmpty(content) || !content.startsWith(OPEN_BRACKET)) {
            return Collections.emptyList();
        }
        return JSON.parseObject(content, new TypeReference<ArrayList<T>>() {
        });
    }

    @Override
    public List<T> getResult(ResultSet rs, int columnIndex) throws SQLException {
        String content = rs.getString(columnIndex);
        if (StringUtils.isEmpty(content) || !content.startsWith(OPEN_BRACKET)) {
            return Collections.emptyList();
        }
        return JSON.parseObject(content, new TypeReference<ArrayList<T>>() {
        });
    }

    @Override
    public List<T> getResult(CallableStatement cs, int columnIndex) throws SQLException {
        String content = cs.getString(columnIndex);
        if (StringUtils.isEmpty(content) || !content.startsWith(OPEN_BRACKET)) {
            return Collections.emptyList();
        }
        return JSON.parseObject(content, new TypeReference<ArrayList<T>>() {
        });
    }
}
