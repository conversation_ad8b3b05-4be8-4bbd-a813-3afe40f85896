package com.yy.manager;

import org.junit.Test;
import io.swagger.annotations.ApiOperation;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.yy.common.annotation.Log;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

/**
 * Controller扫描测试类
 * 用于扫描项目中所有的Controller，输出接口URL和权限信息
 * 不使用SpringBootTest，直接扫描指定包下的Controller类
 * 输出为表格形式
 */
public class ControllerScannerTest {

    // 匹配@ss.hasPermi('xxx')或@ss.hasPermi('xxx','yyy')格式的正则表达式
    private static final Pattern PERMISSION_PATTERN = Pattern.compile("@ss\\.hasPermi\\('([^']+)'(?:,'([^']+)')?\\)");

    @Test
    public void scanControllers() throws Exception {
        Set<Class<?>> classSet = new HashSet<>();

        String[] packages = {"com.yy.web.controller", "com.yy.manager", "com.yy.generator", "com.yy.oss", "com.yy.system"};

        // 获取指定包下所有的类
        for (String pkg : packages) {
            classSet.addAll(findControllerClasses(pkg));
        }
        List<Class<?>> controllerClasses = new ArrayList<>(classSet);
        // 按类名排序
        controllerClasses.sort(Comparator.comparing(Class::getName));

        // 创建结果列表
        List<ControllerInfo> results = new ArrayList<>();

        for (Class<?> clazz : controllerClasses) {
            // 获取类上的@RequestMapping注解
            String baseUrl = "";
            RequestMapping classMapping = AnnotationUtils.findAnnotation(clazz, RequestMapping.class);
            if (classMapping != null && classMapping.value().length > 0) {
                baseUrl = classMapping.value()[0];
            }

            // 获取所有公共方法
            Method[] methods = clazz.getDeclaredMethods();

            // 按方法名排序
            Arrays.sort(methods, Comparator.comparing(Method::getName));

            for (Method method : methods) {
                // 查找方法上的HTTP请求映射注解
                String httpMethod = "";
                String methodUrl = "";

                if (method.isAnnotationPresent(GetMapping.class)) {
                    httpMethod = "GET";
                    GetMapping mapping = method.getAnnotation(GetMapping.class);
                    if (mapping.value().length > 0) {
                        methodUrl = mapping.value()[0];
                    }
                } else if (method.isAnnotationPresent(PostMapping.class)) {
                    httpMethod = "POST";
                    PostMapping mapping = method.getAnnotation(PostMapping.class);
                    if (mapping.value().length > 0) {
                        methodUrl = mapping.value()[0];
                    }
                } else if (method.isAnnotationPresent(PutMapping.class)) {
                    httpMethod = "PUT";
                    PutMapping mapping = method.getAnnotation(PutMapping.class);
                    if (mapping.value().length > 0) {
                        methodUrl = mapping.value()[0];
                    }
                } else if (method.isAnnotationPresent(DeleteMapping.class)) {
                    httpMethod = "DELETE";
                    DeleteMapping mapping = method.getAnnotation(DeleteMapping.class);
                    if (mapping.value().length > 0) {
                        methodUrl = mapping.value()[0];
                    }
                } else if (method.isAnnotationPresent(PatchMapping.class)) {
                    httpMethod = "PATCH";
                    PatchMapping mapping = method.getAnnotation(PatchMapping.class);
                    if (mapping.value().length > 0) {
                        methodUrl = mapping.value()[0];
                    }
                } else if (method.isAnnotationPresent(RequestMapping.class)) {
                    RequestMapping mapping = method.getAnnotation(RequestMapping.class);
                    if (mapping.method().length > 0) {
                        httpMethod = mapping.method()[0].name();
                    } else {
                        httpMethod = "ANY";
                    }
                    if (mapping.value().length > 0) {
                        methodUrl = mapping.value()[0];
                    }
                }

                // 如果找到了HTTP请求映射注解
                if (!httpMethod.isEmpty()) {
                    // 构建完整URL
                    String fullUrl = baseUrl;
                    if (!methodUrl.isEmpty()) {
                        if (!baseUrl.endsWith("/") && !methodUrl.startsWith("/")) {
                            fullUrl += "/";
                        }
                        fullUrl += methodUrl;
                    }

                    fullUrl = fullUrl.replaceAll("\\{[^}]+}", "{}");
                    if (!fullUrl.startsWith("/")) {
                        fullUrl = "/" + fullUrl;
                    }

                    // 获取权限注解
                    String permission = "";
                    PreAuthorize preAuthorize = method.getAnnotation(PreAuthorize.class);
                    if (preAuthorize != null) {
                        String value = preAuthorize.value();
                        Matcher matcher = PERMISSION_PATTERN.matcher(value);
                        if (matcher.find()) {
                            permission = matcher.group(1);
//                            if (matcher.groupCount() > 1 && matcher.group(2) != null) {
//                                permission += " (环境: " + matcher.group(2) + ")";
//                            }
                        } else {
                            permission = value; // 如果不匹配模式，则显示原始值
                        }
                    }

                    // 获取接口功能描述
                    String functionDesc = getFunctionDescription(method);

                    // 添加到结果列表
                    results.add(new ControllerInfo(
                            clazz.getName(),
                            method.getName(),
                            httpMethod,
                            fullUrl,
                            permission,
                            functionDesc
                    ));
                }
            }
        }

        // 输出表格头
        System.out.println("+----------------------------------+------------------+----------+----------------------------------+----------------------------------+");
        System.out.println("| Controller                       | 方法名           | 请求方法   | URL路径                          | 权限                             |");
        System.out.println("+----------------------------------+------------------+----------+----------------------------------+----------------------------------+");

        // 输出表格内容
        for (ControllerInfo info : results) {
            System.out.printf("| %-32s | %-16s | %-8s | %-32s | %-32s |%n",
                    truncate(info.getControllerName(), 32),
                    truncate(info.getMethodName(), 16),
                    info.getHttpMethod(),
                    truncate(info.getUrlPath(), 32),
                    truncate(info.getPermission(), 32));
        }

        // 输出表格底部
        System.out.println("+----------------------------------+------------------+----------+----------------------------------+----------------------------------+");
        System.out.println("总计: " + results.size() + " 个接口");

        // 将结果导出到Excel文件
        exportToExcel(results);

        // 生成SQL文件
        generateSqlFile(results);

        // 生成没有权限code的URL列表
        generateNoPermissionUrlList(results);

        // 生成有权限code的URL列表
        generateWithPermissionUrlList(results);

        // 生成CSV格式的接口信息
        generateCsvFile(results);
    }

    /**
     * 截断字符串，使其不超过指定长度
     */
    private String truncate(String str, int length) {
        if (str == null) {
            return "";
        }
        if (str.length() <= length) {
            return str;
        }
        return str.substring(0, length - 3) + "...";
    }

    /**
     * 查找指定包下所有的Controller类
     *
     * @param basePackage 基础包名
     * @return Controller类列表
     */
    private List<Class<?>> findControllerClasses(String basePackage) throws Exception {
        List<Class<?>> controllers = new ArrayList<>();
        String path = basePackage.replace('.', '/');

        // 获取类加载器
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        Enumeration<URL> resources = classLoader.getResources(path);

        // 遍历所有资源路径
        while (resources.hasMoreElements()) {
            URL resource = resources.nextElement();
            File directory = new File(resource.getFile());
            controllers.addAll(findControllers(directory, basePackage));
        }

        return controllers;
    }

    /**
     * 递归查找指定目录下的Controller类
     *
     * @param directory   目录
     * @param basePackage 基础包名
     * @return Controller类列表
     */
    private List<Class<?>> findControllers(File directory, String basePackage) {
        List<Class<?>> controllers = new ArrayList<>();

        if (!directory.exists()) {
            return controllers;
        }

        // 遍历目录下的所有文件
        File[] files = directory.listFiles();
        if (files == null) {
            return controllers;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                // 如果是目录，递归扫描
                controllers.addAll(findControllers(file, basePackage + "." + file.getName()));
            } else if (file.getName().endsWith(".class")) {
                // 如果是类文件
                String className = basePackage + "." + file.getName().substring(0, file.getName().length() - 6);
                try {
                    Class<?> clazz = Class.forName(className);
                    // 检查是否有Controller或RestController注解
                    if (clazz.isAnnotationPresent(Controller.class) || clazz.isAnnotationPresent(RestController.class)) {
                        controllers.add(clazz);
                    }
                } catch (ClassNotFoundException | NoClassDefFoundError e) {
                    // 忽略加载失败的类
                }
            }
        }

        return controllers;
    }

    /**
     * 将结果导出到Excel文件
     *
     * @param results 接口信息列表
     */
    private void exportToExcel(List<ControllerInfo> results) {
        String excelFilePath = "controller_api_permissions.xlsx";

        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建Sheet
            Sheet sheet = workbook.createSheet("Controller接口权限");

            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);

            // 创建数据行样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"Controller", "方法名", "请求方法", "URL路径", "权限", "接口功能"};

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            int rowNum = 1;
            for (ControllerInfo info : results) {
                Row row = sheet.createRow(rowNum++);

                Cell cell0 = row.createCell(0);
                cell0.setCellValue(info.getControllerName());
                cell0.setCellStyle(dataStyle);

                Cell cell1 = row.createCell(1);
                cell1.setCellValue(info.getMethodName());
                cell1.setCellStyle(dataStyle);

                Cell cell2 = row.createCell(2);
                cell2.setCellValue(info.getHttpMethod());
                cell2.setCellStyle(dataStyle);

                Cell cell3 = row.createCell(3);
                cell3.setCellValue(info.getUrlPath());
                cell3.setCellStyle(dataStyle);

                Cell cell4 = row.createCell(4);
                cell4.setCellValue(info.getPermission());
                cell4.setCellStyle(dataStyle);

                Cell cell5 = row.createCell(5);
                cell5.setCellValue(info.getFunctionDesc());
                cell5.setCellStyle(dataStyle);
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入文件
            try (FileOutputStream outputStream = new FileOutputStream(excelFilePath)) {
                workbook.write(outputStream);
                System.out.println("\n已将结果导出到Excel文件: " + excelFilePath);
            }

        } catch (IOException e) {
            System.out.println("\n导出到Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * Controller信息类，用于存储Controller的相关信息
     */
    static class ControllerInfo {
        private final String controllerName;
        private final String methodName;
        private final String httpMethod;
        private final String urlPath;
        private final String permission;
        private final String functionDesc;

        public ControllerInfo(String controllerName, String methodName, String httpMethod, String urlPath, String permission, String functionDesc) {
            this.controllerName = controllerName;
            this.methodName = methodName;
            this.httpMethod = httpMethod;
            this.urlPath = urlPath;
            this.permission = permission;
            this.functionDesc = functionDesc;
        }

        public String getControllerName() {
            return controllerName;
        }

        public String getMethodName() {
            return methodName;
        }

        public String getHttpMethod() {
            return httpMethod;
        }

        public String getUrlPath() {
            return urlPath;
        }

        public String getPermission() {
            return permission;
        }

        public String getFunctionDesc() {
            return functionDesc;
        }
    }

    /**
     * 生成SQL文件
     * 只有URL路径和权限都不为空时才生成SQL语句
     *
     * @param results 接口信息列表
     */
    private void generateSqlFile(List<ControllerInfo> results) {
        String sqlFilePath = "controller_permissions.sql";

        try (FileWriter writer = new FileWriter(sqlFilePath)) {
            int count = 0;

            for (ControllerInfo info : results) {
                String urlPath = info.getUrlPath();
                String permission = info.getPermission();

                // 只有URL路径和权限都不为空时才生成SQL语句
                if (urlPath != null && !urlPath.isEmpty() && permission != null && !permission.isEmpty()) {
                    // 确保URL路径以"/"开头
                    String formattedUrl = urlPath;
                    if (!formattedUrl.startsWith("/")) {
                        formattedUrl = "/" + formattedUrl;
                    }

                    String sql = String.format("update zy_auth_permission set url = '%s' where access_key in ('hdzt-adminV2', 'hdzt-tec-admin', 'hdzt-adminV2-zwzdel','hdzt-tec-admin-zwzdel') and permission = '%s';\n",
                            formattedUrl, permission);
                    writer.write(sql);
                    count++;
                }
            }

            writer.flush();
            System.out.println("\n已生成SQL文件: " + sqlFilePath + ", 包含 " + count + " 条SQL语句");
        } catch (IOException e) {
            System.out.println("\n生成SQL文件失败: " + e.getMessage());
        }
    }

    /**
     * 生成没有权限code的URL列表
     *
     * @param results 接口信息列表
     */
    private void generateNoPermissionUrlList(List<ControllerInfo> results) {
        String txtFilePath = "no_permission_urls.txt";

        try (FileWriter writer = new FileWriter(txtFilePath)) {
            int count = 0;

            // 写入文件头
            writer.write("# 没有权限code的URL列表\n");
            writer.write("# 格式: [HTTP方法] [URL路径] - [Controller类].[Controller方法] - [接口功能]\n\n");

            for (ControllerInfo info : results) {
                String urlPath = info.getUrlPath();
                String permission = info.getPermission();

                // 如果URL路径不为空但权限为空
                if (urlPath != null && !urlPath.isEmpty() && (permission == null || permission.isEmpty())) {
                    // 确保URL路径以"/"开头
                    String formattedUrl = urlPath;
                    if (!formattedUrl.startsWith("/")) {
                        formattedUrl = "/" + formattedUrl;
                    }

                    String line = String.format("%s %s - %s.%s - %s\n",
                            info.getHttpMethod(),
                            formattedUrl,
                            info.getControllerName(),
                            info.getMethodName(),
                            info.getFunctionDesc());
                    writer.write(line);
                    count++;
                }
            }

            writer.flush();
            System.out.println("\n已生成没有权限code的URL列表: " + txtFilePath + ", 包含 " + count + " 条记录");
        } catch (IOException e) {
            System.out.println("\n生成没有权限code的URL列表失败: " + e.getMessage());
        }
    }

    /**
     * 生成有权限code的URL列表
     *
     * @param results 接口信息列表
     */
    private void generateWithPermissionUrlList(List<ControllerInfo> results) {
        String txtFilePath = "with_permission_urls.txt";

        try (FileWriter writer = new FileWriter(txtFilePath)) {
            int count = 0;

            // 写入文件头
            writer.write("# 有权限code的URL列表\n");
            writer.write("# 格式: [HTTP方法] [URL路径] - [Controller类].[Controller方法] - [权限code] - [接口功能]\n\n");

            for (ControllerInfo info : results) {
                String urlPath = info.getUrlPath();
                String permission = info.getPermission();

                // 如果URL路径和权限都不为空
                if (urlPath != null && !urlPath.isEmpty() && permission != null && !permission.isEmpty()) {
                    // 确保URL路径以"/"开头
                    String formattedUrl = urlPath;
                    if (!formattedUrl.startsWith("/")) {
                        formattedUrl = "/" + formattedUrl;
                    }

                    String line = String.format("%s %s - %s.%s - %s - %s\n",
                            info.getHttpMethod(),
                            formattedUrl,
                            info.getControllerName(),
                            info.getMethodName(),
                            permission,
                            info.getFunctionDesc());
                    writer.write(line);
                    count++;
                }
            }

            writer.flush();
            System.out.println("\n已生成有权限code的URL列表: " + txtFilePath + ", 包含 " + count + " 条记录");
        } catch (IOException e) {
            System.out.println("\n生成有权限code的URL列表失败: " + e.getMessage());
        }
    }

    /**
     * 生成CSV格式的接口信息文件
     *
     * @param results 接口信息列表
     */
    private void generateCsvFile(List<ControllerInfo> results) {
        String csvFilePath = "controller_api_permissions.csv";

        try (FileWriter writer = new FileWriter(csvFilePath)) {
            // 写入CSV头
            writer.write("Controller,方法名,请求方法,URL路径,权限,接口功能\n");

            // 写入数据
            for (ControllerInfo info : results) {
                writer.write(escapeCSV(info.getControllerName()) + ",");
                writer.write(escapeCSV(info.getMethodName()) + ",");
                writer.write(escapeCSV(info.getHttpMethod()) + ",");

                // 确保URL路径以"/"开头
                String formattedUrl = info.getUrlPath();
                if (formattedUrl != null && !formattedUrl.isEmpty() && !formattedUrl.startsWith("/")) {
                    formattedUrl = "/" + formattedUrl;
                }
                writer.write(escapeCSV(formattedUrl) + ",");
                writer.write(escapeCSV(info.getPermission()) + ",");
                writer.write(escapeCSV(info.getFunctionDesc()) + "\n");
            }

            writer.flush();
            System.out.println("\n已生成CSV格式的接口信息文件: " + csvFilePath);
        } catch (IOException e) {
            System.out.println("\n生成CSV格式的接口信息文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取方法的功能描述
     * 优先级：@ApiOperation > @Log > 其他注解 > 方法名
     *
     * @param method 方法
     * @return 功能描述
     */
    private String getFunctionDescription(Method method) {
        // 先尝试获取@ApiOperation注解
        ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
        if (apiOperation != null && StringUtils.isNotBlank(apiOperation.value())) {
            return apiOperation.value();
        }

        // 如果没有，则尝试获取@Log注解
        Log log = method.getAnnotation(Log.class);
        if (log != null && StringUtils.isNotBlank(log.title())) {
            return log.title();
        }

        // 如果都没有，则使用方法名
        return method.getName();
    }

    /**
     * 处理CSV字段中的特殊字符
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        // 如果字段包含逗号、双引号或换行符，则用双引号包裹并将内部的双引号替换为两个双引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
}
