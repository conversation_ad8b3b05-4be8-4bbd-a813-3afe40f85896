package com.yy;

import com.yy.manager.datasource.database.DataSourceSelector;
import com.yy.manager.hdztmanager.mapper.CommonMapper;
import com.yy.manager.template.service.HdptConfigTemplateService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppRun.class)
public class AppTests {

    @Autowired
    private HdptConfigTemplateService hdptConfigTemplateService;

    @Resource
    private CommonMapper commonMapper;

    @Test
    public void test2() {
        System.out.println(hdptConfigTemplateService.queryPreviewConfig("2085021001"));
    }

    @Test
    public void test3() {
        try (DataSourceSelector ignored = DataSourceSelector.doWithActId("hdzt", 2025011001)) {
            Long maxId = commonMapper.selectMaxId("award_package", "package_id");
            System.out.println(maxId);
        }
    }

    @Test
    public void test4() {
        try (DataSourceSelector ignored = DataSourceSelector.doWithActId("hdzt", 2024021001)) {
            List<LinkedHashMap<String, Object>> data = commonMapper.commonSelect("hdzt_activity", "act_id=2024021001", 2024021001L);
            System.out.println(data);
        }
    }
}
