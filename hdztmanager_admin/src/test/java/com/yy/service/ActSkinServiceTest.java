package com.yy.service;

import com.yy.AppRun;
import com.yy.manager.acttemplate.service.ActSkinService;
import com.yy.manager.acttemplate.vo.SaveSkinResourceVo;
import com.yy.manager.acttemplate.vo.SkinResourceInfoVo;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-04-07 20:09
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppRun.class)
@ActiveProfiles("dev")
public class ActSkinServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActSkinService actSkinService;

    @Test
    public void uploadTest(){
        SaveSkinResourceVo param = new SaveSkinResourceVo();
        param.setActId("2024047001");
        param.setRemark("皮肤数据提交");
        List<SkinResourceInfoVo> sourceList = Lists.newArrayList();

        SkinResourceInfoVo vo1 = new SkinResourceInfoVo();
        vo1.setContent("code layer 1");
        vo1.setName("layer_001");
        sourceList.add(vo1);

        SkinResourceInfoVo vo2 = new SkinResourceInfoVo();
        vo2.setContent("code layer 2");
        vo2.setName("layer_002");
        sourceList.add(vo2);


        param.setSourceList(sourceList);
        param.setEnv("DEV");
        actSkinService.saveActSkin(param);
    }
}
