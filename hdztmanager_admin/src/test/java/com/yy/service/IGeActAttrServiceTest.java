package com.yy.service;

import com.yy.AppRun;
import com.yy.manager.ecology.service.IGeActAttrService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-05-06 19:30
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppRun.class)
@ActiveProfiles("dev")
public class IGeActAttrServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IGeActAttrService geActAttrService;

    @Test
    public void test(){
        geActAttrService.select(202012001,"test");
    }


}
