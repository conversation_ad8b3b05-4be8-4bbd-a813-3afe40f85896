package com.yy.service;

import com.yy.AppRun;
import com.yy.common.utils.DateUtils;
import com.yy.manager.template.service.HdptConfigTemplateService;
import com.yy.manager.template.vo.CopyActivityRequest;
import com.yy.manager.utils.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-05-06 11:12
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppRun.class)
@ActiveProfiles("dev")
public class HdptConfigTemplateServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private HdptConfigTemplateService hdptConfigTemplateService;

    @Test
    public void genActConfig() {
        CopyActivityRequest request = new CopyActivityRequest();

        request.setBaseActId(2024017001);
        request.setActId(2024057001);
        request.setActName("Yo开黑王者荣耀赏金赛二期");
        request.setBeginTime(DateUtil.getDate("2024-03-18 16:40:00"));
        request.setEndTime(DateUtil.getDate("2024-04-15 23:59:59"));
        hdptConfigTemplateService.copyActivityConfig(50048391L, request);


    }

    @Test
    public void testPreview() {
        CopyActivityRequest request = new CopyActivityRequest();
        request.setBaseActId(2025037001);
        request.setActId(2025047001);
        request.setBeginTime(DateUtil.getDate("2025-04-04 17:00:00"));
        request.setEndTime(DateUtil.getDate("2025-04-14 23:59:59"));

        hdptConfigTemplateService.queryPreviewConfig(request);
    }
}
