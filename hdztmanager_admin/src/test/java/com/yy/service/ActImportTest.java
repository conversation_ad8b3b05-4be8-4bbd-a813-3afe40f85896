package com.yy.service;

import com.google.common.collect.Lists;
import com.yy.AppRun;
import com.yy.common.utils.file.FileUtils;
import com.yy.manager.rankexcelgen.bean.excel.ExcelConfig;
import com.yy.manager.rankexcelgen.service.ActConfigDbConvertor;
import com.yy.manager.rankexcelgen.service.ActConfigExcelConvertor;
import com.yy.manager.utils.EmailUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedInputStream;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2022/7/7 14:20
 * @Modified:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppRun.class)
@ActiveProfiles("dev")
public class ActImportTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActConfigExcelConvertor excelConvertor;

    @Autowired
    private ActConfigDbConvertor dbConvertor;
    @Test
    public void importByExcelStreamTest(){

        while (true){
            try {
                BufferedInputStream inputStream = FileUtils.getInputStream("C:\\Users\\<USER>\\Downloads\\21年度交约宝个人预选赛 (5).xlsx");
                ExcelConfig excelConfig = excelConvertor.importByExcelStream(inputStream);

                dbConvertor.toDbData( excelConfig);
                System.out.println();
            } catch (Exception e){
                log.error("",e);
            }

        }

    }

    @Test
    public void emailTest() throws Exception {
        EmailUtil.send("teststesttetest", Lists.newArrayList("<EMAIL>"));
    }


}
