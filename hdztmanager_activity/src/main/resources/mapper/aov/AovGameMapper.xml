<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.aov.mapper.AovGameMapper">
  <resultMap id="BaseResultMap" type="com.yy.manager.aov.entity.AovGame">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="round_num" jdbcType="INTEGER" property="roundNum" />
    <result column="round_name" jdbcType="VARCHAR" property="roundName" />
    <result column="advance_node_index" jdbcType="INTEGER" property="advanceNodeIndex" />
    <result column="camp1_team_id" jdbcType="BIGINT" property="camp1TeamId" />
    <result column="camp2_team_id" jdbcType="BIGINT" property="camp2TeamId" />
    <result column="bo" jdbcType="INTEGER" property="bo" />
    <result column="battle_mode" jdbcType="INTEGER" property="battleMode" />
    <result column="cur_bo" jdbcType="INTEGER" property="curBo" />
    <result column="child_id" jdbcType="VARCHAR" property="childId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="settle_state" jdbcType="INTEGER" property="settleState" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="ready_time" jdbcType="TIMESTAMP" property="readyTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, phase_id, round_id, round_num, round_name, advance_node_index, camp1_team_id, 
    camp2_team_id, bo, battle_mode, cur_bo, child_id, state, settle_state, start_time, 
    ready_time, end_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aov_game
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectPagePhaseGames" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from aov_game
      where phase_id = #{phaseId}
      <if test="roundNum != null">
        and round_num = #{roundNum}
      </if>
      <if test="state != null">
        and `state` = #{state}
      </if>
      <if test="teamId != null">
        and (camp1_team_id = #{teamId} or camp2_team_id = #{teamId})
      </if>
      order by start_time desc
      limit #{offset}, #{limit}
    </select>
  <select id="countPhaseGames" resultType="java.lang.Integer">
    select count(1) from aov_game
    where phase_id = #{phaseId}
    <if test="roundNum != null">
      and round_num = #{roundNum}
    </if>
    <if test="state != null">
      and `state` = #{state}
    </if>
    <if test="teamId != null">
      and (camp1_team_id = #{teamId} or camp2_team_id = #{teamId})
    </if>
  </select>
  <select id="selectRoundGames" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from aov_game
    where phase_id = #{phaseId}
    <if test="roundNum != null">
      and round_num = #{roundNum}
    </if>
    <if test="curBo != null">
      and cur_bo = #{curBo}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_game
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.manager.aov.entity.AovGame">
    insert into aov_game (id, phase_id, round_id, 
      round_num, round_name, advance_node_index, 
      camp1_team_id, camp2_team_id, bo, 
      battle_mode, cur_bo, child_id, 
      state, settle_state, start_time, 
      ready_time, end_time)
    values (#{id,jdbcType=BIGINT}, #{phaseId,jdbcType=BIGINT}, #{roundId,jdbcType=BIGINT}, 
      #{roundNum,jdbcType=INTEGER}, #{roundName,jdbcType=VARCHAR}, #{advanceNodeIndex,jdbcType=INTEGER}, 
      #{camp1TeamId,jdbcType=BIGINT}, #{camp2TeamId,jdbcType=BIGINT}, #{bo,jdbcType=INTEGER}, 
      #{battleMode,jdbcType=INTEGER}, #{curBo,jdbcType=INTEGER}, #{childId,jdbcType=VARCHAR}, 
      #{state,jdbcType=INTEGER}, #{settleState,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, 
      #{readyTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.manager.aov.entity.AovGame">
    insert into aov_game
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phaseId != null">
        phase_id,
      </if>
      <if test="roundId != null">
        round_id,
      </if>
      <if test="roundNum != null">
        round_num,
      </if>
      <if test="roundName != null">
        round_name,
      </if>
      <if test="advanceNodeIndex != null">
        advance_node_index,
      </if>
      <if test="camp1TeamId != null">
        camp1_team_id,
      </if>
      <if test="camp2TeamId != null">
        camp2_team_id,
      </if>
      <if test="bo != null">
        bo,
      </if>
      <if test="battleMode != null">
        battle_mode,
      </if>
      <if test="curBo != null">
        cur_bo,
      </if>
      <if test="childId != null">
        child_id,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="settleState != null">
        settle_state,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="readyTime != null">
        ready_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="roundName != null">
        #{roundName,jdbcType=VARCHAR},
      </if>
      <if test="advanceNodeIndex != null">
        #{advanceNodeIndex,jdbcType=INTEGER},
      </if>
      <if test="camp1TeamId != null">
        #{camp1TeamId,jdbcType=BIGINT},
      </if>
      <if test="camp2TeamId != null">
        #{camp2TeamId,jdbcType=BIGINT},
      </if>
      <if test="bo != null">
        #{bo,jdbcType=INTEGER},
      </if>
      <if test="battleMode != null">
        #{battleMode,jdbcType=INTEGER},
      </if>
      <if test="curBo != null">
        #{curBo,jdbcType=INTEGER},
      </if>
      <if test="childId != null">
        #{childId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="settleState != null">
        #{settleState,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="readyTime != null">
        #{readyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.manager.aov.entity.AovGame">
    update aov_game
    <set>
      <if test="phaseId != null">
        phase_id = #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        round_num = #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="roundName != null">
        round_name = #{roundName,jdbcType=VARCHAR},
      </if>
      <if test="advanceNodeIndex != null">
        advance_node_index = #{advanceNodeIndex,jdbcType=INTEGER},
      </if>
      <if test="camp1TeamId != null">
        camp1_team_id = #{camp1TeamId,jdbcType=BIGINT},
      </if>
      <if test="camp2TeamId != null">
        camp2_team_id = #{camp2TeamId,jdbcType=BIGINT},
      </if>
      <if test="bo != null">
        bo = #{bo,jdbcType=INTEGER},
      </if>
      <if test="battleMode != null">
        battle_mode = #{battleMode,jdbcType=INTEGER},
      </if>
      <if test="curBo != null">
        cur_bo = #{curBo,jdbcType=INTEGER},
      </if>
      <if test="childId != null">
        child_id = #{childId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="settleState != null">
        settle_state = #{settleState,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="readyTime != null">
        ready_time = #{readyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.manager.aov.entity.AovGame">
    update aov_game
    set phase_id = #{phaseId,jdbcType=BIGINT},
      round_id = #{roundId,jdbcType=BIGINT},
      round_num = #{roundNum,jdbcType=INTEGER},
      round_name = #{roundName,jdbcType=VARCHAR},
      advance_node_index = #{advanceNodeIndex,jdbcType=INTEGER},
      camp1_team_id = #{camp1TeamId,jdbcType=BIGINT},
      camp2_team_id = #{camp2TeamId,jdbcType=BIGINT},
      bo = #{bo,jdbcType=INTEGER},
      battle_mode = #{battleMode,jdbcType=INTEGER},
      cur_bo = #{curBo,jdbcType=INTEGER},
      child_id = #{childId,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
      settle_state = #{settleState,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      ready_time = #{readyTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deletePhaseGames">
    delete from aov_game where phase_id = #{phaseId}
  </delete>
</mapper>