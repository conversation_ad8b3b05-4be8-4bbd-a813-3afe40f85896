<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.aov.mapper.AovPhaseAwardMapper">
  <resultMap id="BaseResultMap" type="com.yy.manager.aov.entity.AovPhaseAward">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
    <result column="round_num" jdbcType="INTEGER" property="roundNum" />
    <result column="member_size" jdbcType="INTEGER" property="memberSize" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="package_id" jdbcType="BIGINT" property="packageId" />
    <result column="award_amount" jdbcType="BIGINT" property="awardAmount" />
    <result column="award_desc" jdbcType="VARCHAR" property="awardDesc" />
  </resultMap>
  <sql id="Base_Column_List">
    id, phase_id, round_num, member_size, task_id, package_id, award_amount, award_desc
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aov_phase_award
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectPhaseAwards" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from aov_phase_award
    where phase_id = #{phaseId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_phase_award
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.manager.aov.entity.AovPhaseAward">
    insert into aov_phase_award (id, phase_id, round_num, 
      member_size, task_id, package_id, 
      award_amount, award_desc)
    values (#{id,jdbcType=BIGINT}, #{phaseId,jdbcType=BIGINT}, #{roundNum,jdbcType=INTEGER}, 
      #{memberSize,jdbcType=INTEGER}, #{taskId,jdbcType=BIGINT}, #{packageId,jdbcType=BIGINT}, 
      #{awardAmount,jdbcType=BIGINT}, #{awardDesc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.manager.aov.entity.AovPhaseAward">
    insert into aov_phase_award
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phaseId != null">
        phase_id,
      </if>
      <if test="roundNum != null">
        round_num,
      </if>
      <if test="memberSize != null">
        member_size,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="packageId != null">
        package_id,
      </if>
      <if test="awardAmount != null">
        award_amount,
      </if>
      <if test="awardDesc != null">
        award_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="memberSize != null">
        #{memberSize,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="packageId != null">
        #{packageId,jdbcType=BIGINT},
      </if>
      <if test="awardAmount != null">
        #{awardAmount,jdbcType=BIGINT},
      </if>
      <if test="awardDesc != null">
        #{awardDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.manager.aov.entity.AovPhaseAward">
    update aov_phase_award
    <set>
      <if test="phaseId != null">
        phase_id = #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        round_num = #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="memberSize != null">
        member_size = #{memberSize,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="packageId != null">
        package_id = #{packageId,jdbcType=BIGINT},
      </if>
      <if test="awardAmount != null">
        award_amount = #{awardAmount,jdbcType=BIGINT},
      </if>
      <if test="awardDesc != null">
        award_desc = #{awardDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.manager.aov.entity.AovPhaseAward">
    update aov_phase_award
    set phase_id = #{phaseId,jdbcType=BIGINT},
      round_num = #{roundNum,jdbcType=INTEGER},
      member_size = #{memberSize,jdbcType=INTEGER},
      task_id = #{taskId,jdbcType=BIGINT},
      package_id = #{packageId,jdbcType=BIGINT},
      award_amount = #{awardAmount,jdbcType=BIGINT},
      award_desc = #{awardDesc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deletePhaseAwards">
    delete from aov_phase_award where phase_id = #{phaseId}
  </delete>
</mapper>