<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.aov.mapper.AovAdjustRecordMapper">
  <resultMap id="BaseResultMap" type="com.yy.manager.aov.entity.AovAdjustRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="adjust_data" jdbcType="LONGVARCHAR" property="adjustData" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, phase_id, uid, adjust_data, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aov_adjust_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryAdjustRecords" resultMap="BaseResultMap">
    select id, phase_id, uid, create_time
    from aov_adjust_record
    where phase_id = #{phaseId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_adjust_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.manager.aov.entity.AovAdjustRecord">
    insert into aov_adjust_record (id, phase_id, uid, 
      adjust_data, create_time)
    values (#{id,jdbcType=BIGINT}, #{phaseId,jdbcType=BIGINT}, #{uid,jdbcType=BIGINT}, 
      #{adjustData,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.manager.aov.entity.AovAdjustRecord">
    insert into aov_adjust_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phaseId != null">
        phase_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="adjustData != null">
        adjust_data,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="adjustData != null">
        #{adjustData,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.manager.aov.entity.AovAdjustRecord">
    update aov_adjust_record
    <set>
      <if test="phaseId != null">
        phase_id = #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="adjustData != null">
        adjust_data = #{adjustData,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.manager.aov.entity.AovAdjustRecord">
    update aov_adjust_record
    set phase_id = #{phaseId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      adjust_data = #{adjustData,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>