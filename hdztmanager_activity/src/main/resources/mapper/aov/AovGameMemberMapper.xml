<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.aov.mapper.AovGameMemberMapper">
  <resultMap id="BaseResultMap" type="com.yy.manager.aov.entity.AovGameMember">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="game_id" jdbcType="BIGINT" property="gameId" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="camp" jdbcType="INTEGER" property="camp" />
    <result column="seat_id" jdbcType="INTEGER" property="seatId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="join_game_time" jdbcType="TIMESTAMP" property="joinGameTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="assist_cnt" jdbcType="INTEGER" property="assistCnt" />
    <result column="dead_cnt" jdbcType="INTEGER" property="deadCnt" />
    <result column="kill_cnt" jdbcType="INTEGER" property="killCnt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, game_id, team_id, camp, seat_id, uid, join_game_time, state, assist_cnt, dead_cnt, 
    kill_cnt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aov_game_member
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="batchSelectGameMembers" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from aov_game_member
      where game_id in <foreach collection="gameIds" item="gameId" separator="," open="(" close=")">#{gameId}</foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_game_member
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.manager.aov.entity.AovGameMember">
    insert into aov_game_member (id, game_id, team_id, 
      camp, seat_id, uid, join_game_time, 
      state, assist_cnt, dead_cnt, 
      kill_cnt)
    values (#{id,jdbcType=BIGINT}, #{gameId,jdbcType=BIGINT}, #{teamId,jdbcType=BIGINT}, 
      #{camp,jdbcType=INTEGER}, #{seatId,jdbcType=INTEGER}, #{uid,jdbcType=BIGINT}, #{joinGameTime,jdbcType=TIMESTAMP}, 
      #{state,jdbcType=INTEGER}, #{assistCnt,jdbcType=INTEGER}, #{deadCnt,jdbcType=INTEGER}, 
      #{killCnt,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.manager.aov.entity.AovGameMember">
    insert into aov_game_member
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gameId != null">
        game_id,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="camp != null">
        camp,
      </if>
      <if test="seatId != null">
        seat_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="joinGameTime != null">
        join_game_time,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="assistCnt != null">
        assist_cnt,
      </if>
      <if test="deadCnt != null">
        dead_cnt,
      </if>
      <if test="killCnt != null">
        kill_cnt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gameId != null">
        #{gameId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="camp != null">
        #{camp,jdbcType=INTEGER},
      </if>
      <if test="seatId != null">
        #{seatId,jdbcType=INTEGER},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="joinGameTime != null">
        #{joinGameTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="assistCnt != null">
        #{assistCnt,jdbcType=INTEGER},
      </if>
      <if test="deadCnt != null">
        #{deadCnt,jdbcType=INTEGER},
      </if>
      <if test="killCnt != null">
        #{killCnt,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.manager.aov.entity.AovGameMember">
    update aov_game_member
    <set>
      <if test="gameId != null">
        game_id = #{gameId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="camp != null">
        camp = #{camp,jdbcType=INTEGER},
      </if>
      <if test="seatId != null">
        seat_id = #{seatId,jdbcType=INTEGER},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="joinGameTime != null">
        join_game_time = #{joinGameTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="assistCnt != null">
        assist_cnt = #{assistCnt,jdbcType=INTEGER},
      </if>
      <if test="deadCnt != null">
        dead_cnt = #{deadCnt,jdbcType=INTEGER},
      </if>
      <if test="killCnt != null">
        kill_cnt = #{killCnt,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.manager.aov.entity.AovGameMember">
    update aov_game_member
    set game_id = #{gameId,jdbcType=BIGINT},
      team_id = #{teamId,jdbcType=BIGINT},
      camp = #{camp,jdbcType=INTEGER},
      seat_id = #{seatId,jdbcType=INTEGER},
      uid = #{uid,jdbcType=BIGINT},
      join_game_time = #{joinGameTime,jdbcType=TIMESTAMP},
      state = #{state,jdbcType=INTEGER},
      assist_cnt = #{assistCnt,jdbcType=INTEGER},
      dead_cnt = #{deadCnt,jdbcType=INTEGER},
      kill_cnt = #{killCnt,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deletePhaseGameMembers">
    delete from aov_game_member where game_id in (select id from aov_game where aov_game.phase_id = #{phaseId})
  </delete>
</mapper>