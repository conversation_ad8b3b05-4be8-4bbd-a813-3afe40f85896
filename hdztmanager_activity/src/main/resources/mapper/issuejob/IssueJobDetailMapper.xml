<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.hdztmanager.mapper.IssueJobDetailDao">

    <insert id="batchInsertOrUpdate">
        INSERT INTO issue_job_detail (act_id, job_id, uid, task_id, package_id, state, resp, name, seq, cnt)
        VALUES
        <foreach item="item" collection="list" separator="),(" open="(" close=")">
            #{item.actId}, #{item.jobId}, #{item.uid}, #{item.taskId}, #{item.packageId},
            #{item.state}, #{item.resp},  #{item.name}, #{item.seq}, #{item.cnt}
        </foreach>
        ON DUPLICATE KEY UPDATE
        state = VALUES(state),
        resp = VALUES(resp)
    </insert>
</mapper>