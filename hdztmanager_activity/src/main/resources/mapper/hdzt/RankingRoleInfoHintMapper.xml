<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.hdzt.mapper.RankingRoleInfoHintMapper">

    <resultMap type="com.yy.manager.hdzt.entity.RankingRoleInfoHint" id="RankingRoleInfoHintResult">
        <result property="vId" column="concat(act_id,'-',rank_id,'-',role_id)"/>
        <result property="actId" column="act_id"/>
        <result property="rankId" column="rank_id"/>
        <result property="roleId" column="role_id"/>
        <result property="hint" column="hint"/>
        <result property="extjson" column="extjson"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <insert id="batchInsertOrUpdate">
        insert into ranking_role_info_hint
        (act_id, rank_id, role_id,
        hint, extjson, remark)
        VALUE
        <foreach item="item" collection="list" separator="),(" open="(" close=")" index="">
            #{item.actId}, #{item.rankId}, #{item.roleId},
            #{item.hint}, #{item.extjson}, #{item.remark}
        </foreach>
        ON DUPLICATE KEY UPDATE hint = VALUES(hint), extjson = VALUES(extjson), remark = VALUES(remark)
    </insert>


</mapper>
