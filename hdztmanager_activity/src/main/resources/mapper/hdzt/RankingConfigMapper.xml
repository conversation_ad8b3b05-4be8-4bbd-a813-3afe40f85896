<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.hdzt.mapper.RankingConfigDao">
    <insert id="insertBatch" parameterType="list">
        REPLACE INTO ranking_config(
        act_id,
        rank_id,
        is_show,
        phase_group_code,
        member_key,
        item_key,
        time_key_end,
        time_key_begin,
        time_key,
        limit_item,
        value_type,
        rank_name,
        rank_type,
        status,
        show_begin_time,
        show_end_time,
        utime,
        ctime,
        extjson,
        remark,
        calc_end_time,
        position,
        calc_begin_time
        ) VALUES
        <foreach item="item" collection="list" separator="),(" open="(" close=")" index="">
            #{item.actId},
            #{item.rankId},
            #{item.isShow},
            #{item.phaseGroupCode},
            #{item.memberKey},
            #{item.itemKey},
            #{item.timeKeyEnd},
            #{item.timeKeyBegin},
            #{item.timeKey},
            #{item.limitItem},
            #{item.valueType},
            #{item.rankName},
            #{item.rankType},
            #{item.status},
            #{item.showBeginTime},
            #{item.showEndTime},
            #{item.utime},
            #{item.ctime},
            #{item.extjson},
            #{item.remark},
            #{item.calcEndTime},
            #{item.position},
            #{item.calcBeginTime}
        </foreach>
    </insert>
    <insert id="batchInsertOrUpdate">
        insert into ranking_config(act_id, rank_id, rank_name, rank_name_show,
        rank_type, status, calc_begin_time, calc_end_time,
        value_type, limit_item, time_key, time_key_begin, time_key_end, item_key,
        member_key, phase_group_code, skip_rank_update, skip_phase_update,
        act_group, is_show, show_begin_time, show_end_time,
        position, remark, extjson, ctime, utime)
        VALUE
        <foreach item="item" collection="list" separator="),(" open="(" close=")" index="">
            #{item.actId}, #{item.rankId},#{item.rankName},#{item.rankNameShow},
            #{item.rankType},#{item.status}, #{item.calcBeginTime},#{item.calcEndTime},
            #{item.valueType},#{item.limitItem}, #{item.timeKey}, #{item.timeKeyBegin}, #{item.timeKeyEnd},
            #{item.itemKey},
            #{item.memberKey}, #{item.phaseGroupCode},#{item.skipRankUpdate},#{item.skipPhaseUpdate},
            #{item.actGroup},#{item.isShow},#{item.showBeginTime},#{item.showEndTime},
            #{item.position},#{item.remark}, #{item.extjson}, #{item.ctime},#{item.utime}
        </foreach>
        ON DUPLICATE KEY UPDATE
        rank_name = VALUES(rank_name), rank_name_show = VALUES(rank_name_show),rank_type = VALUES(rank_type),
        status = VALUES(status) , calc_begin_time = VALUES(calc_begin_time),calc_end_time = VALUES(calc_end_time),
        value_type = VALUES(value_type), limit_item = VALUES(limit_item) , time_key = VALUES(time_key),
        time_key_begin = VALUES(time_key_begin),time_key_end = VALUES(time_key_end), item_key = VALUES(item_key),
        member_key = VALUES(member_key),phase_group_code = VALUES(phase_group_code),
        skip_rank_update = VALUES(skip_rank_update), skip_phase_update = VALUES(skip_phase_update),
        act_group = VALUES(act_group),is_show = VALUES(is_show),
        show_begin_time = VALUES(show_begin_time), show_end_time = VALUES(show_end_time),
        position = VALUES(position),extjson = VALUES(extjson), remark = VALUES(remark) , utime = VALUES(utime)
    </insert>
    <insert id="batchInsertOrUpdateNoName">
        insert into ranking_config(act_id, rank_id, rank_name, rank_name_show,
        rank_type, status, calc_begin_time, calc_end_time,
        value_type, limit_item, time_key, time_key_begin, time_key_end, item_key,
        member_key, phase_group_code, skip_rank_update, skip_phase_update,
        act_group, is_show, show_begin_time, show_end_time,
        position, remark, extjson, ctime, utime)
        VALUE
        <foreach item="item" collection="list" separator="),(" open="(" close=")" index="">
            #{item.actId}, #{item.rankId},#{item.rankName},#{item.rankNameShow},
            #{item.rankType},#{item.status}, #{item.calcBeginTime},#{item.calcEndTime},
            #{item.valueType},#{item.limitItem}, #{item.timeKey}, #{item.timeKeyBegin}, #{item.timeKeyEnd},
            #{item.itemKey},
            #{item.memberKey}, #{item.phaseGroupCode},#{item.skipRankUpdate},#{item.skipPhaseUpdate},
            #{item.actGroup},#{item.isShow},#{item.showBeginTime},#{item.showEndTime},
            #{item.position},#{item.remark}, #{item.extjson}, #{item.ctime},#{item.utime}
        </foreach>
        ON DUPLICATE KEY UPDATE
        rank_type = VALUES(rank_type),
        status = VALUES(status) , calc_begin_time = VALUES(calc_begin_time),calc_end_time = VALUES(calc_end_time),
        value_type = VALUES(value_type), limit_item = VALUES(limit_item) , time_key = VALUES(time_key),
        time_key_begin = VALUES(time_key_begin),time_key_end = VALUES(time_key_end), item_key = VALUES(item_key),
        member_key = VALUES(member_key),phase_group_code = VALUES(phase_group_code),
        skip_rank_update = VALUES(skip_rank_update), skip_phase_update = VALUES(skip_phase_update),
        act_group = VALUES(act_group),is_show = VALUES(is_show),
        show_begin_time = VALUES(show_begin_time), show_end_time = VALUES(show_end_time),
        position = VALUES(position),extjson = VALUES(extjson), remark = VALUES(remark) , utime = VALUES(utime)


    </insert>
</mapper>
