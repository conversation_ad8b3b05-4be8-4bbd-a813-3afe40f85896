<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.backup.mapper.BackupActivityConfigMapper">
    <resultMap id="BaseResultMap" type="com.yy.manager.backup.entity.BackupActivityConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="act_id" jdbcType="BIGINT" property="actId"/>
        <result column="backup_type" jdbcType="VARCHAR" property="backupType"/>
        <result column="uri" jdbcType="INTEGER" property="uri"/>
        <result column="rank_id" jdbcType="INTEGER" property="rankId"/>
        <result column="phase_id" jdbcType="INTEGER" property="phaseId"/>
        <result column="backup_time" jdbcType="VARCHAR" property="backupTime"/>
        <result column="backup_source" jdbcType="VARCHAR" property="backupSource"/>
        <result column="key_patterns" jdbcType="VARCHAR" property="keyPatterns"/>
        <result column="databases" jdbcType="VARCHAR" property="databases"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, act_id, backup_type, uri, rank_id, phase_id, backup_time, backup_source, key_patterns, `databases`, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from backup_activity_config
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByActMessage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from backup_activity_config
        where act_id = #{actId} and uri = #{uri} and backup_type = '${@<EMAIL>()}'
        <if test="rankId != null and rankId != 0">
            and rank_id = #{rankId}
        </if>
        <if test="phaseId != null and phaseId != 0">
            and phase_id = #{phaseId}
        </if>
    </select>
    <select id="selectByTimerConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from backup_activity_config
        where act_id = #{actId} and backup_time = #{backupTime} and backup_type = '${@<EMAIL>()}'
    </select>
    <select id="selectBackupConfigList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from backup_activity_config
        <where>
            <if test="actId != null">
                act_id = #{actId}
            </if>
            <if test="backType != null">
                and backup_type = #{backType}
            </if>
            <if test="backupSource != null">
                and backup_source = #{backupSource}
            </if>
        </where>
        order by act_id desc
        limit #{offset}, #{length}
    </select>
    <select id="selectBackupConfigCount" resultType="java.lang.Integer">
        select count(1) from backup_activity_config
        <where>
            <if test="actId != null">
                act_id = #{actId}
            </if>
            <if test="backType != null">
                and backup_type = #{backType}
            </if>
            <if test="backupSource != null">
                and backup_source = #{backupSource}
            </if>
        </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from backup_activity_config
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yy.manager.backup.entity.BackupActivityConfig">
        insert into backup_activity_config (id, act_id, backup_type, uri,
                                            rank_id, phase_id, backup_time,
                                            backup_source, key_patterns, `databases`,
                                            create_time, update_time)
        values (#{id,jdbcType=BIGINT}, #{actId,jdbcType=BIGINT}, #{backupType,jdbcType=VARCHAR}, #{uri,jdbcType=INTEGER},
                #{rankId,jdbcType=INTEGER}, #{phaseId,jdbcType=INTEGER}, #{backupTime,jdbcType=VARCHAR},
                #{backupSource,jdbcType=VARCHAR}, #{keyPatterns,jdbcType=VARCHAR}, #{databases,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.yy.manager.backup.entity.BackupActivityConfig">
        insert into backup_activity_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="actId != null">
                act_id,
            </if>
            <if test="backupType != null">
                backup_type,
            </if>
            <if test="uri != null">
                uri,
            </if>
            <if test="rankId != null">
                rank_id,
            </if>
            <if test="phaseId != null">
                phase_id,
            </if>
            <if test="backupTime != null">
                backup_time,
            </if>
            <if test="backupSource != null">
                backup_source,
            </if>
            <if test="keyPatterns != null">
                key_patterns,
            </if>
            <if test="databases">
                `databases`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="actId != null">
                #{actId,jdbcType=BIGINT},
            </if>
            <if test="backupType != null">
                #{backupType,jdbcType=VARCHAR},
            </if>
            <if test="uri != null">
                #{uri,jdbcType=INTEGER},
            </if>
            <if test="rankId != null">
                #{rankId,jdbcType=INTEGER},
            </if>
            <if test="phaseId != null">
                #{phaseId,jdbcType=INTEGER},
            </if>
            <if test="backupTime != null">
                #{backupTime,jdbcType=VARCHAR},
            </if>
            <if test="backupSource != null">
                #{backupSource,jdbcType=VARCHAR},
            </if>
            <if test="keyPatterns != null">
                #{keyPatterns,jdbcType=VARCHAR},
            </if>
            <if test="databases">
                #{databases,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yy.manager.backup.entity.BackupActivityConfig">
        update backup_activity_config
        <set>
            <if test="actId != null">
                act_id = #{actId,jdbcType=BIGINT},
            </if>
            <if test="backupType != null">
                backup_type = #{backupType,jdbcType=VARCHAR},
            </if>
            <if test="uri != null">
                uri = #{uri,jdbcType=INTEGER},
            </if>
            <if test="rankId != null">
                rank_id = #{rankId,jdbcType=INTEGER},
            </if>
            <if test="phaseId != null">
                phase_id = #{phaseId,jdbcType=INTEGER},
            </if>
            <if test="backupTime != null">
                backup_time = #{backupTime,jdbcType=VARCHAR},
            </if>
            <if test="backupSource != null">
                backup_source = #{backupSource,jdbcType=VARCHAR},
            </if>
            <if test="keyPatterns != null">
                key_patterns = #{keyPatterns,jdbcType=VARCHAR},
            </if>
            <if test="databases">
                `databases` = #{databases,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yy.manager.backup.entity.BackupActivityConfig">
        update backup_activity_config
        set act_id        = #{actId,jdbcType=BIGINT},
            backup_type   = #{backupType,jdbcType=VARCHAR},
            uri           = #{uri,jdbcType=INTEGER},
            rank_id       = #{rankId,jdbcType=INTEGER},
            phase_id      = #{phaseId,jdbcType=INTEGER},
            backup_time   = #{backupTime,jdbcType=VARCHAR},
            backup_source = #{backupSource,jdbcType=VARCHAR},
            key_patterns  = #{keyPatterns,jdbcType=VARCHAR},
            `databases`     = #{databases,jdbcType=VARCHAR},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>