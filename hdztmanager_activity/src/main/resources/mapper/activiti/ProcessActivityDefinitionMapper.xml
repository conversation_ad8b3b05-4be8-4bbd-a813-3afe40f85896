<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.activiti.mapper.ProcessActivityDefinitionMapper">

    <select id="selectByProcIdAndActivityId"
            resultType="com.yy.manager.activiti.entity.ProcessActivityDefinition">
        select * from process_activity_definition where proc_id = #{procId} and activity_id = #{activityId}
    </select>
</mapper>