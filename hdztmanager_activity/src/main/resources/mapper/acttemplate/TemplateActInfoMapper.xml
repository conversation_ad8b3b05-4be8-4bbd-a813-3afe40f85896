<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.acttemplate.mapper.TemplateActInfoMapper">
  <resultMap id="BaseResultMap" type="com.yy.manager.acttemplate.entity.TemplateActInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 15 16:23:26 CST 2024.
    -->
    <id column="act_id" jdbcType="VARCHAR" property="actId" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="act_name" jdbcType="VARCHAR" property="actName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="phase_time_config" jdbcType="VARCHAR" property="phaseTimeConfig" />
    <result column="act_gift_info" jdbcType="VARCHAR" property="actGiftInfo" />
    <result column="extjson" jdbcType="VARCHAR" property="extjson" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
    <result column="puzzle_act_id" jdbcType="BIGINT" property="puzzleActId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 15 16:23:26 CST 2024.
    -->
    act_id, template_id, act_name, status, begin_time, end_time, phase_time_config, act_gift_info, 
    extjson, ctime, utime, puzzle_act_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 15 16:23:26 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from template_act_info
    where act_id = #{actId,jdbcType=VARCHAR}
  </select>
    <select id="selectTemplateActInfos" resultMap="BaseResultMap">
      select <include refid="Base_Column_List" /> from template_act_info
      <where>
        <if test="status != null">
          `status` = #{status}
        </if>
        <if test="templateId != null and templateId != ''">
          and template_id = #{templateId}
        </if>
        <if test="beginTime != null">
          and begin_time &gt; #{beginTime}
        </if>
        <if test="endTime != null">
          and begin_time &lt; #{endTime}
        </if>
      </where>
    </select>
  <select id="selectByPuzzleActId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from template_act_info
    where puzzle_act_id = #{puzzleActId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 15 16:23:26 CST 2024.
    -->
    delete from template_act_info
    where act_id = #{actId,jdbcType=VARCHAR}
  </delete>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.manager.acttemplate.entity.TemplateActInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 15 16:23:26 CST 2024.
    -->
    update template_act_info
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="actName != null">
        act_name = #{actName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="phaseTimeConfig != null">
        phase_time_config = #{phaseTimeConfig,jdbcType=VARCHAR},
      </if>
      <if test="actGiftInfo != null">
        act_gift_info = #{actGiftInfo,jdbcType=VARCHAR},
      </if>
      <if test="extjson != null">
        extjson = #{extjson,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="utime != null">
        utime = #{utime,jdbcType=TIMESTAMP},
      </if>
      <if test="puzzleActId != null">
        puzzle_act_id = #{puzzleActId,jdbcType=BIGINT},
      </if>
    </set>
    where act_id = #{actId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.manager.acttemplate.entity.TemplateActInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 15 16:23:26 CST 2024.
    -->
    update template_act_info
    set template_id = #{templateId,jdbcType=VARCHAR},
      act_name = #{actName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      phase_time_config = #{phaseTimeConfig,jdbcType=VARCHAR},
      act_gift_info = #{actGiftInfo,jdbcType=VARCHAR},
      extjson = #{extjson,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      utime = #{utime,jdbcType=TIMESTAMP},
      puzzle_act_id = #{puzzleActId,jdbcType=BIGINT}
    where act_id = #{actId,jdbcType=VARCHAR}
  </update>
</mapper>