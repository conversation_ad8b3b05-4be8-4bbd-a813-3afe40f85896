<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.template.mapper.TemplateAttrDefineMapper">
    <update id="updateByPrimaryKeySelective">
        update template_attr_template
        <set>
            <if test="attrType != null">
                attr_type = #{attrType},
            </if>
            <if test="attrName != null">
                attr_name = #{attrName},
            </if>
            <if test="attrDesc != null">
                attr_desc = #{attrDesc},
            </if>
            <if test="required != null">
                required = #{required},
            </if>
            <if test="weight != null">
                weight = #{weight},
            </if>
        </set>
        where template_id = #{templateId} and db = #{db} and tbl = #{tbl} and col = #{col} and row_index = #{rowIndex}
    </update>
    <delete id="deleteByPrimaryKey">
        delete from template_attr_define where template_id = #{templateId} and db = #{db} and tbl = #{tbl} and col = #{col} and row_index = #{rowIndex}
    </delete>

    <select id="selectTemplateAttrDefines" resultType="com.yy.manager.template.entity.TemplateAttrDefine">
        select * from hdpt_template.template_attr_define where template_id = #{templateId} order by weight asc, db asc, tbl asc, col asc, row_index asc
    </select>
    <select id="selectActAttrs" resultType="com.yy.manager.template.vo.TemplateAttr">
        select define.db, define.tbl, define.col, define.row_index, define.attr_type, define.attr_name, define.attr_desc, define.required, attr.attr_value
        from hdpt_template.template_attr_define define
        left join hdpt_template.template_attr_value attr on attr.act_id = #{actId} and attr.db = define.db
            and attr.tbl = define.tbl and attr.col = define.col and attr.row_index = define.row_index
        where define.template_id = #{templateId}
        order by define.weight asc
    </select>
    <select id="selectByPrimaryKey" resultType="com.yy.manager.template.entity.TemplateAttrDefine">
        select * from template_attr_define where template_id = #{templateId} and db = #{db} and tbl = #{tbl} and col = #{col} and row_index = #{rowIndex}
    </select>
</mapper>