<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.hdztmanager.mapper.RoleAdjustRecordDao">
<update id="insertOrUpdate" parameterType="com.yy.manager.hdztmanager.entity.RoleAdjustRecord">
     replace into hdzt_manager.role_adjust_record(record, executed, executed_result, act_id, member_id , src_role,dest_role,c_time)
            values (#{record},#{executed},#{executedResult},#{actId}, #{memberId},#{srcRole},#{destRole}, now())
</update>
</mapper>
