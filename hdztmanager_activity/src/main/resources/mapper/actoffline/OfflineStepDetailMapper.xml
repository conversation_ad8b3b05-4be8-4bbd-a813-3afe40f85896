<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.manager.actoffline.mapper.OfflineStepDetailMapper">
    <insert id="batchInsert">
        insert ignore into offline_step_detail (act_id, step_id, step, state) values
        <foreach collection="details" item="detail" separator=",">
            (#{detail.actId}, #{detail.stepId}, #{detail.step}, #{detail.state})
        </foreach>
    </insert>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 17 12:01:06 CST 2023.
    -->
    delete from offline_step_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
</mapper>