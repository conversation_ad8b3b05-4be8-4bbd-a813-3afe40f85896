package com.yy.manager.kafka;

import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.backoff.FixedBackOff;

@Configuration
public class KafkaConfiguration {

    @Bean
    @Primary
    @ConfigurationProperties("spring.kafka.hdzt-wx")
    public KafkaProperties hdztWxKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public ConsumerFactory<String, String> hdztWxConsumerFactory(KafkaProperties hdztWxKafkaProperties) {
        return new DefaultKafkaConsumerFactory<>(hdztWxKafkaProperties.buildConsumerProperties());
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> hdztWxContainerFactory(ConsumerFactory<String, String> hdztWxConsumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(hdztWxConsumerFactory);
        factory.setConcurrency(2);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new FixedBackOff(3000L, 3)));
        return factory;
    }

    @Bean
    @ConfigurationProperties("spring.kafka.hdzt-sz")
    public KafkaProperties hdztSzKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public ConsumerFactory<String, String> hdztSzConsumerFactory(KafkaProperties hdztSzKafkaProperties) {
        return new DefaultKafkaConsumerFactory<>(hdztSzKafkaProperties.buildConsumerProperties());
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> hdztSzContainerFactory(ConsumerFactory<String, String> hdztSzConsumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(hdztSzConsumerFactory);
        factory.setConcurrency(2);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new FixedBackOff(3000L, 3)));
        return factory;
    }

    @Bean
    @ConfigurationProperties("spring.kafka.hdzt7-wx")
    public KafkaProperties hdzt7WxKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public ConsumerFactory<String, String> hdzt7WxConsumerFactory(KafkaProperties hdzt7WxKafkaProperties) {
        return new DefaultKafkaConsumerFactory<>(hdzt7WxKafkaProperties.buildConsumerProperties());
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> hdzt7WxContainerFactory(ConsumerFactory<String, String> hdzt7WxConsumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(hdzt7WxConsumerFactory);
        factory.setConcurrency(3);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new FixedBackOff(3000L, 3)));
        return factory;
    }

    @Bean
    @ConfigurationProperties("spring.kafka.hdzt7-sz")
    public KafkaProperties hdzt7SzKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public ConsumerFactory<String, String> hdzt7SzConsumerFactory(KafkaProperties hdzt7SzKafkaProperties) {
        return new DefaultKafkaConsumerFactory<>(hdzt7SzKafkaProperties.buildConsumerProperties());
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> hdzt7SzContainerFactory(ConsumerFactory<String, String> hdzt7SzConsumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(hdzt7SzConsumerFactory);
        factory.setConcurrency(3);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new FixedBackOff(3000L, 3)));
        return factory;
    }

    @Bean
    @ConfigurationProperties("spring.kafka.puzzle")
    public KafkaProperties puzzleKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public ConsumerFactory<String, String> puzzleConsumerFactory(KafkaProperties puzzleKafkaProperties) {
        return new DefaultKafkaConsumerFactory<>(puzzleKafkaProperties.buildConsumerProperties());
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> puzzleContainerFactory(ConsumerFactory<String, String> puzzleConsumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(puzzleConsumerFactory);
        factory.setConcurrency(3);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new FixedBackOff(3000L, 3)));
        return factory;
    }
}
