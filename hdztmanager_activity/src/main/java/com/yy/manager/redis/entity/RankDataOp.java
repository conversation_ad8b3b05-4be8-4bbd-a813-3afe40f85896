package com.yy.manager.redis.entity;

/**
 * <AUTHOR>
 * @date 2021.11.26 14:37
 */
public class RankDataOp {
    /**
     * redis 命令
     */
    private String command;

    private long actId;

    private int rankId;

    private int phaseId;

    private long score;

    private String dateStr;

    private String memberId;

    private int redisGroup;

    /**
     * 被贡献者
     */
    private String srcMember;

    /**
     * 是否是晋级榜单
     */
    private String promote;

    /**
     * 命令类型, 榜单类型,其他  默认榜单
     * rank,
     */
    private String type = "rank";

    public String getPromote() {
        return promote;
    }

    public void setPromote(String promote) {
        this.promote = promote;
    }

    public String getSrcMember() {
        return srcMember;
    }

    public void setSrcMember(String srcMember) {
        this.srcMember = srcMember;
    }

    public int getRedisGroup() {
        return redisGroup;
    }

    public void setRedisGroup(int redisGroup) {
        this.redisGroup = redisGroup;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public int getRankId() {
        return rankId;
    }

    public void setRankId(int rankId) {
        this.rankId = rankId;
    }

    public int getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(int phaseId) {
        this.phaseId = phaseId;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
