namespace java com.yy.hdzt.common.thrift.hdzt.award.gamebaby

struct GamebabySimpleResult
{
	1:i32 code, // 结果码，0-成功，非零失败
	2:string reason, // 成功或失败的提示信息
}

service GamebabyAwardService
{
    /**
     * 测试接口
     */
    void ping();

	/* 任务活动发道具奖励接口（支持抽红心、抽活动道具）
     * tid - 任务标识
     * yyuid - 用户的yyuid（道具发放对象）
     * itemId - 道具标识
     * itemNum - 道具发放数量
     * time - 时间戳，精确到毫秒，用来做请求时间偏差检查
     * sign - 签名串,算法=MD5(tid+yyuid+itemId+itemNum+time+busiNum+${key})， ${key} 由双方约定 - 增加对 busiNum 的签名保护
     * busiNum - 目前传递 业务流水(由于后面3个参数类型相同且实际未用，这里调整顺序和名字，不会影响老代码)，by guoliping
     * ip - 用户ip，非必要信息，跟踪统计用
     * extdat - json 结构的额外数据，可空，非空时，当前含2字段 {sid:12343, ssid:132344, awdtyp:1}, sid-抽奖发生的频道，ssid-抽奖发生的子频道, awdtyp-抽奖类型（当前含：0-页面抽奖，1-领任务中奖，2-交任务中奖）
     * 返回是 SimpleResult
     */
      GamebabySimpleResult award4task(1:i32 tid, 2:i64 yyuid, 3:string itemId, 4:i32 itemNum, 5:i64 time, 6:string sign, 7:string busiNum, 8:string ip, 9:string extdat);
}