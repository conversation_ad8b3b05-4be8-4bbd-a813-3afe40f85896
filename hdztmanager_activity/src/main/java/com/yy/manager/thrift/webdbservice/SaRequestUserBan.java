/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 查询某个频道内被封禁的用户信息(分页)
 * @param     appkey          客户端的标识
 * @param     sid             频道id
 * @param     date_from       被封起始时间，格式为"2018-09-13 09:08:20",如果为空则忽略此参数
 * @param     date_to         被封最晚时间，格式为"2018-09-14 21:10:00",如果为空则忽略此参数
 * @param     reason          被封原因，可选值为：
 *                              0 - 所有原因
 *                              1 - 恶意刷屏
 *                              2 - 谩骂
 *                              3 - 刷广告
 *                              4 - 其他(除上面1,2,3三个原因外所有其他原因)
 * @param     uid             被封用户id，如果为VALUE_NULL则忽略此参数
 * @param     columns         要查询的字段，可选值为
 *                              - op_date, 被封时间
 *                              - uid_, 被封用户id
 *                              - admin_uid, 操作者(封人者)id
 *                              - sid, 顶级频道id
 *                              - reason, 被封原因
 * @param     page            要查询的页码，以0为起始页码，不能为负数
 * @param     per_page        每页返回的记录数，不能小于1，不能大于500，
 *                            相关的多次查询里每次分页查询此值应该保持不变
 * @param     order_by        指定排序字段，可选值为
 *                              - op_date,
 *                              - uid,
 *                              - admin_uid
 * @param     descending      指定排序字段是否按降序排序
 *                              1 - 按降序排序
 * @SaResponseSet             封禁的用户信息结果集，如果没有查到结果则dataSet为空
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class SaRequestUserBan implements org.apache.thrift.TBase<SaRequestUserBan, SaRequestUserBan._Fields>, java.io.Serializable, Cloneable, Comparable<SaRequestUserBan> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaRequestUserBan");

  private static final org.apache.thrift.protocol.TField AUTH_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authMsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPKEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appkey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField DATE_FROM_FIELD_DESC = new org.apache.thrift.protocol.TField("date_from", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField DATE_TO_FIELD_DESC = new org.apache.thrift.protocol.TField("date_to", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField REASON_FIELD_DESC = new org.apache.thrift.protocol.TField("reason", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField COLUMNS_FIELD_DESC = new org.apache.thrift.protocol.TField("columns", org.apache.thrift.protocol.TType.LIST, (short)8);
  private static final org.apache.thrift.protocol.TField PAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("page", org.apache.thrift.protocol.TType.I32, (short)9);
  private static final org.apache.thrift.protocol.TField PER_PAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("per_page", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField ORDER_BY_FIELD_DESC = new org.apache.thrift.protocol.TField("order_by", org.apache.thrift.protocol.TType.STRING, (short)11);
  private static final org.apache.thrift.protocol.TField DESCENDING_FIELD_DESC = new org.apache.thrift.protocol.TField("descending", org.apache.thrift.protocol.TType.BOOL, (short)12);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaRequestUserBanStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaRequestUserBanTupleSchemeFactory());
  }

  public AuthorizeMsg authMsg; // required
  public String appkey; // required
  public String sid; // required
  public String date_from; // required
  public String date_to; // required
  public int reason; // required
  public String uid; // required
  public List<String> columns; // required
  public int page; // required
  public int per_page; // required
  public String order_by; // required
  public boolean descending; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_MSG((short)1, "authMsg"),
    APPKEY((short)2, "appkey"),
    SID((short)3, "sid"),
    DATE_FROM((short)4, "date_from"),
    DATE_TO((short)5, "date_to"),
    REASON((short)6, "reason"),
    UID((short)7, "uid"),
    COLUMNS((short)8, "columns"),
    PAGE((short)9, "page"),
    PER_PAGE((short)10, "per_page"),
    ORDER_BY((short)11, "order_by"),
    DESCENDING((short)12, "descending");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_MSG
          return AUTH_MSG;
        case 2: // APPKEY
          return APPKEY;
        case 3: // SID
          return SID;
        case 4: // DATE_FROM
          return DATE_FROM;
        case 5: // DATE_TO
          return DATE_TO;
        case 6: // REASON
          return REASON;
        case 7: // UID
          return UID;
        case 8: // COLUMNS
          return COLUMNS;
        case 9: // PAGE
          return PAGE;
        case 10: // PER_PAGE
          return PER_PAGE;
        case 11: // ORDER_BY
          return ORDER_BY;
        case 12: // DESCENDING
          return DESCENDING;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __REASON_ISSET_ID = 0;
  private static final int __PAGE_ISSET_ID = 1;
  private static final int __PER_PAGE_ISSET_ID = 2;
  private static final int __DESCENDING_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_MSG, new org.apache.thrift.meta_data.FieldMetaData("authMsg", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPKEY, new org.apache.thrift.meta_data.FieldMetaData("appkey", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DATE_FROM, new org.apache.thrift.meta_data.FieldMetaData("date_from", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DATE_TO, new org.apache.thrift.meta_data.FieldMetaData("date_to", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REASON, new org.apache.thrift.meta_data.FieldMetaData("reason", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COLUMNS, new org.apache.thrift.meta_data.FieldMetaData("columns", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.PAGE, new org.apache.thrift.meta_data.FieldMetaData("page", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PER_PAGE, new org.apache.thrift.meta_data.FieldMetaData("per_page", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ORDER_BY, new org.apache.thrift.meta_data.FieldMetaData("order_by", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DESCENDING, new org.apache.thrift.meta_data.FieldMetaData("descending", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaRequestUserBan.class, metaDataMap);
  }

  public SaRequestUserBan() {
  }

  public SaRequestUserBan(
    AuthorizeMsg authMsg,
    String appkey,
    String sid,
    String date_from,
    String date_to,
    int reason,
    String uid,
    List<String> columns,
    int page,
    int per_page,
    String order_by,
    boolean descending)
  {
    this();
    this.authMsg = authMsg;
    this.appkey = appkey;
    this.sid = sid;
    this.date_from = date_from;
    this.date_to = date_to;
    this.reason = reason;
    setReasonIsSet(true);
    this.uid = uid;
    this.columns = columns;
    this.page = page;
    setPageIsSet(true);
    this.per_page = per_page;
    setPer_pageIsSet(true);
    this.order_by = order_by;
    this.descending = descending;
    setDescendingIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaRequestUserBan(SaRequestUserBan other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAuthMsg()) {
      this.authMsg = new AuthorizeMsg(other.authMsg);
    }
    if (other.isSetAppkey()) {
      this.appkey = other.appkey;
    }
    if (other.isSetSid()) {
      this.sid = other.sid;
    }
    if (other.isSetDate_from()) {
      this.date_from = other.date_from;
    }
    if (other.isSetDate_to()) {
      this.date_to = other.date_to;
    }
    this.reason = other.reason;
    if (other.isSetUid()) {
      this.uid = other.uid;
    }
    if (other.isSetColumns()) {
      List<String> __this__columns = new ArrayList<String>(other.columns);
      this.columns = __this__columns;
    }
    this.page = other.page;
    this.per_page = other.per_page;
    if (other.isSetOrder_by()) {
      this.order_by = other.order_by;
    }
    this.descending = other.descending;
  }

  public SaRequestUserBan deepCopy() {
    return new SaRequestUserBan(this);
  }

  @Override
  public void clear() {
    this.authMsg = null;
    this.appkey = null;
    this.sid = null;
    this.date_from = null;
    this.date_to = null;
    setReasonIsSet(false);
    this.reason = 0;
    this.uid = null;
    this.columns = null;
    setPageIsSet(false);
    this.page = 0;
    setPer_pageIsSet(false);
    this.per_page = 0;
    this.order_by = null;
    setDescendingIsSet(false);
    this.descending = false;
  }

  public AuthorizeMsg getAuthMsg() {
    return this.authMsg;
  }

  public SaRequestUserBan setAuthMsg(AuthorizeMsg authMsg) {
    this.authMsg = authMsg;
    return this;
  }

  public void unsetAuthMsg() {
    this.authMsg = null;
  }

  /** Returns true if field authMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthMsg() {
    return this.authMsg != null;
  }

  public void setAuthMsgIsSet(boolean value) {
    if (!value) {
      this.authMsg = null;
    }
  }

  public String getAppkey() {
    return this.appkey;
  }

  public SaRequestUserBan setAppkey(String appkey) {
    this.appkey = appkey;
    return this;
  }

  public void unsetAppkey() {
    this.appkey = null;
  }

  /** Returns true if field appkey is set (has been assigned a value) and false otherwise */
  public boolean isSetAppkey() {
    return this.appkey != null;
  }

  public void setAppkeyIsSet(boolean value) {
    if (!value) {
      this.appkey = null;
    }
  }

  public String getSid() {
    return this.sid;
  }

  public SaRequestUserBan setSid(String sid) {
    this.sid = sid;
    return this;
  }

  public void unsetSid() {
    this.sid = null;
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return this.sid != null;
  }

  public void setSidIsSet(boolean value) {
    if (!value) {
      this.sid = null;
    }
  }

  public String getDate_from() {
    return this.date_from;
  }

  public SaRequestUserBan setDate_from(String date_from) {
    this.date_from = date_from;
    return this;
  }

  public void unsetDate_from() {
    this.date_from = null;
  }

  /** Returns true if field date_from is set (has been assigned a value) and false otherwise */
  public boolean isSetDate_from() {
    return this.date_from != null;
  }

  public void setDate_fromIsSet(boolean value) {
    if (!value) {
      this.date_from = null;
    }
  }

  public String getDate_to() {
    return this.date_to;
  }

  public SaRequestUserBan setDate_to(String date_to) {
    this.date_to = date_to;
    return this;
  }

  public void unsetDate_to() {
    this.date_to = null;
  }

  /** Returns true if field date_to is set (has been assigned a value) and false otherwise */
  public boolean isSetDate_to() {
    return this.date_to != null;
  }

  public void setDate_toIsSet(boolean value) {
    if (!value) {
      this.date_to = null;
    }
  }

  public int getReason() {
    return this.reason;
  }

  public SaRequestUserBan setReason(int reason) {
    this.reason = reason;
    setReasonIsSet(true);
    return this;
  }

  public void unsetReason() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REASON_ISSET_ID);
  }

  /** Returns true if field reason is set (has been assigned a value) and false otherwise */
  public boolean isSetReason() {
    return EncodingUtils.testBit(__isset_bitfield, __REASON_ISSET_ID);
  }

  public void setReasonIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REASON_ISSET_ID, value);
  }

  public String getUid() {
    return this.uid;
  }

  public SaRequestUserBan setUid(String uid) {
    this.uid = uid;
    return this;
  }

  public void unsetUid() {
    this.uid = null;
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return this.uid != null;
  }

  public void setUidIsSet(boolean value) {
    if (!value) {
      this.uid = null;
    }
  }

  public int getColumnsSize() {
    return (this.columns == null) ? 0 : this.columns.size();
  }

  public java.util.Iterator<String> getColumnsIterator() {
    return (this.columns == null) ? null : this.columns.iterator();
  }

  public void addToColumns(String elem) {
    if (this.columns == null) {
      this.columns = new ArrayList<String>();
    }
    this.columns.add(elem);
  }

  public List<String> getColumns() {
    return this.columns;
  }

  public SaRequestUserBan setColumns(List<String> columns) {
    this.columns = columns;
    return this;
  }

  public void unsetColumns() {
    this.columns = null;
  }

  /** Returns true if field columns is set (has been assigned a value) and false otherwise */
  public boolean isSetColumns() {
    return this.columns != null;
  }

  public void setColumnsIsSet(boolean value) {
    if (!value) {
      this.columns = null;
    }
  }

  public int getPage() {
    return this.page;
  }

  public SaRequestUserBan setPage(int page) {
    this.page = page;
    setPageIsSet(true);
    return this;
  }

  public void unsetPage() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  /** Returns true if field page is set (has been assigned a value) and false otherwise */
  public boolean isSetPage() {
    return EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  public void setPageIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
  }

  public int getPer_page() {
    return this.per_page;
  }

  public SaRequestUserBan setPer_page(int per_page) {
    this.per_page = per_page;
    setPer_pageIsSet(true);
    return this;
  }

  public void unsetPer_page() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PER_PAGE_ISSET_ID);
  }

  /** Returns true if field per_page is set (has been assigned a value) and false otherwise */
  public boolean isSetPer_page() {
    return EncodingUtils.testBit(__isset_bitfield, __PER_PAGE_ISSET_ID);
  }

  public void setPer_pageIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PER_PAGE_ISSET_ID, value);
  }

  public String getOrder_by() {
    return this.order_by;
  }

  public SaRequestUserBan setOrder_by(String order_by) {
    this.order_by = order_by;
    return this;
  }

  public void unsetOrder_by() {
    this.order_by = null;
  }

  /** Returns true if field order_by is set (has been assigned a value) and false otherwise */
  public boolean isSetOrder_by() {
    return this.order_by != null;
  }

  public void setOrder_byIsSet(boolean value) {
    if (!value) {
      this.order_by = null;
    }
  }

  public boolean isDescending() {
    return this.descending;
  }

  public SaRequestUserBan setDescending(boolean descending) {
    this.descending = descending;
    setDescendingIsSet(true);
    return this;
  }

  public void unsetDescending() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __DESCENDING_ISSET_ID);
  }

  /** Returns true if field descending is set (has been assigned a value) and false otherwise */
  public boolean isSetDescending() {
    return EncodingUtils.testBit(__isset_bitfield, __DESCENDING_ISSET_ID);
  }

  public void setDescendingIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __DESCENDING_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_MSG:
      if (value == null) {
        unsetAuthMsg();
      } else {
        setAuthMsg((AuthorizeMsg)value);
      }
      break;

    case APPKEY:
      if (value == null) {
        unsetAppkey();
      } else {
        setAppkey((String)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((String)value);
      }
      break;

    case DATE_FROM:
      if (value == null) {
        unsetDate_from();
      } else {
        setDate_from((String)value);
      }
      break;

    case DATE_TO:
      if (value == null) {
        unsetDate_to();
      } else {
        setDate_to((String)value);
      }
      break;

    case REASON:
      if (value == null) {
        unsetReason();
      } else {
        setReason((Integer)value);
      }
      break;

    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((String)value);
      }
      break;

    case COLUMNS:
      if (value == null) {
        unsetColumns();
      } else {
        setColumns((List<String>)value);
      }
      break;

    case PAGE:
      if (value == null) {
        unsetPage();
      } else {
        setPage((Integer)value);
      }
      break;

    case PER_PAGE:
      if (value == null) {
        unsetPer_page();
      } else {
        setPer_page((Integer)value);
      }
      break;

    case ORDER_BY:
      if (value == null) {
        unsetOrder_by();
      } else {
        setOrder_by((String)value);
      }
      break;

    case DESCENDING:
      if (value == null) {
        unsetDescending();
      } else {
        setDescending((Boolean)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_MSG:
      return getAuthMsg();

    case APPKEY:
      return getAppkey();

    case SID:
      return getSid();

    case DATE_FROM:
      return getDate_from();

    case DATE_TO:
      return getDate_to();

    case REASON:
      return getReason();

    case UID:
      return getUid();

    case COLUMNS:
      return getColumns();

    case PAGE:
      return getPage();

    case PER_PAGE:
      return getPer_page();

    case ORDER_BY:
      return getOrder_by();

    case DESCENDING:
      return isDescending();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_MSG:
      return isSetAuthMsg();
    case APPKEY:
      return isSetAppkey();
    case SID:
      return isSetSid();
    case DATE_FROM:
      return isSetDate_from();
    case DATE_TO:
      return isSetDate_to();
    case REASON:
      return isSetReason();
    case UID:
      return isSetUid();
    case COLUMNS:
      return isSetColumns();
    case PAGE:
      return isSetPage();
    case PER_PAGE:
      return isSetPer_page();
    case ORDER_BY:
      return isSetOrder_by();
    case DESCENDING:
      return isSetDescending();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaRequestUserBan)
      return this.equals((SaRequestUserBan)that);
    return false;
  }

  public boolean equals(SaRequestUserBan that) {
    if (that == null)
      return false;

    boolean this_present_authMsg = true && this.isSetAuthMsg();
    boolean that_present_authMsg = true && that.isSetAuthMsg();
    if (this_present_authMsg || that_present_authMsg) {
      if (!(this_present_authMsg && that_present_authMsg))
        return false;
      if (!this.authMsg.equals(that.authMsg))
        return false;
    }

    boolean this_present_appkey = true && this.isSetAppkey();
    boolean that_present_appkey = true && that.isSetAppkey();
    if (this_present_appkey || that_present_appkey) {
      if (!(this_present_appkey && that_present_appkey))
        return false;
      if (!this.appkey.equals(that.appkey))
        return false;
    }

    boolean this_present_sid = true && this.isSetSid();
    boolean that_present_sid = true && that.isSetSid();
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (!this.sid.equals(that.sid))
        return false;
    }

    boolean this_present_date_from = true && this.isSetDate_from();
    boolean that_present_date_from = true && that.isSetDate_from();
    if (this_present_date_from || that_present_date_from) {
      if (!(this_present_date_from && that_present_date_from))
        return false;
      if (!this.date_from.equals(that.date_from))
        return false;
    }

    boolean this_present_date_to = true && this.isSetDate_to();
    boolean that_present_date_to = true && that.isSetDate_to();
    if (this_present_date_to || that_present_date_to) {
      if (!(this_present_date_to && that_present_date_to))
        return false;
      if (!this.date_to.equals(that.date_to))
        return false;
    }

    boolean this_present_reason = true;
    boolean that_present_reason = true;
    if (this_present_reason || that_present_reason) {
      if (!(this_present_reason && that_present_reason))
        return false;
      if (this.reason != that.reason)
        return false;
    }

    boolean this_present_uid = true && this.isSetUid();
    boolean that_present_uid = true && that.isSetUid();
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (!this.uid.equals(that.uid))
        return false;
    }

    boolean this_present_columns = true && this.isSetColumns();
    boolean that_present_columns = true && that.isSetColumns();
    if (this_present_columns || that_present_columns) {
      if (!(this_present_columns && that_present_columns))
        return false;
      if (!this.columns.equals(that.columns))
        return false;
    }

    boolean this_present_page = true;
    boolean that_present_page = true;
    if (this_present_page || that_present_page) {
      if (!(this_present_page && that_present_page))
        return false;
      if (this.page != that.page)
        return false;
    }

    boolean this_present_per_page = true;
    boolean that_present_per_page = true;
    if (this_present_per_page || that_present_per_page) {
      if (!(this_present_per_page && that_present_per_page))
        return false;
      if (this.per_page != that.per_page)
        return false;
    }

    boolean this_present_order_by = true && this.isSetOrder_by();
    boolean that_present_order_by = true && that.isSetOrder_by();
    if (this_present_order_by || that_present_order_by) {
      if (!(this_present_order_by && that_present_order_by))
        return false;
      if (!this.order_by.equals(that.order_by))
        return false;
    }

    boolean this_present_descending = true;
    boolean that_present_descending = true;
    if (this_present_descending || that_present_descending) {
      if (!(this_present_descending && that_present_descending))
        return false;
      if (this.descending != that.descending)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authMsg = true && (isSetAuthMsg());
    list.add(present_authMsg);
    if (present_authMsg)
      list.add(authMsg);

    boolean present_appkey = true && (isSetAppkey());
    list.add(present_appkey);
    if (present_appkey)
      list.add(appkey);

    boolean present_sid = true && (isSetSid());
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_date_from = true && (isSetDate_from());
    list.add(present_date_from);
    if (present_date_from)
      list.add(date_from);

    boolean present_date_to = true && (isSetDate_to());
    list.add(present_date_to);
    if (present_date_to)
      list.add(date_to);

    boolean present_reason = true;
    list.add(present_reason);
    if (present_reason)
      list.add(reason);

    boolean present_uid = true && (isSetUid());
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    boolean present_columns = true && (isSetColumns());
    list.add(present_columns);
    if (present_columns)
      list.add(columns);

    boolean present_page = true;
    list.add(present_page);
    if (present_page)
      list.add(page);

    boolean present_per_page = true;
    list.add(present_per_page);
    if (present_per_page)
      list.add(per_page);

    boolean present_order_by = true && (isSetOrder_by());
    list.add(present_order_by);
    if (present_order_by)
      list.add(order_by);

    boolean present_descending = true;
    list.add(present_descending);
    if (present_descending)
      list.add(descending);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaRequestUserBan other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthMsg()).compareTo(other.isSetAuthMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authMsg, other.authMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppkey()).compareTo(other.isSetAppkey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppkey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appkey, other.appkey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDate_from()).compareTo(other.isSetDate_from());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDate_from()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.date_from, other.date_from);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDate_to()).compareTo(other.isSetDate_to());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDate_to()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.date_to, other.date_to);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReason()).compareTo(other.isSetReason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reason, other.reason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetColumns()).compareTo(other.isSetColumns());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetColumns()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.columns, other.columns);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.page, other.page);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPer_page()).compareTo(other.isSetPer_page());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPer_page()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.per_page, other.per_page);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrder_by()).compareTo(other.isSetOrder_by());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrder_by()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.order_by, other.order_by);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDescending()).compareTo(other.isSetDescending());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDescending()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.descending, other.descending);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaRequestUserBan(");
    boolean first = true;

    sb.append("authMsg:");
    if (this.authMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authMsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appkey:");
    if (this.appkey == null) {
      sb.append("null");
    } else {
      sb.append(this.appkey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    if (this.sid == null) {
      sb.append("null");
    } else {
      sb.append(this.sid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("date_from:");
    if (this.date_from == null) {
      sb.append("null");
    } else {
      sb.append(this.date_from);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("date_to:");
    if (this.date_to == null) {
      sb.append("null");
    } else {
      sb.append(this.date_to);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("reason:");
    sb.append(this.reason);
    first = false;
    if (!first) sb.append(", ");
    sb.append("uid:");
    if (this.uid == null) {
      sb.append("null");
    } else {
      sb.append(this.uid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("columns:");
    if (this.columns == null) {
      sb.append("null");
    } else {
      sb.append(this.columns);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("page:");
    sb.append(this.page);
    first = false;
    if (!first) sb.append(", ");
    sb.append("per_page:");
    sb.append(this.per_page);
    first = false;
    if (!first) sb.append(", ");
    sb.append("order_by:");
    if (this.order_by == null) {
      sb.append("null");
    } else {
      sb.append(this.order_by);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("descending:");
    sb.append(this.descending);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
    if (authMsg != null) {
      authMsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaRequestUserBanStandardSchemeFactory implements SchemeFactory {
    public SaRequestUserBanStandardScheme getScheme() {
      return new SaRequestUserBanStandardScheme();
    }
  }

  private static class SaRequestUserBanStandardScheme extends StandardScheme<SaRequestUserBan> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaRequestUserBan struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authMsg = new AuthorizeMsg();
              struct.authMsg.read(iprot);
              struct.setAuthMsgIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPKEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appkey = iprot.readString();
              struct.setAppkeyIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sid = iprot.readString();
              struct.setSidIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DATE_FROM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.date_from = iprot.readString();
              struct.setDate_fromIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // DATE_TO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.date_to = iprot.readString();
              struct.setDate_toIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // REASON
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.reason = iprot.readI32();
              struct.setReasonIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.uid = iprot.readString();
              struct.setUidIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // COLUMNS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list228 = iprot.readListBegin();
                struct.columns = new ArrayList<String>(_list228.size);
                String _elem229;
                for (int _i230 = 0; _i230 < _list228.size; ++_i230)
                {
                  _elem229 = iprot.readString();
                  struct.columns.add(_elem229);
                }
                iprot.readListEnd();
              }
              struct.setColumnsIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // PAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.page = iprot.readI32();
              struct.setPageIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // PER_PAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.per_page = iprot.readI32();
              struct.setPer_pageIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // ORDER_BY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.order_by = iprot.readString();
              struct.setOrder_byIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // DESCENDING
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.descending = iprot.readBool();
              struct.setDescendingIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaRequestUserBan struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authMsg != null) {
        oprot.writeFieldBegin(AUTH_MSG_FIELD_DESC);
        struct.authMsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appkey != null) {
        oprot.writeFieldBegin(APPKEY_FIELD_DESC);
        oprot.writeString(struct.appkey);
        oprot.writeFieldEnd();
      }
      if (struct.sid != null) {
        oprot.writeFieldBegin(SID_FIELD_DESC);
        oprot.writeString(struct.sid);
        oprot.writeFieldEnd();
      }
      if (struct.date_from != null) {
        oprot.writeFieldBegin(DATE_FROM_FIELD_DESC);
        oprot.writeString(struct.date_from);
        oprot.writeFieldEnd();
      }
      if (struct.date_to != null) {
        oprot.writeFieldBegin(DATE_TO_FIELD_DESC);
        oprot.writeString(struct.date_to);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(REASON_FIELD_DESC);
      oprot.writeI32(struct.reason);
      oprot.writeFieldEnd();
      if (struct.uid != null) {
        oprot.writeFieldBegin(UID_FIELD_DESC);
        oprot.writeString(struct.uid);
        oprot.writeFieldEnd();
      }
      if (struct.columns != null) {
        oprot.writeFieldBegin(COLUMNS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.columns.size()));
          for (String _iter231 : struct.columns)
          {
            oprot.writeString(_iter231);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PAGE_FIELD_DESC);
      oprot.writeI32(struct.page);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PER_PAGE_FIELD_DESC);
      oprot.writeI32(struct.per_page);
      oprot.writeFieldEnd();
      if (struct.order_by != null) {
        oprot.writeFieldBegin(ORDER_BY_FIELD_DESC);
        oprot.writeString(struct.order_by);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(DESCENDING_FIELD_DESC);
      oprot.writeBool(struct.descending);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaRequestUserBanTupleSchemeFactory implements SchemeFactory {
    public SaRequestUserBanTupleScheme getScheme() {
      return new SaRequestUserBanTupleScheme();
    }
  }

  private static class SaRequestUserBanTupleScheme extends TupleScheme<SaRequestUserBan> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaRequestUserBan struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthMsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppkey()) {
        optionals.set(1);
      }
      if (struct.isSetSid()) {
        optionals.set(2);
      }
      if (struct.isSetDate_from()) {
        optionals.set(3);
      }
      if (struct.isSetDate_to()) {
        optionals.set(4);
      }
      if (struct.isSetReason()) {
        optionals.set(5);
      }
      if (struct.isSetUid()) {
        optionals.set(6);
      }
      if (struct.isSetColumns()) {
        optionals.set(7);
      }
      if (struct.isSetPage()) {
        optionals.set(8);
      }
      if (struct.isSetPer_page()) {
        optionals.set(9);
      }
      if (struct.isSetOrder_by()) {
        optionals.set(10);
      }
      if (struct.isSetDescending()) {
        optionals.set(11);
      }
      oprot.writeBitSet(optionals, 12);
      if (struct.isSetAuthMsg()) {
        struct.authMsg.write(oprot);
      }
      if (struct.isSetAppkey()) {
        oprot.writeString(struct.appkey);
      }
      if (struct.isSetSid()) {
        oprot.writeString(struct.sid);
      }
      if (struct.isSetDate_from()) {
        oprot.writeString(struct.date_from);
      }
      if (struct.isSetDate_to()) {
        oprot.writeString(struct.date_to);
      }
      if (struct.isSetReason()) {
        oprot.writeI32(struct.reason);
      }
      if (struct.isSetUid()) {
        oprot.writeString(struct.uid);
      }
      if (struct.isSetColumns()) {
        {
          oprot.writeI32(struct.columns.size());
          for (String _iter232 : struct.columns)
          {
            oprot.writeString(_iter232);
          }
        }
      }
      if (struct.isSetPage()) {
        oprot.writeI32(struct.page);
      }
      if (struct.isSetPer_page()) {
        oprot.writeI32(struct.per_page);
      }
      if (struct.isSetOrder_by()) {
        oprot.writeString(struct.order_by);
      }
      if (struct.isSetDescending()) {
        oprot.writeBool(struct.descending);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaRequestUserBan struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(12);
      if (incoming.get(0)) {
        struct.authMsg = new AuthorizeMsg();
        struct.authMsg.read(iprot);
        struct.setAuthMsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appkey = iprot.readString();
        struct.setAppkeyIsSet(true);
      }
      if (incoming.get(2)) {
        struct.sid = iprot.readString();
        struct.setSidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.date_from = iprot.readString();
        struct.setDate_fromIsSet(true);
      }
      if (incoming.get(4)) {
        struct.date_to = iprot.readString();
        struct.setDate_toIsSet(true);
      }
      if (incoming.get(5)) {
        struct.reason = iprot.readI32();
        struct.setReasonIsSet(true);
      }
      if (incoming.get(6)) {
        struct.uid = iprot.readString();
        struct.setUidIsSet(true);
      }
      if (incoming.get(7)) {
        {
          org.apache.thrift.protocol.TList _list233 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.columns = new ArrayList<String>(_list233.size);
          String _elem234;
          for (int _i235 = 0; _i235 < _list233.size; ++_i235)
          {
            _elem234 = iprot.readString();
            struct.columns.add(_elem234);
          }
        }
        struct.setColumnsIsSet(true);
      }
      if (incoming.get(8)) {
        struct.page = iprot.readI32();
        struct.setPageIsSet(true);
      }
      if (incoming.get(9)) {
        struct.per_page = iprot.readI32();
        struct.setPer_pageIsSet(true);
      }
      if (incoming.get(10)) {
        struct.order_by = iprot.readString();
        struct.setOrder_byIsSet(true);
      }
      if (incoming.get(11)) {
        struct.descending = iprot.readBool();
        struct.setDescendingIsSet(true);
      }
    }
  }

}

