package com.yy.manager.thrift.client;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.manager.thrift.support.WebdbSaHelper;
import com.yy.manager.thrift.webdbservice.*;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 用户类信息服务接口
 *
 * <AUTHOR>
 * @date 2021年06月01日 上午10:33:27
 **/
@Component
@Slf4j
public class WebdbUinfoClient {
    @Reference(protocol = "nythrift", owner = "__", url = "${thrift.service-agent.url}")
    private webdb_uinfo_service.Iface proxy = null;

    // webdb 用户信息列
    private final static List<String> USER_INFO_COLUMNS = Lists.newArrayList(
            // 用户uid
            "id",
            // 通行证
            "passport",
            // 邮箱
            "account",
            // 用户昵称
            "nick",
            // 性别
            "sex",
            // 签名
            "sign",
            // 个人积分,按分钟计算
            "jifen",
            // 注册时间
            "register_time",
            // 个人高清头像图片url
            "hdlogo",
            // yy号
            "yyno",
            //  比老webdb client 多加的：用户个人普通自定义头像url(60*60)
            "custom_logo",
            //  比老webdb client 多加的：个人系统头像类型。0为自定义头像，即为hdlogo；其他值为系统定义头像
            "logo_index"
    );

    public webdb_uinfo_service.Iface getProxy() {
        return proxy;
    }

    /**
     * 批量获取用户信息
     */
    //@Cached(timeToLiveMillis = CacheTimeout.yy_user_info)
    public Map<Long, Map<String, String>> batchGetUserInfo(List<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }
        long startTime = System.currentTimeMillis();
        Long firstUid = CollectionUtils.isEmpty(uids) ? -1L : uids.getFirst();
        Map<Long, Map<String, String>> map = Maps.newHashMap();
        try {
            List<String> userIds = Lists.newArrayList();
            for (Long uid : uids) {
                userIds.add(uid.toString());
            }

            SaRequestBatchUser req = new SaRequestBatchUser();
            req.setAuthMsg(WebdbSaHelper.getAuthorizeMsg());
            req.setAppkey(Const.APP_ID);
            req.setUids(userIds);
            req.setColumns(USER_INFO_COLUMNS);

            SaResponseSet result = proxy.sa_batch_get_user_info(req);
            if (result == null || result.getRescode() != 0 || CollectionUtils.isEmpty(result.getDataSet())) {
                return map;
            }

            List<StringList> dataset = result.getDataSet();
            for (StringList strings : dataset) {
                Map<String, String> user = Maps.newHashMap();
                for (int i = 0; i < USER_INFO_COLUMNS.size(); i++) {
                    fillValue4UserInfo(user, USER_INFO_COLUMNS, strings, i);
                }
                Long yyuid = Long.parseLong(user.get("id"));
                map.put(yyuid, user);

                // 填充用户 yylogo
                fillUserYylogo(user);
            }

            if (MapUtils.isEmpty(map)) {
                log.warn("batchGetUserInfo() | map is empty | uid size:{} {}", uids.size(), System.currentTimeMillis() - startTime);
            }
        } catch (Exception e) {
            log.error("batchGetUserInfo exception@firstUid:{} {}", firstUid, System.currentTimeMillis() - startTime, e);
        }

        return map;
    }

    public Map<String, String> getUserInfo(long uid) {
        Map<Long, Map<String, String>> userInfoMap = batchGetUserInfo(Collections.singletonList(uid));
        return userInfoMap.get(uid);
    }

    public long getUidByYyno(long yyno) {
        SaRequestUid request = new SaRequestUid();
        request.setAuthMsg(WebdbSaHelper.getAuthorizeMsg());
        request.setAppkey(Const.APP_ID);
        request.setImids(Collections.singletonList(String.valueOf(yyno)));

        try {
            SaResponse response = getProxy().sa_get_uid_by_imid(request);
            if (response == null || response.rescode != 0) {
                return 0;
            }

            return response.keyValue.entrySet().stream().findFirst().map(Map.Entry::getValue).map(Long::parseLong).orElse(0L);
        } catch (Exception e) {
            log.error("getUidByYyno exception:", e);
        }

        return 0;
    }

    /**
     * 填充用户头像
     */
    private void fillUserYylogo(Map<String, String> user) throws TException {
        String logo = Const.IMAGE.DEFAULT_USER_LOGO;
        String url = user.get("custom_logo");
        if (!StringUtils.isBlank(url)) {
            logo = url.trim();
        } else {
            int num10100 = 10100;
            int num10163 = 10163;
            long logoIndex = Convert.toLong(user.get("logo_index"), 0);
            if (logoIndex > 0) {
                if (!(logoIndex >= num10100 && logoIndex <= num10163)) {
                    logoIndex = 10001;
                }
                logo = "https://s1.yy.com/guild/header/" + logoIndex + ".jpg";
            }
        }
        user.put("yylogo", logo);

        // 移除这2个属性，保持和老的webdb client 一致
        user.remove("custom_logo");
        user.remove("logo_index");
    }

    /**
     * 填充用户字段
     *
     * <AUTHOR>
     * @date 2018年9月11日 上午11:17:04
     */
    private void fillValue4UserInfo(Map<String, String> user, List<String> userInfoColumns, StringList strings, int i) {
        String key = userInfoColumns.get(i);
        String value = strings.getStrList().get(i);
        user.put(key, value);
    }

    /**
     * SaResponseSet 转成 List<Map>
     */
    private List<Map<String, String>> getSaResponseMapsForUser(SaResponseSet saResponseSet) {
        Map<String, Integer> keyIndex = saResponseSet.getKeyIndex();
        List<StringList> dataSet = saResponseSet.getDataSet();
        if (MapUtils.isEmpty(keyIndex) || CollectionUtils.isEmpty(dataSet)) {
            return Lists.newArrayList();
        }
        List<Map<String, String>> saResponseMaps = Lists.newArrayList();
        for (StringList data : dataSet) {
            Map<String, String> dataMap = Maps.newHashMap();
            List<String> strList = data.getStrList();
            for (Map.Entry<String, Integer> entry : keyIndex.entrySet()) {
                dataMap.put(entry.getKey(), strList.get(entry.getValue()));
            }
            saResponseMaps.add(dataMap);
        }

        return saResponseMaps;
    }
}
