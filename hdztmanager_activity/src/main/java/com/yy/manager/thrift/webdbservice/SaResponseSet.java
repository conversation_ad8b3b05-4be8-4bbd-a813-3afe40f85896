/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 * @param     keyIndex           columns中各字段在dataSet中对应的索引
 * @param     dataset            内层StringList表示一行查询结果,外层list表示多行查询结果
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class SaResponseSet implements org.apache.thrift.TBase<SaResponseSet, SaResponseSet._Fields>, java.io.Serializable, Cloneable, Comparable<SaResponseSet> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaResponseSet");

  private static final org.apache.thrift.protocol.TField RESCODE_FIELD_DESC = new org.apache.thrift.protocol.TField("rescode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField KEY_VALUE_FIELD_DESC = new org.apache.thrift.protocol.TField("keyValue", org.apache.thrift.protocol.TType.MAP, (short)2);
  private static final org.apache.thrift.protocol.TField KEY_INDEX_FIELD_DESC = new org.apache.thrift.protocol.TField("keyIndex", org.apache.thrift.protocol.TType.MAP, (short)3);
  private static final org.apache.thrift.protocol.TField DATA_SET_FIELD_DESC = new org.apache.thrift.protocol.TField("dataSet", org.apache.thrift.protocol.TType.LIST, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaResponseSetStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaResponseSetTupleSchemeFactory());
  }

  public int rescode; // required
  public Map<String,String> keyValue; // required
  public Map<String,Integer> keyIndex; // required
  public List<StringList> dataSet; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RESCODE((short)1, "rescode"),
    KEY_VALUE((short)2, "keyValue"),
    KEY_INDEX((short)3, "keyIndex"),
    DATA_SET((short)4, "dataSet");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RESCODE
          return RESCODE;
        case 2: // KEY_VALUE
          return KEY_VALUE;
        case 3: // KEY_INDEX
          return KEY_INDEX;
        case 4: // DATA_SET
          return DATA_SET;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RESCODE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RESCODE, new org.apache.thrift.meta_data.FieldMetaData("rescode", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.KEY_VALUE, new org.apache.thrift.meta_data.FieldMetaData("keyValue", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING),
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.KEY_INDEX, new org.apache.thrift.meta_data.FieldMetaData("keyIndex", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING),
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    tmpMap.put(_Fields.DATA_SET, new org.apache.thrift.meta_data.FieldMetaData("dataSet", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, StringList.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaResponseSet.class, metaDataMap);
  }

  public SaResponseSet() {
  }

  public SaResponseSet(
    int rescode,
    Map<String,String> keyValue,
    Map<String,Integer> keyIndex,
    List<StringList> dataSet)
  {
    this();
    this.rescode = rescode;
    setRescodeIsSet(true);
    this.keyValue = keyValue;
    this.keyIndex = keyIndex;
    this.dataSet = dataSet;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaResponseSet(SaResponseSet other) {
    __isset_bitfield = other.__isset_bitfield;
    this.rescode = other.rescode;
    if (other.isSetKeyValue()) {
      Map<String,String> __this__keyValue = new HashMap<String,String>(other.keyValue);
      this.keyValue = __this__keyValue;
    }
    if (other.isSetKeyIndex()) {
      Map<String,Integer> __this__keyIndex = new HashMap<String,Integer>(other.keyIndex);
      this.keyIndex = __this__keyIndex;
    }
    if (other.isSetDataSet()) {
      List<StringList> __this__dataSet = new ArrayList<StringList>(other.dataSet.size());
      for (StringList other_element : other.dataSet) {
        __this__dataSet.add(new StringList(other_element));
      }
      this.dataSet = __this__dataSet;
    }
  }

  public SaResponseSet deepCopy() {
    return new SaResponseSet(this);
  }

  @Override
  public void clear() {
    setRescodeIsSet(false);
    this.rescode = 0;
    this.keyValue = null;
    this.keyIndex = null;
    this.dataSet = null;
  }

  public int getRescode() {
    return this.rescode;
  }

  public SaResponseSet setRescode(int rescode) {
    this.rescode = rescode;
    setRescodeIsSet(true);
    return this;
  }

  public void unsetRescode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RESCODE_ISSET_ID);
  }

  /** Returns true if field rescode is set (has been assigned a value) and false otherwise */
  public boolean isSetRescode() {
    return EncodingUtils.testBit(__isset_bitfield, __RESCODE_ISSET_ID);
  }

  public void setRescodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RESCODE_ISSET_ID, value);
  }

  public int getKeyValueSize() {
    return (this.keyValue == null) ? 0 : this.keyValue.size();
  }

  public void putToKeyValue(String key, String val) {
    if (this.keyValue == null) {
      this.keyValue = new HashMap<String,String>();
    }
    this.keyValue.put(key, val);
  }

  public Map<String,String> getKeyValue() {
    return this.keyValue;
  }

  public SaResponseSet setKeyValue(Map<String,String> keyValue) {
    this.keyValue = keyValue;
    return this;
  }

  public void unsetKeyValue() {
    this.keyValue = null;
  }

  /** Returns true if field keyValue is set (has been assigned a value) and false otherwise */
  public boolean isSetKeyValue() {
    return this.keyValue != null;
  }

  public void setKeyValueIsSet(boolean value) {
    if (!value) {
      this.keyValue = null;
    }
  }

  public int getKeyIndexSize() {
    return (this.keyIndex == null) ? 0 : this.keyIndex.size();
  }

  public void putToKeyIndex(String key, int val) {
    if (this.keyIndex == null) {
      this.keyIndex = new HashMap<String,Integer>();
    }
    this.keyIndex.put(key, val);
  }

  public Map<String,Integer> getKeyIndex() {
    return this.keyIndex;
  }

  public SaResponseSet setKeyIndex(Map<String,Integer> keyIndex) {
    this.keyIndex = keyIndex;
    return this;
  }

  public void unsetKeyIndex() {
    this.keyIndex = null;
  }

  /** Returns true if field keyIndex is set (has been assigned a value) and false otherwise */
  public boolean isSetKeyIndex() {
    return this.keyIndex != null;
  }

  public void setKeyIndexIsSet(boolean value) {
    if (!value) {
      this.keyIndex = null;
    }
  }

  public int getDataSetSize() {
    return (this.dataSet == null) ? 0 : this.dataSet.size();
  }

  public java.util.Iterator<StringList> getDataSetIterator() {
    return (this.dataSet == null) ? null : this.dataSet.iterator();
  }

  public void addToDataSet(StringList elem) {
    if (this.dataSet == null) {
      this.dataSet = new ArrayList<StringList>();
    }
    this.dataSet.add(elem);
  }

  public List<StringList> getDataSet() {
    return this.dataSet;
  }

  public SaResponseSet setDataSet(List<StringList> dataSet) {
    this.dataSet = dataSet;
    return this;
  }

  public void unsetDataSet() {
    this.dataSet = null;
  }

  /** Returns true if field dataSet is set (has been assigned a value) and false otherwise */
  public boolean isSetDataSet() {
    return this.dataSet != null;
  }

  public void setDataSetIsSet(boolean value) {
    if (!value) {
      this.dataSet = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RESCODE:
      if (value == null) {
        unsetRescode();
      } else {
        setRescode((Integer)value);
      }
      break;

    case KEY_VALUE:
      if (value == null) {
        unsetKeyValue();
      } else {
        setKeyValue((Map<String,String>)value);
      }
      break;

    case KEY_INDEX:
      if (value == null) {
        unsetKeyIndex();
      } else {
        setKeyIndex((Map<String,Integer>)value);
      }
      break;

    case DATA_SET:
      if (value == null) {
        unsetDataSet();
      } else {
        setDataSet((List<StringList>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RESCODE:
      return getRescode();

    case KEY_VALUE:
      return getKeyValue();

    case KEY_INDEX:
      return getKeyIndex();

    case DATA_SET:
      return getDataSet();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RESCODE:
      return isSetRescode();
    case KEY_VALUE:
      return isSetKeyValue();
    case KEY_INDEX:
      return isSetKeyIndex();
    case DATA_SET:
      return isSetDataSet();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaResponseSet)
      return this.equals((SaResponseSet)that);
    return false;
  }

  public boolean equals(SaResponseSet that) {
    if (that == null)
      return false;

    boolean this_present_rescode = true;
    boolean that_present_rescode = true;
    if (this_present_rescode || that_present_rescode) {
      if (!(this_present_rescode && that_present_rescode))
        return false;
      if (this.rescode != that.rescode)
        return false;
    }

    boolean this_present_keyValue = true && this.isSetKeyValue();
    boolean that_present_keyValue = true && that.isSetKeyValue();
    if (this_present_keyValue || that_present_keyValue) {
      if (!(this_present_keyValue && that_present_keyValue))
        return false;
      if (!this.keyValue.equals(that.keyValue))
        return false;
    }

    boolean this_present_keyIndex = true && this.isSetKeyIndex();
    boolean that_present_keyIndex = true && that.isSetKeyIndex();
    if (this_present_keyIndex || that_present_keyIndex) {
      if (!(this_present_keyIndex && that_present_keyIndex))
        return false;
      if (!this.keyIndex.equals(that.keyIndex))
        return false;
    }

    boolean this_present_dataSet = true && this.isSetDataSet();
    boolean that_present_dataSet = true && that.isSetDataSet();
    if (this_present_dataSet || that_present_dataSet) {
      if (!(this_present_dataSet && that_present_dataSet))
        return false;
      if (!this.dataSet.equals(that.dataSet))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rescode = true;
    list.add(present_rescode);
    if (present_rescode)
      list.add(rescode);

    boolean present_keyValue = true && (isSetKeyValue());
    list.add(present_keyValue);
    if (present_keyValue)
      list.add(keyValue);

    boolean present_keyIndex = true && (isSetKeyIndex());
    list.add(present_keyIndex);
    if (present_keyIndex)
      list.add(keyIndex);

    boolean present_dataSet = true && (isSetDataSet());
    list.add(present_dataSet);
    if (present_dataSet)
      list.add(dataSet);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaResponseSet other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRescode()).compareTo(other.isSetRescode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRescode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rescode, other.rescode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKeyValue()).compareTo(other.isSetKeyValue());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKeyValue()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.keyValue, other.keyValue);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKeyIndex()).compareTo(other.isSetKeyIndex());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKeyIndex()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.keyIndex, other.keyIndex);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDataSet()).compareTo(other.isSetDataSet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDataSet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dataSet, other.dataSet);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaResponseSet(");
    boolean first = true;

    sb.append("rescode:");
    sb.append(this.rescode);
    first = false;
    if (!first) sb.append(", ");
    sb.append("keyValue:");
    if (this.keyValue == null) {
      sb.append("null");
    } else {
      sb.append(this.keyValue);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("keyIndex:");
    if (this.keyIndex == null) {
      sb.append("null");
    } else {
      sb.append(this.keyIndex);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("dataSet:");
    if (this.dataSet == null) {
      sb.append("null");
    } else {
      sb.append(this.dataSet);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaResponseSetStandardSchemeFactory implements SchemeFactory {
    public SaResponseSetStandardScheme getScheme() {
      return new SaResponseSetStandardScheme();
    }
  }

  private static class SaResponseSetStandardScheme extends StandardScheme<SaResponseSet> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaResponseSet struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // RESCODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rescode = iprot.readI32();
              struct.setRescodeIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // KEY_VALUE
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map28 = iprot.readMapBegin();
                struct.keyValue = new HashMap<String,String>(2*_map28.size);
                String _key29;
                String _val30;
                for (int _i31 = 0; _i31 < _map28.size; ++_i31)
                {
                  _key29 = iprot.readString();
                  _val30 = iprot.readString();
                  struct.keyValue.put(_key29, _val30);
                }
                iprot.readMapEnd();
              }
              struct.setKeyValueIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // KEY_INDEX
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map32 = iprot.readMapBegin();
                struct.keyIndex = new HashMap<String,Integer>(2*_map32.size);
                String _key33;
                int _val34;
                for (int _i35 = 0; _i35 < _map32.size; ++_i35)
                {
                  _key33 = iprot.readString();
                  _val34 = iprot.readI32();
                  struct.keyIndex.put(_key33, _val34);
                }
                iprot.readMapEnd();
              }
              struct.setKeyIndexIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DATA_SET
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list36 = iprot.readListBegin();
                struct.dataSet = new ArrayList<StringList>(_list36.size);
                StringList _elem37;
                for (int _i38 = 0; _i38 < _list36.size; ++_i38)
                {
                  _elem37 = new StringList();
                  _elem37.read(iprot);
                  struct.dataSet.add(_elem37);
                }
                iprot.readListEnd();
              }
              struct.setDataSetIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaResponseSet struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RESCODE_FIELD_DESC);
      oprot.writeI32(struct.rescode);
      oprot.writeFieldEnd();
      if (struct.keyValue != null) {
        oprot.writeFieldBegin(KEY_VALUE_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.keyValue.size()));
          for (Map.Entry<String, String> _iter39 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter39.getKey());
            oprot.writeString(_iter39.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.keyIndex != null) {
        oprot.writeFieldBegin(KEY_INDEX_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, struct.keyIndex.size()));
          for (Map.Entry<String, Integer> _iter40 : struct.keyIndex.entrySet())
          {
            oprot.writeString(_iter40.getKey());
            oprot.writeI32(_iter40.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.dataSet != null) {
        oprot.writeFieldBegin(DATA_SET_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.dataSet.size()));
          for (StringList _iter41 : struct.dataSet)
          {
            _iter41.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaResponseSetTupleSchemeFactory implements SchemeFactory {
    public SaResponseSetTupleScheme getScheme() {
      return new SaResponseSetTupleScheme();
    }
  }

  private static class SaResponseSetTupleScheme extends TupleScheme<SaResponseSet> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaResponseSet struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRescode()) {
        optionals.set(0);
      }
      if (struct.isSetKeyValue()) {
        optionals.set(1);
      }
      if (struct.isSetKeyIndex()) {
        optionals.set(2);
      }
      if (struct.isSetDataSet()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetRescode()) {
        oprot.writeI32(struct.rescode);
      }
      if (struct.isSetKeyValue()) {
        {
          oprot.writeI32(struct.keyValue.size());
          for (Map.Entry<String, String> _iter42 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter42.getKey());
            oprot.writeString(_iter42.getValue());
          }
        }
      }
      if (struct.isSetKeyIndex()) {
        {
          oprot.writeI32(struct.keyIndex.size());
          for (Map.Entry<String, Integer> _iter43 : struct.keyIndex.entrySet())
          {
            oprot.writeString(_iter43.getKey());
            oprot.writeI32(_iter43.getValue());
          }
        }
      }
      if (struct.isSetDataSet()) {
        {
          oprot.writeI32(struct.dataSet.size());
          for (StringList _iter44 : struct.dataSet)
          {
            _iter44.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaResponseSet struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.rescode = iprot.readI32();
        struct.setRescodeIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map45 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.keyValue = new HashMap<String,String>(2*_map45.size);
          String _key46;
          String _val47;
          for (int _i48 = 0; _i48 < _map45.size; ++_i48)
          {
            _key46 = iprot.readString();
            _val47 = iprot.readString();
            struct.keyValue.put(_key46, _val47);
          }
        }
        struct.setKeyValueIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map49 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, iprot.readI32());
          struct.keyIndex = new HashMap<String,Integer>(2*_map49.size);
          String _key50;
          int _val51;
          for (int _i52 = 0; _i52 < _map49.size; ++_i52)
          {
            _key50 = iprot.readString();
            _val51 = iprot.readI32();
            struct.keyIndex.put(_key50, _val51);
          }
        }
        struct.setKeyIndexIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list53 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.dataSet = new ArrayList<StringList>(_list53.size);
          StringList _elem54;
          for (int _i55 = 0; _i55 < _list53.size; ++_i55)
          {
            _elem54 = new StringList();
            _elem54.read(iprot);
            struct.dataSet.add(_elem54);
          }
        }
        struct.setDataSetIsSet(true);
      }
    }
  }

}

