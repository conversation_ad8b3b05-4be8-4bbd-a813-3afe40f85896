/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.to_service;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2025-06-26")
public class THudongPropTimer implements org.apache.thrift.TBase<THudongPropTimer, THudongPropTimer._Fields>, java.io.Serializable, Cloneable, Comparable<THudongPropTimer> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("THudongPropTimer");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField PROP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("propId", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField EXECUTE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("executeTime", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField VISIBLE_FIELD_DESC = new org.apache.thrift.protocol.TField("visible", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField CAN_BUY_FIELD_DESC = new org.apache.thrift.protocol.TField("canBuy", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField SHIELD_PROP_WALL_FIELD_DESC = new org.apache.thrift.protocol.TField("shieldPropWall", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField PROP_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("propName", org.apache.thrift.protocol.TType.STRING, (short)8);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new THudongPropTimerStandardSchemeFactory());
    schemes.put(TupleScheme.class, new THudongPropTimerTupleSchemeFactory());
  }

  public int id; // required
  public int appid; // required
  public int propId; // required
  public long executeTime; // required
  public int visible; // required
  public int canBuy; // required
  public int shieldPropWall; // required
  public String propName; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ID((short)1, "id"),
    APPID((short)2, "appid"),
    PROP_ID((short)3, "propId"),
    EXECUTE_TIME((short)4, "executeTime"),
    VISIBLE((short)5, "visible"),
    CAN_BUY((short)6, "canBuy"),
    SHIELD_PROP_WALL((short)7, "shieldPropWall"),
    PROP_NAME((short)8, "propName");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // APPID
          return APPID;
        case 3: // PROP_ID
          return PROP_ID;
        case 4: // EXECUTE_TIME
          return EXECUTE_TIME;
        case 5: // VISIBLE
          return VISIBLE;
        case 6: // CAN_BUY
          return CAN_BUY;
        case 7: // SHIELD_PROP_WALL
          return SHIELD_PROP_WALL;
        case 8: // PROP_NAME
          return PROP_NAME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __APPID_ISSET_ID = 1;
  private static final int __PROPID_ISSET_ID = 2;
  private static final int __EXECUTETIME_ISSET_ID = 3;
  private static final int __VISIBLE_ISSET_ID = 4;
  private static final int __CANBUY_ISSET_ID = 5;
  private static final int __SHIELDPROPWALL_ISSET_ID = 6;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PROP_ID, new org.apache.thrift.meta_data.FieldMetaData("propId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXECUTE_TIME, new org.apache.thrift.meta_data.FieldMetaData("executeTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.VISIBLE, new org.apache.thrift.meta_data.FieldMetaData("visible", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CAN_BUY, new org.apache.thrift.meta_data.FieldMetaData("canBuy", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SHIELD_PROP_WALL, new org.apache.thrift.meta_data.FieldMetaData("shieldPropWall", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PROP_NAME, new org.apache.thrift.meta_data.FieldMetaData("propName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(THudongPropTimer.class, metaDataMap);
  }

  public THudongPropTimer() {
  }

  public THudongPropTimer(
    int id,
    int appid,
    int propId,
    long executeTime,
    int visible,
    int canBuy,
    int shieldPropWall,
    String propName)
  {
    this();
    this.id = id;
    setIdIsSet(true);
    this.appid = appid;
    setAppidIsSet(true);
    this.propId = propId;
    setPropIdIsSet(true);
    this.executeTime = executeTime;
    setExecuteTimeIsSet(true);
    this.visible = visible;
    setVisibleIsSet(true);
    this.canBuy = canBuy;
    setCanBuyIsSet(true);
    this.shieldPropWall = shieldPropWall;
    setShieldPropWallIsSet(true);
    this.propName = propName;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public THudongPropTimer(THudongPropTimer other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    this.appid = other.appid;
    this.propId = other.propId;
    this.executeTime = other.executeTime;
    this.visible = other.visible;
    this.canBuy = other.canBuy;
    this.shieldPropWall = other.shieldPropWall;
    if (other.isSetPropName()) {
      this.propName = other.propName;
    }
  }

  public THudongPropTimer deepCopy() {
    return new THudongPropTimer(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    setAppidIsSet(false);
    this.appid = 0;
    setPropIdIsSet(false);
    this.propId = 0;
    setExecuteTimeIsSet(false);
    this.executeTime = 0;
    setVisibleIsSet(false);
    this.visible = 0;
    setCanBuyIsSet(false);
    this.canBuy = 0;
    setShieldPropWallIsSet(false);
    this.shieldPropWall = 0;
    this.propName = null;
  }

  public int getId() {
    return this.id;
  }

  public THudongPropTimer setId(int id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  public int getAppid() {
    return this.appid;
  }

  public THudongPropTimer setAppid(int appid) {
    this.appid = appid;
    setAppidIsSet(true);
    return this;
  }

  public void unsetAppid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  public int getPropId() {
    return this.propId;
  }

  public THudongPropTimer setPropId(int propId) {
    this.propId = propId;
    setPropIdIsSet(true);
    return this;
  }

  public void unsetPropId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PROPID_ISSET_ID);
  }

  /** Returns true if field propId is set (has been assigned a value) and false otherwise */
  public boolean isSetPropId() {
    return EncodingUtils.testBit(__isset_bitfield, __PROPID_ISSET_ID);
  }

  public void setPropIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PROPID_ISSET_ID, value);
  }

  public long getExecuteTime() {
    return this.executeTime;
  }

  public THudongPropTimer setExecuteTime(long executeTime) {
    this.executeTime = executeTime;
    setExecuteTimeIsSet(true);
    return this;
  }

  public void unsetExecuteTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXECUTETIME_ISSET_ID);
  }

  /** Returns true if field executeTime is set (has been assigned a value) and false otherwise */
  public boolean isSetExecuteTime() {
    return EncodingUtils.testBit(__isset_bitfield, __EXECUTETIME_ISSET_ID);
  }

  public void setExecuteTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXECUTETIME_ISSET_ID, value);
  }

  public int getVisible() {
    return this.visible;
  }

  public THudongPropTimer setVisible(int visible) {
    this.visible = visible;
    setVisibleIsSet(true);
    return this;
  }

  public void unsetVisible() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __VISIBLE_ISSET_ID);
  }

  /** Returns true if field visible is set (has been assigned a value) and false otherwise */
  public boolean isSetVisible() {
    return EncodingUtils.testBit(__isset_bitfield, __VISIBLE_ISSET_ID);
  }

  public void setVisibleIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __VISIBLE_ISSET_ID, value);
  }

  public int getCanBuy() {
    return this.canBuy;
  }

  public THudongPropTimer setCanBuy(int canBuy) {
    this.canBuy = canBuy;
    setCanBuyIsSet(true);
    return this;
  }

  public void unsetCanBuy() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CANBUY_ISSET_ID);
  }

  /** Returns true if field canBuy is set (has been assigned a value) and false otherwise */
  public boolean isSetCanBuy() {
    return EncodingUtils.testBit(__isset_bitfield, __CANBUY_ISSET_ID);
  }

  public void setCanBuyIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CANBUY_ISSET_ID, value);
  }

  public int getShieldPropWall() {
    return this.shieldPropWall;
  }

  public THudongPropTimer setShieldPropWall(int shieldPropWall) {
    this.shieldPropWall = shieldPropWall;
    setShieldPropWallIsSet(true);
    return this;
  }

  public void unsetShieldPropWall() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SHIELDPROPWALL_ISSET_ID);
  }

  /** Returns true if field shieldPropWall is set (has been assigned a value) and false otherwise */
  public boolean isSetShieldPropWall() {
    return EncodingUtils.testBit(__isset_bitfield, __SHIELDPROPWALL_ISSET_ID);
  }

  public void setShieldPropWallIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SHIELDPROPWALL_ISSET_ID, value);
  }

  public String getPropName() {
    return this.propName;
  }

  public THudongPropTimer setPropName(String propName) {
    this.propName = propName;
    return this;
  }

  public void unsetPropName() {
    this.propName = null;
  }

  /** Returns true if field propName is set (has been assigned a value) and false otherwise */
  public boolean isSetPropName() {
    return this.propName != null;
  }

  public void setPropNameIsSet(boolean value) {
    if (!value) {
      this.propName = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((Integer)value);
      }
      break;

    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((Integer)value);
      }
      break;

    case PROP_ID:
      if (value == null) {
        unsetPropId();
      } else {
        setPropId((Integer)value);
      }
      break;

    case EXECUTE_TIME:
      if (value == null) {
        unsetExecuteTime();
      } else {
        setExecuteTime((Long)value);
      }
      break;

    case VISIBLE:
      if (value == null) {
        unsetVisible();
      } else {
        setVisible((Integer)value);
      }
      break;

    case CAN_BUY:
      if (value == null) {
        unsetCanBuy();
      } else {
        setCanBuy((Integer)value);
      }
      break;

    case SHIELD_PROP_WALL:
      if (value == null) {
        unsetShieldPropWall();
      } else {
        setShieldPropWall((Integer)value);
      }
      break;

    case PROP_NAME:
      if (value == null) {
        unsetPropName();
      } else {
        setPropName((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case APPID:
      return getAppid();

    case PROP_ID:
      return getPropId();

    case EXECUTE_TIME:
      return getExecuteTime();

    case VISIBLE:
      return getVisible();

    case CAN_BUY:
      return getCanBuy();

    case SHIELD_PROP_WALL:
      return getShieldPropWall();

    case PROP_NAME:
      return getPropName();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case APPID:
      return isSetAppid();
    case PROP_ID:
      return isSetPropId();
    case EXECUTE_TIME:
      return isSetExecuteTime();
    case VISIBLE:
      return isSetVisible();
    case CAN_BUY:
      return isSetCanBuy();
    case SHIELD_PROP_WALL:
      return isSetShieldPropWall();
    case PROP_NAME:
      return isSetPropName();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof THudongPropTimer)
      return this.equals((THudongPropTimer)that);
    return false;
  }

  public boolean equals(THudongPropTimer that) {
    if (that == null)
      return false;

    boolean this_present_id = true;
    boolean that_present_id = true;
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_appid = true;
    boolean that_present_appid = true;
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (this.appid != that.appid)
        return false;
    }

    boolean this_present_propId = true;
    boolean that_present_propId = true;
    if (this_present_propId || that_present_propId) {
      if (!(this_present_propId && that_present_propId))
        return false;
      if (this.propId != that.propId)
        return false;
    }

    boolean this_present_executeTime = true;
    boolean that_present_executeTime = true;
    if (this_present_executeTime || that_present_executeTime) {
      if (!(this_present_executeTime && that_present_executeTime))
        return false;
      if (this.executeTime != that.executeTime)
        return false;
    }

    boolean this_present_visible = true;
    boolean that_present_visible = true;
    if (this_present_visible || that_present_visible) {
      if (!(this_present_visible && that_present_visible))
        return false;
      if (this.visible != that.visible)
        return false;
    }

    boolean this_present_canBuy = true;
    boolean that_present_canBuy = true;
    if (this_present_canBuy || that_present_canBuy) {
      if (!(this_present_canBuy && that_present_canBuy))
        return false;
      if (this.canBuy != that.canBuy)
        return false;
    }

    boolean this_present_shieldPropWall = true;
    boolean that_present_shieldPropWall = true;
    if (this_present_shieldPropWall || that_present_shieldPropWall) {
      if (!(this_present_shieldPropWall && that_present_shieldPropWall))
        return false;
      if (this.shieldPropWall != that.shieldPropWall)
        return false;
    }

    boolean this_present_propName = true && this.isSetPropName();
    boolean that_present_propName = true && that.isSetPropName();
    if (this_present_propName || that_present_propName) {
      if (!(this_present_propName && that_present_propName))
        return false;
      if (!this.propName.equals(that.propName))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_id = true;
    list.add(present_id);
    if (present_id)
      list.add(id);

    boolean present_appid = true;
    list.add(present_appid);
    if (present_appid)
      list.add(appid);

    boolean present_propId = true;
    list.add(present_propId);
    if (present_propId)
      list.add(propId);

    boolean present_executeTime = true;
    list.add(present_executeTime);
    if (present_executeTime)
      list.add(executeTime);

    boolean present_visible = true;
    list.add(present_visible);
    if (present_visible)
      list.add(visible);

    boolean present_canBuy = true;
    list.add(present_canBuy);
    if (present_canBuy)
      list.add(canBuy);

    boolean present_shieldPropWall = true;
    list.add(present_shieldPropWall);
    if (present_shieldPropWall)
      list.add(shieldPropWall);

    boolean present_propName = true && (isSetPropName());
    list.add(present_propName);
    if (present_propName)
      list.add(propName);

    return list.hashCode();
  }

  @Override
  public int compareTo(THudongPropTimer other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPropId()).compareTo(other.isSetPropId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPropId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.propId, other.propId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExecuteTime()).compareTo(other.isSetExecuteTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExecuteTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.executeTime, other.executeTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetVisible()).compareTo(other.isSetVisible());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVisible()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.visible, other.visible);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCanBuy()).compareTo(other.isSetCanBuy());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCanBuy()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.canBuy, other.canBuy);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetShieldPropWall()).compareTo(other.isSetShieldPropWall());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetShieldPropWall()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.shieldPropWall, other.shieldPropWall);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPropName()).compareTo(other.isSetPropName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPropName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.propName, other.propName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("THudongPropTimer(");
    boolean first = true;

    sb.append("id:");
    sb.append(this.id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("appid:");
    sb.append(this.appid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("propId:");
    sb.append(this.propId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("executeTime:");
    sb.append(this.executeTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("visible:");
    sb.append(this.visible);
    first = false;
    if (!first) sb.append(", ");
    sb.append("canBuy:");
    sb.append(this.canBuy);
    first = false;
    if (!first) sb.append(", ");
    sb.append("shieldPropWall:");
    sb.append(this.shieldPropWall);
    first = false;
    if (!first) sb.append(", ");
    sb.append("propName:");
    if (this.propName == null) {
      sb.append("null");
    } else {
      sb.append(this.propName);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class THudongPropTimerStandardSchemeFactory implements SchemeFactory {
    public THudongPropTimerStandardScheme getScheme() {
      return new THudongPropTimerStandardScheme();
    }
  }

  private static class THudongPropTimerStandardScheme extends StandardScheme<THudongPropTimer> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, THudongPropTimer struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.id = iprot.readI32();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.appid = iprot.readI32();
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PROP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.propId = iprot.readI32();
              struct.setPropIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // EXECUTE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.executeTime = iprot.readI64();
              struct.setExecuteTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // VISIBLE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.visible = iprot.readI32();
              struct.setVisibleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // CAN_BUY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.canBuy = iprot.readI32();
              struct.setCanBuyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SHIELD_PROP_WALL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.shieldPropWall = iprot.readI32();
              struct.setShieldPropWallIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // PROP_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.propName = iprot.readString();
              struct.setPropNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, THudongPropTimer struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ID_FIELD_DESC);
      oprot.writeI32(struct.id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(APPID_FIELD_DESC);
      oprot.writeI32(struct.appid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PROP_ID_FIELD_DESC);
      oprot.writeI32(struct.propId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(EXECUTE_TIME_FIELD_DESC);
      oprot.writeI64(struct.executeTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(VISIBLE_FIELD_DESC);
      oprot.writeI32(struct.visible);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CAN_BUY_FIELD_DESC);
      oprot.writeI32(struct.canBuy);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SHIELD_PROP_WALL_FIELD_DESC);
      oprot.writeI32(struct.shieldPropWall);
      oprot.writeFieldEnd();
      if (struct.propName != null) {
        oprot.writeFieldBegin(PROP_NAME_FIELD_DESC);
        oprot.writeString(struct.propName);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class THudongPropTimerTupleSchemeFactory implements SchemeFactory {
    public THudongPropTimerTupleScheme getScheme() {
      return new THudongPropTimerTupleScheme();
    }
  }

  private static class THudongPropTimerTupleScheme extends TupleScheme<THudongPropTimer> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, THudongPropTimer struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetId()) {
        optionals.set(0);
      }
      if (struct.isSetAppid()) {
        optionals.set(1);
      }
      if (struct.isSetPropId()) {
        optionals.set(2);
      }
      if (struct.isSetExecuteTime()) {
        optionals.set(3);
      }
      if (struct.isSetVisible()) {
        optionals.set(4);
      }
      if (struct.isSetCanBuy()) {
        optionals.set(5);
      }
      if (struct.isSetShieldPropWall()) {
        optionals.set(6);
      }
      if (struct.isSetPropName()) {
        optionals.set(7);
      }
      oprot.writeBitSet(optionals, 8);
      if (struct.isSetId()) {
        oprot.writeI32(struct.id);
      }
      if (struct.isSetAppid()) {
        oprot.writeI32(struct.appid);
      }
      if (struct.isSetPropId()) {
        oprot.writeI32(struct.propId);
      }
      if (struct.isSetExecuteTime()) {
        oprot.writeI64(struct.executeTime);
      }
      if (struct.isSetVisible()) {
        oprot.writeI32(struct.visible);
      }
      if (struct.isSetCanBuy()) {
        oprot.writeI32(struct.canBuy);
      }
      if (struct.isSetShieldPropWall()) {
        oprot.writeI32(struct.shieldPropWall);
      }
      if (struct.isSetPropName()) {
        oprot.writeString(struct.propName);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, THudongPropTimer struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(8);
      if (incoming.get(0)) {
        struct.id = iprot.readI32();
        struct.setIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appid = iprot.readI32();
        struct.setAppidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.propId = iprot.readI32();
        struct.setPropIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.executeTime = iprot.readI64();
        struct.setExecuteTimeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.visible = iprot.readI32();
        struct.setVisibleIsSet(true);
      }
      if (incoming.get(5)) {
        struct.canBuy = iprot.readI32();
        struct.setCanBuyIsSet(true);
      }
      if (incoming.get(6)) {
        struct.shieldPropWall = iprot.readI32();
        struct.setShieldPropWallIsSet(true);
      }
      if (incoming.get(7)) {
        struct.propName = iprot.readString();
        struct.setPropNameIsSet(true);
      }
    }
  }

}

