/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.hdzt.ranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-11-17")
public class UserTaskItem implements org.apache.thrift.TBase<UserTaskItem, UserTaskItem._Fields>, java.io.Serializable, Cloneable, Comparable<UserTaskItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("UserTaskItem");

  private static final org.apache.thrift.protocol.TField CUR_TASK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("curTaskId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField CUR_TASK_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("curTaskScore", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField CUR_ROUND_FIELD_DESC = new org.apache.thrift.protocol.TField("curRound", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField CUR_TASK_SCORE_CONFIG_FIELD_DESC = new org.apache.thrift.protocol.TField("curTaskScoreConfig", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField ITEM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("itemId", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField ITEM_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("itemName", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField ITEM_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("itemUrl", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField ALL_TASK_SCORE_CONFIG_FIELD_DESC = new org.apache.thrift.protocol.TField("allTaskScoreConfig", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField ITEM_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("itemScore", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField ALL_TASK_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("allTaskCount", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField TASK_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("taskName", org.apache.thrift.protocol.TType.STRING, (short)11);
  private static final org.apache.thrift.protocol.TField TASK_EXT_JSON_FIELD_DESC = new org.apache.thrift.protocol.TField("taskExtJson", org.apache.thrift.protocol.TType.STRING, (short)12);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new UserTaskItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new UserTaskItemTupleSchemeFactory());
  }

  public long curTaskId; // required
  public long curTaskScore; // required
  public long curRound; // required
  public long curTaskScoreConfig; // required
  public String itemId; // required
  public String itemName; // required
  public String itemUrl; // required
  public long allTaskScoreConfig; // required
  public long itemScore; // required
  public long allTaskCount; // required
  public String taskName; // required
  public String taskExtJson; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    CUR_TASK_ID((short)1, "curTaskId"),
    CUR_TASK_SCORE((short)2, "curTaskScore"),
    CUR_ROUND((short)3, "curRound"),
    CUR_TASK_SCORE_CONFIG((short)4, "curTaskScoreConfig"),
    ITEM_ID((short)5, "itemId"),
    ITEM_NAME((short)6, "itemName"),
    ITEM_URL((short)7, "itemUrl"),
    ALL_TASK_SCORE_CONFIG((short)8, "allTaskScoreConfig"),
    ITEM_SCORE((short)9, "itemScore"),
    ALL_TASK_COUNT((short)10, "allTaskCount"),
    TASK_NAME((short)11, "taskName"),
    TASK_EXT_JSON((short)12, "taskExtJson"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CUR_TASK_ID
          return CUR_TASK_ID;
        case 2: // CUR_TASK_SCORE
          return CUR_TASK_SCORE;
        case 3: // CUR_ROUND
          return CUR_ROUND;
        case 4: // CUR_TASK_SCORE_CONFIG
          return CUR_TASK_SCORE_CONFIG;
        case 5: // ITEM_ID
          return ITEM_ID;
        case 6: // ITEM_NAME
          return ITEM_NAME;
        case 7: // ITEM_URL
          return ITEM_URL;
        case 8: // ALL_TASK_SCORE_CONFIG
          return ALL_TASK_SCORE_CONFIG;
        case 9: // ITEM_SCORE
          return ITEM_SCORE;
        case 10: // ALL_TASK_COUNT
          return ALL_TASK_COUNT;
        case 11: // TASK_NAME
          return TASK_NAME;
        case 12: // TASK_EXT_JSON
          return TASK_EXT_JSON;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CURTASKID_ISSET_ID = 0;
  private static final int __CURTASKSCORE_ISSET_ID = 1;
  private static final int __CURROUND_ISSET_ID = 2;
  private static final int __CURTASKSCORECONFIG_ISSET_ID = 3;
  private static final int __ALLTASKSCORECONFIG_ISSET_ID = 4;
  private static final int __ITEMSCORE_ISSET_ID = 5;
  private static final int __ALLTASKCOUNT_ISSET_ID = 6;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CUR_TASK_ID, new org.apache.thrift.meta_data.FieldMetaData("curTaskId", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CUR_TASK_SCORE, new org.apache.thrift.meta_data.FieldMetaData("curTaskScore", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CUR_ROUND, new org.apache.thrift.meta_data.FieldMetaData("curRound", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CUR_TASK_SCORE_CONFIG, new org.apache.thrift.meta_data.FieldMetaData("curTaskScoreConfig", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ITEM_ID, new org.apache.thrift.meta_data.FieldMetaData("itemId", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEM_NAME, new org.apache.thrift.meta_data.FieldMetaData("itemName", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEM_URL, new org.apache.thrift.meta_data.FieldMetaData("itemUrl", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ALL_TASK_SCORE_CONFIG, new org.apache.thrift.meta_data.FieldMetaData("allTaskScoreConfig", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ITEM_SCORE, new org.apache.thrift.meta_data.FieldMetaData("itemScore", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ALL_TASK_COUNT, new org.apache.thrift.meta_data.FieldMetaData("allTaskCount", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TASK_NAME, new org.apache.thrift.meta_data.FieldMetaData("taskName", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TASK_EXT_JSON, new org.apache.thrift.meta_data.FieldMetaData("taskExtJson", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING),
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(UserTaskItem.class, metaDataMap);
  }

  public UserTaskItem() {
  }

  public UserTaskItem(
    long curTaskId,
    long curTaskScore,
    long curRound,
    long curTaskScoreConfig,
    String itemId,
    String itemName,
    String itemUrl,
    long allTaskScoreConfig,
    long itemScore,
    long allTaskCount,
    String taskName,
    String taskExtJson,
    Map<String,String> extData)
  {
    this();
    this.curTaskId = curTaskId;
    setCurTaskIdIsSet(true);
    this.curTaskScore = curTaskScore;
    setCurTaskScoreIsSet(true);
    this.curRound = curRound;
    setCurRoundIsSet(true);
    this.curTaskScoreConfig = curTaskScoreConfig;
    setCurTaskScoreConfigIsSet(true);
    this.itemId = itemId;
    this.itemName = itemName;
    this.itemUrl = itemUrl;
    this.allTaskScoreConfig = allTaskScoreConfig;
    setAllTaskScoreConfigIsSet(true);
    this.itemScore = itemScore;
    setItemScoreIsSet(true);
    this.allTaskCount = allTaskCount;
    setAllTaskCountIsSet(true);
    this.taskName = taskName;
    this.taskExtJson = taskExtJson;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public UserTaskItem(UserTaskItem other) {
    __isset_bitfield = other.__isset_bitfield;
    this.curTaskId = other.curTaskId;
    this.curTaskScore = other.curTaskScore;
    this.curRound = other.curRound;
    this.curTaskScoreConfig = other.curTaskScoreConfig;
    if (other.isSetItemId()) {
      this.itemId = other.itemId;
    }
    if (other.isSetItemName()) {
      this.itemName = other.itemName;
    }
    if (other.isSetItemUrl()) {
      this.itemUrl = other.itemUrl;
    }
    this.allTaskScoreConfig = other.allTaskScoreConfig;
    this.itemScore = other.itemScore;
    this.allTaskCount = other.allTaskCount;
    if (other.isSetTaskName()) {
      this.taskName = other.taskName;
    }
    if (other.isSetTaskExtJson()) {
      this.taskExtJson = other.taskExtJson;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public UserTaskItem deepCopy() {
    return new UserTaskItem(this);
  }

  @Override
  public void clear() {
    setCurTaskIdIsSet(false);
    this.curTaskId = 0;
    setCurTaskScoreIsSet(false);
    this.curTaskScore = 0;
    setCurRoundIsSet(false);
    this.curRound = 0;
    setCurTaskScoreConfigIsSet(false);
    this.curTaskScoreConfig = 0;
    this.itemId = null;
    this.itemName = null;
    this.itemUrl = null;
    setAllTaskScoreConfigIsSet(false);
    this.allTaskScoreConfig = 0;
    setItemScoreIsSet(false);
    this.itemScore = 0;
    setAllTaskCountIsSet(false);
    this.allTaskCount = 0;
    this.taskName = null;
    this.taskExtJson = null;
    this.extData = null;
  }

  public long getCurTaskId() {
    return this.curTaskId;
  }

  public UserTaskItem setCurTaskId(long curTaskId) {
    this.curTaskId = curTaskId;
    setCurTaskIdIsSet(true);
    return this;
  }

  public void unsetCurTaskId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURTASKID_ISSET_ID);
  }

  /** Returns true if field curTaskId is set (has been assigned a value) and false otherwise */
  public boolean isSetCurTaskId() {
    return EncodingUtils.testBit(__isset_bitfield, __CURTASKID_ISSET_ID);
  }

  public void setCurTaskIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURTASKID_ISSET_ID, value);
  }

  public long getCurTaskScore() {
    return this.curTaskScore;
  }

  public UserTaskItem setCurTaskScore(long curTaskScore) {
    this.curTaskScore = curTaskScore;
    setCurTaskScoreIsSet(true);
    return this;
  }

  public void unsetCurTaskScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURTASKSCORE_ISSET_ID);
  }

  /** Returns true if field curTaskScore is set (has been assigned a value) and false otherwise */
  public boolean isSetCurTaskScore() {
    return EncodingUtils.testBit(__isset_bitfield, __CURTASKSCORE_ISSET_ID);
  }

  public void setCurTaskScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURTASKSCORE_ISSET_ID, value);
  }

  public long getCurRound() {
    return this.curRound;
  }

  public UserTaskItem setCurRound(long curRound) {
    this.curRound = curRound;
    setCurRoundIsSet(true);
    return this;
  }

  public void unsetCurRound() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURROUND_ISSET_ID);
  }

  /** Returns true if field curRound is set (has been assigned a value) and false otherwise */
  public boolean isSetCurRound() {
    return EncodingUtils.testBit(__isset_bitfield, __CURROUND_ISSET_ID);
  }

  public void setCurRoundIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURROUND_ISSET_ID, value);
  }

  public long getCurTaskScoreConfig() {
    return this.curTaskScoreConfig;
  }

  public UserTaskItem setCurTaskScoreConfig(long curTaskScoreConfig) {
    this.curTaskScoreConfig = curTaskScoreConfig;
    setCurTaskScoreConfigIsSet(true);
    return this;
  }

  public void unsetCurTaskScoreConfig() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURTASKSCORECONFIG_ISSET_ID);
  }

  /** Returns true if field curTaskScoreConfig is set (has been assigned a value) and false otherwise */
  public boolean isSetCurTaskScoreConfig() {
    return EncodingUtils.testBit(__isset_bitfield, __CURTASKSCORECONFIG_ISSET_ID);
  }

  public void setCurTaskScoreConfigIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURTASKSCORECONFIG_ISSET_ID, value);
  }

  public String getItemId() {
    return this.itemId;
  }

  public UserTaskItem setItemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

  public void unsetItemId() {
    this.itemId = null;
  }

  /** Returns true if field itemId is set (has been assigned a value) and false otherwise */
  public boolean isSetItemId() {
    return this.itemId != null;
  }

  public void setItemIdIsSet(boolean value) {
    if (!value) {
      this.itemId = null;
    }
  }

  public String getItemName() {
    return this.itemName;
  }

  public UserTaskItem setItemName(String itemName) {
    this.itemName = itemName;
    return this;
  }

  public void unsetItemName() {
    this.itemName = null;
  }

  /** Returns true if field itemName is set (has been assigned a value) and false otherwise */
  public boolean isSetItemName() {
    return this.itemName != null;
  }

  public void setItemNameIsSet(boolean value) {
    if (!value) {
      this.itemName = null;
    }
  }

  public String getItemUrl() {
    return this.itemUrl;
  }

  public UserTaskItem setItemUrl(String itemUrl) {
    this.itemUrl = itemUrl;
    return this;
  }

  public void unsetItemUrl() {
    this.itemUrl = null;
  }

  /** Returns true if field itemUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetItemUrl() {
    return this.itemUrl != null;
  }

  public void setItemUrlIsSet(boolean value) {
    if (!value) {
      this.itemUrl = null;
    }
  }

  public long getAllTaskScoreConfig() {
    return this.allTaskScoreConfig;
  }

  public UserTaskItem setAllTaskScoreConfig(long allTaskScoreConfig) {
    this.allTaskScoreConfig = allTaskScoreConfig;
    setAllTaskScoreConfigIsSet(true);
    return this;
  }

  public void unsetAllTaskScoreConfig() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ALLTASKSCORECONFIG_ISSET_ID);
  }

  /** Returns true if field allTaskScoreConfig is set (has been assigned a value) and false otherwise */
  public boolean isSetAllTaskScoreConfig() {
    return EncodingUtils.testBit(__isset_bitfield, __ALLTASKSCORECONFIG_ISSET_ID);
  }

  public void setAllTaskScoreConfigIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ALLTASKSCORECONFIG_ISSET_ID, value);
  }

  public long getItemScore() {
    return this.itemScore;
  }

  public UserTaskItem setItemScore(long itemScore) {
    this.itemScore = itemScore;
    setItemScoreIsSet(true);
    return this;
  }

  public void unsetItemScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ITEMSCORE_ISSET_ID);
  }

  /** Returns true if field itemScore is set (has been assigned a value) and false otherwise */
  public boolean isSetItemScore() {
    return EncodingUtils.testBit(__isset_bitfield, __ITEMSCORE_ISSET_ID);
  }

  public void setItemScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ITEMSCORE_ISSET_ID, value);
  }

  public long getAllTaskCount() {
    return this.allTaskCount;
  }

  public UserTaskItem setAllTaskCount(long allTaskCount) {
    this.allTaskCount = allTaskCount;
    setAllTaskCountIsSet(true);
    return this;
  }

  public void unsetAllTaskCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ALLTASKCOUNT_ISSET_ID);
  }

  /** Returns true if field allTaskCount is set (has been assigned a value) and false otherwise */
  public boolean isSetAllTaskCount() {
    return EncodingUtils.testBit(__isset_bitfield, __ALLTASKCOUNT_ISSET_ID);
  }

  public void setAllTaskCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ALLTASKCOUNT_ISSET_ID, value);
  }

  public String getTaskName() {
    return this.taskName;
  }

  public UserTaskItem setTaskName(String taskName) {
    this.taskName = taskName;
    return this;
  }

  public void unsetTaskName() {
    this.taskName = null;
  }

  /** Returns true if field taskName is set (has been assigned a value) and false otherwise */
  public boolean isSetTaskName() {
    return this.taskName != null;
  }

  public void setTaskNameIsSet(boolean value) {
    if (!value) {
      this.taskName = null;
    }
  }

  public String getTaskExtJson() {
    return this.taskExtJson;
  }

  public UserTaskItem setTaskExtJson(String taskExtJson) {
    this.taskExtJson = taskExtJson;
    return this;
  }

  public void unsetTaskExtJson() {
    this.taskExtJson = null;
  }

  /** Returns true if field taskExtJson is set (has been assigned a value) and false otherwise */
  public boolean isSetTaskExtJson() {
    return this.taskExtJson != null;
  }

  public void setTaskExtJsonIsSet(boolean value) {
    if (!value) {
      this.taskExtJson = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public UserTaskItem setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CUR_TASK_ID:
      if (value == null) {
        unsetCurTaskId();
      } else {
        setCurTaskId((Long)value);
      }
      break;

    case CUR_TASK_SCORE:
      if (value == null) {
        unsetCurTaskScore();
      } else {
        setCurTaskScore((Long)value);
      }
      break;

    case CUR_ROUND:
      if (value == null) {
        unsetCurRound();
      } else {
        setCurRound((Long)value);
      }
      break;

    case CUR_TASK_SCORE_CONFIG:
      if (value == null) {
        unsetCurTaskScoreConfig();
      } else {
        setCurTaskScoreConfig((Long)value);
      }
      break;

    case ITEM_ID:
      if (value == null) {
        unsetItemId();
      } else {
        setItemId((String)value);
      }
      break;

    case ITEM_NAME:
      if (value == null) {
        unsetItemName();
      } else {
        setItemName((String)value);
      }
      break;

    case ITEM_URL:
      if (value == null) {
        unsetItemUrl();
      } else {
        setItemUrl((String)value);
      }
      break;

    case ALL_TASK_SCORE_CONFIG:
      if (value == null) {
        unsetAllTaskScoreConfig();
      } else {
        setAllTaskScoreConfig((Long)value);
      }
      break;

    case ITEM_SCORE:
      if (value == null) {
        unsetItemScore();
      } else {
        setItemScore((Long)value);
      }
      break;

    case ALL_TASK_COUNT:
      if (value == null) {
        unsetAllTaskCount();
      } else {
        setAllTaskCount((Long)value);
      }
      break;

    case TASK_NAME:
      if (value == null) {
        unsetTaskName();
      } else {
        setTaskName((String)value);
      }
      break;

    case TASK_EXT_JSON:
      if (value == null) {
        unsetTaskExtJson();
      } else {
        setTaskExtJson((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CUR_TASK_ID:
      return getCurTaskId();

    case CUR_TASK_SCORE:
      return getCurTaskScore();

    case CUR_ROUND:
      return getCurRound();

    case CUR_TASK_SCORE_CONFIG:
      return getCurTaskScoreConfig();

    case ITEM_ID:
      return getItemId();

    case ITEM_NAME:
      return getItemName();

    case ITEM_URL:
      return getItemUrl();

    case ALL_TASK_SCORE_CONFIG:
      return getAllTaskScoreConfig();

    case ITEM_SCORE:
      return getItemScore();

    case ALL_TASK_COUNT:
      return getAllTaskCount();

    case TASK_NAME:
      return getTaskName();

    case TASK_EXT_JSON:
      return getTaskExtJson();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CUR_TASK_ID:
      return isSetCurTaskId();
    case CUR_TASK_SCORE:
      return isSetCurTaskScore();
    case CUR_ROUND:
      return isSetCurRound();
    case CUR_TASK_SCORE_CONFIG:
      return isSetCurTaskScoreConfig();
    case ITEM_ID:
      return isSetItemId();
    case ITEM_NAME:
      return isSetItemName();
    case ITEM_URL:
      return isSetItemUrl();
    case ALL_TASK_SCORE_CONFIG:
      return isSetAllTaskScoreConfig();
    case ITEM_SCORE:
      return isSetItemScore();
    case ALL_TASK_COUNT:
      return isSetAllTaskCount();
    case TASK_NAME:
      return isSetTaskName();
    case TASK_EXT_JSON:
      return isSetTaskExtJson();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof UserTaskItem)
      return this.equals((UserTaskItem)that);
    return false;
  }

  public boolean equals(UserTaskItem that) {
    if (that == null)
      return false;

    boolean this_present_curTaskId = true;
    boolean that_present_curTaskId = true;
    if (this_present_curTaskId || that_present_curTaskId) {
      if (!(this_present_curTaskId && that_present_curTaskId))
        return false;
      if (this.curTaskId != that.curTaskId)
        return false;
    }

    boolean this_present_curTaskScore = true;
    boolean that_present_curTaskScore = true;
    if (this_present_curTaskScore || that_present_curTaskScore) {
      if (!(this_present_curTaskScore && that_present_curTaskScore))
        return false;
      if (this.curTaskScore != that.curTaskScore)
        return false;
    }

    boolean this_present_curRound = true;
    boolean that_present_curRound = true;
    if (this_present_curRound || that_present_curRound) {
      if (!(this_present_curRound && that_present_curRound))
        return false;
      if (this.curRound != that.curRound)
        return false;
    }

    boolean this_present_curTaskScoreConfig = true;
    boolean that_present_curTaskScoreConfig = true;
    if (this_present_curTaskScoreConfig || that_present_curTaskScoreConfig) {
      if (!(this_present_curTaskScoreConfig && that_present_curTaskScoreConfig))
        return false;
      if (this.curTaskScoreConfig != that.curTaskScoreConfig)
        return false;
    }

    boolean this_present_itemId = true && this.isSetItemId();
    boolean that_present_itemId = true && that.isSetItemId();
    if (this_present_itemId || that_present_itemId) {
      if (!(this_present_itemId && that_present_itemId))
        return false;
      if (!this.itemId.equals(that.itemId))
        return false;
    }

    boolean this_present_itemName = true && this.isSetItemName();
    boolean that_present_itemName = true && that.isSetItemName();
    if (this_present_itemName || that_present_itemName) {
      if (!(this_present_itemName && that_present_itemName))
        return false;
      if (!this.itemName.equals(that.itemName))
        return false;
    }

    boolean this_present_itemUrl = true && this.isSetItemUrl();
    boolean that_present_itemUrl = true && that.isSetItemUrl();
    if (this_present_itemUrl || that_present_itemUrl) {
      if (!(this_present_itemUrl && that_present_itemUrl))
        return false;
      if (!this.itemUrl.equals(that.itemUrl))
        return false;
    }

    boolean this_present_allTaskScoreConfig = true;
    boolean that_present_allTaskScoreConfig = true;
    if (this_present_allTaskScoreConfig || that_present_allTaskScoreConfig) {
      if (!(this_present_allTaskScoreConfig && that_present_allTaskScoreConfig))
        return false;
      if (this.allTaskScoreConfig != that.allTaskScoreConfig)
        return false;
    }

    boolean this_present_itemScore = true;
    boolean that_present_itemScore = true;
    if (this_present_itemScore || that_present_itemScore) {
      if (!(this_present_itemScore && that_present_itemScore))
        return false;
      if (this.itemScore != that.itemScore)
        return false;
    }

    boolean this_present_allTaskCount = true;
    boolean that_present_allTaskCount = true;
    if (this_present_allTaskCount || that_present_allTaskCount) {
      if (!(this_present_allTaskCount && that_present_allTaskCount))
        return false;
      if (this.allTaskCount != that.allTaskCount)
        return false;
    }

    boolean this_present_taskName = true && this.isSetTaskName();
    boolean that_present_taskName = true && that.isSetTaskName();
    if (this_present_taskName || that_present_taskName) {
      if (!(this_present_taskName && that_present_taskName))
        return false;
      if (!this.taskName.equals(that.taskName))
        return false;
    }

    boolean this_present_taskExtJson = true && this.isSetTaskExtJson();
    boolean that_present_taskExtJson = true && that.isSetTaskExtJson();
    if (this_present_taskExtJson || that_present_taskExtJson) {
      if (!(this_present_taskExtJson && that_present_taskExtJson))
        return false;
      if (!this.taskExtJson.equals(that.taskExtJson))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_curTaskId = true;
    list.add(present_curTaskId);
    if (present_curTaskId)
      list.add(curTaskId);

    boolean present_curTaskScore = true;
    list.add(present_curTaskScore);
    if (present_curTaskScore)
      list.add(curTaskScore);

    boolean present_curRound = true;
    list.add(present_curRound);
    if (present_curRound)
      list.add(curRound);

    boolean present_curTaskScoreConfig = true;
    list.add(present_curTaskScoreConfig);
    if (present_curTaskScoreConfig)
      list.add(curTaskScoreConfig);

    boolean present_itemId = true && (isSetItemId());
    list.add(present_itemId);
    if (present_itemId)
      list.add(itemId);

    boolean present_itemName = true && (isSetItemName());
    list.add(present_itemName);
    if (present_itemName)
      list.add(itemName);

    boolean present_itemUrl = true && (isSetItemUrl());
    list.add(present_itemUrl);
    if (present_itemUrl)
      list.add(itemUrl);

    boolean present_allTaskScoreConfig = true;
    list.add(present_allTaskScoreConfig);
    if (present_allTaskScoreConfig)
      list.add(allTaskScoreConfig);

    boolean present_itemScore = true;
    list.add(present_itemScore);
    if (present_itemScore)
      list.add(itemScore);

    boolean present_allTaskCount = true;
    list.add(present_allTaskCount);
    if (present_allTaskCount)
      list.add(allTaskCount);

    boolean present_taskName = true && (isSetTaskName());
    list.add(present_taskName);
    if (present_taskName)
      list.add(taskName);

    boolean present_taskExtJson = true && (isSetTaskExtJson());
    list.add(present_taskExtJson);
    if (present_taskExtJson)
      list.add(taskExtJson);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(UserTaskItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetCurTaskId()).compareTo(other.isSetCurTaskId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurTaskId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curTaskId, other.curTaskId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurTaskScore()).compareTo(other.isSetCurTaskScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurTaskScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curTaskScore, other.curTaskScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurRound()).compareTo(other.isSetCurRound());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurRound()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curRound, other.curRound);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurTaskScoreConfig()).compareTo(other.isSetCurTaskScoreConfig());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurTaskScoreConfig()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curTaskScoreConfig, other.curTaskScoreConfig);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemId()).compareTo(other.isSetItemId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemId, other.itemId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemName()).compareTo(other.isSetItemName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemName, other.itemName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemUrl()).compareTo(other.isSetItemUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemUrl, other.itemUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAllTaskScoreConfig()).compareTo(other.isSetAllTaskScoreConfig());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllTaskScoreConfig()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allTaskScoreConfig, other.allTaskScoreConfig);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemScore()).compareTo(other.isSetItemScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemScore, other.itemScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAllTaskCount()).compareTo(other.isSetAllTaskCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllTaskCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allTaskCount, other.allTaskCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTaskName()).compareTo(other.isSetTaskName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTaskName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.taskName, other.taskName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTaskExtJson()).compareTo(other.isSetTaskExtJson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTaskExtJson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.taskExtJson, other.taskExtJson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("UserTaskItem(");
    boolean first = true;

    sb.append("curTaskId:");
    sb.append(this.curTaskId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("curTaskScore:");
    sb.append(this.curTaskScore);
    first = false;
    if (!first) sb.append(", ");
    sb.append("curRound:");
    sb.append(this.curRound);
    first = false;
    if (!first) sb.append(", ");
    sb.append("curTaskScoreConfig:");
    sb.append(this.curTaskScoreConfig);
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemId:");
    if (this.itemId == null) {
      sb.append("null");
    } else {
      sb.append(this.itemId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemName:");
    if (this.itemName == null) {
      sb.append("null");
    } else {
      sb.append(this.itemName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemUrl:");
    if (this.itemUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.itemUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("allTaskScoreConfig:");
    sb.append(this.allTaskScoreConfig);
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemScore:");
    sb.append(this.itemScore);
    first = false;
    if (!first) sb.append(", ");
    sb.append("allTaskCount:");
    sb.append(this.allTaskCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("taskName:");
    if (this.taskName == null) {
      sb.append("null");
    } else {
      sb.append(this.taskName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("taskExtJson:");
    if (this.taskExtJson == null) {
      sb.append("null");
    } else {
      sb.append(this.taskExtJson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class UserTaskItemStandardSchemeFactory implements SchemeFactory {
    public UserTaskItemStandardScheme getScheme() {
      return new UserTaskItemStandardScheme();
    }
  }

  private static class UserTaskItemStandardScheme extends StandardScheme<UserTaskItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, UserTaskItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // CUR_TASK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.curTaskId = iprot.readI64();
              struct.setCurTaskIdIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // CUR_TASK_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.curTaskScore = iprot.readI64();
              struct.setCurTaskScoreIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CUR_ROUND
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.curRound = iprot.readI64();
              struct.setCurRoundIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CUR_TASK_SCORE_CONFIG
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.curTaskScoreConfig = iprot.readI64();
              struct.setCurTaskScoreConfigIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ITEM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemId = iprot.readString();
              struct.setItemIdIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // ITEM_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemName = iprot.readString();
              struct.setItemNameIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // ITEM_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemUrl = iprot.readString();
              struct.setItemUrlIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // ALL_TASK_SCORE_CONFIG
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.allTaskScoreConfig = iprot.readI64();
              struct.setAllTaskScoreConfigIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // ITEM_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.itemScore = iprot.readI64();
              struct.setItemScoreIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // ALL_TASK_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.allTaskCount = iprot.readI64();
              struct.setAllTaskCountIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // TASK_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.taskName = iprot.readString();
              struct.setTaskNameIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // TASK_EXT_JSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.taskExtJson = iprot.readString();
              struct.setTaskExtJsonIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map472 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map472.size);
                String _key473;
                String _val474;
                for (int _i475 = 0; _i475 < _map472.size; ++_i475)
                {
                  _key473 = iprot.readString();
                  _val474 = iprot.readString();
                  struct.extData.put(_key473, _val474);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, UserTaskItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(CUR_TASK_ID_FIELD_DESC);
      oprot.writeI64(struct.curTaskId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CUR_TASK_SCORE_FIELD_DESC);
      oprot.writeI64(struct.curTaskScore);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CUR_ROUND_FIELD_DESC);
      oprot.writeI64(struct.curRound);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CUR_TASK_SCORE_CONFIG_FIELD_DESC);
      oprot.writeI64(struct.curTaskScoreConfig);
      oprot.writeFieldEnd();
      if (struct.itemId != null) {
        oprot.writeFieldBegin(ITEM_ID_FIELD_DESC);
        oprot.writeString(struct.itemId);
        oprot.writeFieldEnd();
      }
      if (struct.itemName != null) {
        oprot.writeFieldBegin(ITEM_NAME_FIELD_DESC);
        oprot.writeString(struct.itemName);
        oprot.writeFieldEnd();
      }
      if (struct.itemUrl != null) {
        oprot.writeFieldBegin(ITEM_URL_FIELD_DESC);
        oprot.writeString(struct.itemUrl);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ALL_TASK_SCORE_CONFIG_FIELD_DESC);
      oprot.writeI64(struct.allTaskScoreConfig);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ITEM_SCORE_FIELD_DESC);
      oprot.writeI64(struct.itemScore);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ALL_TASK_COUNT_FIELD_DESC);
      oprot.writeI64(struct.allTaskCount);
      oprot.writeFieldEnd();
      if (struct.taskName != null) {
        oprot.writeFieldBegin(TASK_NAME_FIELD_DESC);
        oprot.writeString(struct.taskName);
        oprot.writeFieldEnd();
      }
      if (struct.taskExtJson != null) {
        oprot.writeFieldBegin(TASK_EXT_JSON_FIELD_DESC);
        oprot.writeString(struct.taskExtJson);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter476 : struct.extData.entrySet())
          {
            oprot.writeString(_iter476.getKey());
            oprot.writeString(_iter476.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class UserTaskItemTupleSchemeFactory implements SchemeFactory {
    public UserTaskItemTupleScheme getScheme() {
      return new UserTaskItemTupleScheme();
    }
  }

  private static class UserTaskItemTupleScheme extends TupleScheme<UserTaskItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, UserTaskItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetCurTaskId()) {
        optionals.set(0);
      }
      if (struct.isSetCurTaskScore()) {
        optionals.set(1);
      }
      if (struct.isSetCurRound()) {
        optionals.set(2);
      }
      if (struct.isSetCurTaskScoreConfig()) {
        optionals.set(3);
      }
      if (struct.isSetItemId()) {
        optionals.set(4);
      }
      if (struct.isSetItemName()) {
        optionals.set(5);
      }
      if (struct.isSetItemUrl()) {
        optionals.set(6);
      }
      if (struct.isSetAllTaskScoreConfig()) {
        optionals.set(7);
      }
      if (struct.isSetItemScore()) {
        optionals.set(8);
      }
      if (struct.isSetAllTaskCount()) {
        optionals.set(9);
      }
      if (struct.isSetTaskName()) {
        optionals.set(10);
      }
      if (struct.isSetTaskExtJson()) {
        optionals.set(11);
      }
      if (struct.isSetExtData()) {
        optionals.set(12);
      }
      oprot.writeBitSet(optionals, 13);
      if (struct.isSetCurTaskId()) {
        oprot.writeI64(struct.curTaskId);
      }
      if (struct.isSetCurTaskScore()) {
        oprot.writeI64(struct.curTaskScore);
      }
      if (struct.isSetCurRound()) {
        oprot.writeI64(struct.curRound);
      }
      if (struct.isSetCurTaskScoreConfig()) {
        oprot.writeI64(struct.curTaskScoreConfig);
      }
      if (struct.isSetItemId()) {
        oprot.writeString(struct.itemId);
      }
      if (struct.isSetItemName()) {
        oprot.writeString(struct.itemName);
      }
      if (struct.isSetItemUrl()) {
        oprot.writeString(struct.itemUrl);
      }
      if (struct.isSetAllTaskScoreConfig()) {
        oprot.writeI64(struct.allTaskScoreConfig);
      }
      if (struct.isSetItemScore()) {
        oprot.writeI64(struct.itemScore);
      }
      if (struct.isSetAllTaskCount()) {
        oprot.writeI64(struct.allTaskCount);
      }
      if (struct.isSetTaskName()) {
        oprot.writeString(struct.taskName);
      }
      if (struct.isSetTaskExtJson()) {
        oprot.writeString(struct.taskExtJson);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter477 : struct.extData.entrySet())
          {
            oprot.writeString(_iter477.getKey());
            oprot.writeString(_iter477.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, UserTaskItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(13);
      if (incoming.get(0)) {
        struct.curTaskId = iprot.readI64();
        struct.setCurTaskIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.curTaskScore = iprot.readI64();
        struct.setCurTaskScoreIsSet(true);
      }
      if (incoming.get(2)) {
        struct.curRound = iprot.readI64();
        struct.setCurRoundIsSet(true);
      }
      if (incoming.get(3)) {
        struct.curTaskScoreConfig = iprot.readI64();
        struct.setCurTaskScoreConfigIsSet(true);
      }
      if (incoming.get(4)) {
        struct.itemId = iprot.readString();
        struct.setItemIdIsSet(true);
      }
      if (incoming.get(5)) {
        struct.itemName = iprot.readString();
        struct.setItemNameIsSet(true);
      }
      if (incoming.get(6)) {
        struct.itemUrl = iprot.readString();
        struct.setItemUrlIsSet(true);
      }
      if (incoming.get(7)) {
        struct.allTaskScoreConfig = iprot.readI64();
        struct.setAllTaskScoreConfigIsSet(true);
      }
      if (incoming.get(8)) {
        struct.itemScore = iprot.readI64();
        struct.setItemScoreIsSet(true);
      }
      if (incoming.get(9)) {
        struct.allTaskCount = iprot.readI64();
        struct.setAllTaskCountIsSet(true);
      }
      if (incoming.get(10)) {
        struct.taskName = iprot.readString();
        struct.setTaskNameIsSet(true);
      }
      if (incoming.get(11)) {
        struct.taskExtJson = iprot.readString();
        struct.setTaskExtJsonIsSet(true);
      }
      if (incoming.get(12)) {
        {
          org.apache.thrift.protocol.TMap _map478 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map478.size);
          String _key479;
          String _val480;
          for (int _i481 = 0; _i481 < _map478.size; ++_i481)
          {
            _key479 = iprot.readString();
            _val480 = iprot.readString();
            struct.extData.put(_key479, _val480);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

