/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.fts_group_center;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2025-05-24")
public class BatchQueryGroupResp implements org.apache.thrift.TBase<BatchQueryGroupResp, BatchQueryGroupResp._Fields>, java.io.Serializable, Cloneable, Comparable<BatchQueryGroupResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BatchQueryGroupResp");

  private static final org.apache.thrift.protocol.TField HEADER_FIELD_DESC = new org.apache.thrift.protocol.TField("header", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField RESPONSE_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("response_list", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BatchQueryGroupRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BatchQueryGroupRespTupleSchemeFactory());
  }

  public CommonRet header; // required
  public List<QueryGroupResp> response_list; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    HEADER((short)1, "header"),
    RESPONSE_LIST((short)2, "response_list");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // HEADER
          return HEADER;
        case 2: // RESPONSE_LIST
          return RESPONSE_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.HEADER, new org.apache.thrift.meta_data.FieldMetaData("header", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CommonRet.class)));
    tmpMap.put(_Fields.RESPONSE_LIST, new org.apache.thrift.meta_data.FieldMetaData("response_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "QueryGroupResp"))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BatchQueryGroupResp.class, metaDataMap);
  }

  public BatchQueryGroupResp() {
  }

  public BatchQueryGroupResp(
    CommonRet header,
    List<QueryGroupResp> response_list)
  {
    this();
    this.header = header;
    this.response_list = response_list;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BatchQueryGroupResp(BatchQueryGroupResp other) {
    if (other.isSetHeader()) {
      this.header = new CommonRet(other.header);
    }
    if (other.isSetResponse_list()) {
      List<QueryGroupResp> __this__response_list = new ArrayList<QueryGroupResp>(other.response_list.size());
      for (QueryGroupResp other_element : other.response_list) {
        __this__response_list.add(other_element);
      }
      this.response_list = __this__response_list;
    }
  }

  public BatchQueryGroupResp deepCopy() {
    return new BatchQueryGroupResp(this);
  }

  @Override
  public void clear() {
    this.header = null;
    this.response_list = null;
  }

  public CommonRet getHeader() {
    return this.header;
  }

  public BatchQueryGroupResp setHeader(CommonRet header) {
    this.header = header;
    return this;
  }

  public void unsetHeader() {
    this.header = null;
  }

  /** Returns true if field header is set (has been assigned a value) and false otherwise */
  public boolean isSetHeader() {
    return this.header != null;
  }

  public void setHeaderIsSet(boolean value) {
    if (!value) {
      this.header = null;
    }
  }

  public int getResponse_listSize() {
    return (this.response_list == null) ? 0 : this.response_list.size();
  }

  public java.util.Iterator<QueryGroupResp> getResponse_listIterator() {
    return (this.response_list == null) ? null : this.response_list.iterator();
  }

  public void addToResponse_list(QueryGroupResp elem) {
    if (this.response_list == null) {
      this.response_list = new ArrayList<QueryGroupResp>();
    }
    this.response_list.add(elem);
  }

  public List<QueryGroupResp> getResponse_list() {
    return this.response_list;
  }

  public BatchQueryGroupResp setResponse_list(List<QueryGroupResp> response_list) {
    this.response_list = response_list;
    return this;
  }

  public void unsetResponse_list() {
    this.response_list = null;
  }

  /** Returns true if field response_list is set (has been assigned a value) and false otherwise */
  public boolean isSetResponse_list() {
    return this.response_list != null;
  }

  public void setResponse_listIsSet(boolean value) {
    if (!value) {
      this.response_list = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case HEADER:
      if (value == null) {
        unsetHeader();
      } else {
        setHeader((CommonRet)value);
      }
      break;

    case RESPONSE_LIST:
      if (value == null) {
        unsetResponse_list();
      } else {
        setResponse_list((List<QueryGroupResp>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case HEADER:
      return getHeader();

    case RESPONSE_LIST:
      return getResponse_list();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case HEADER:
      return isSetHeader();
    case RESPONSE_LIST:
      return isSetResponse_list();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BatchQueryGroupResp)
      return this.equals((BatchQueryGroupResp)that);
    return false;
  }

  public boolean equals(BatchQueryGroupResp that) {
    if (that == null)
      return false;

    boolean this_present_header = true && this.isSetHeader();
    boolean that_present_header = true && that.isSetHeader();
    if (this_present_header || that_present_header) {
      if (!(this_present_header && that_present_header))
        return false;
      if (!this.header.equals(that.header))
        return false;
    }

    boolean this_present_response_list = true && this.isSetResponse_list();
    boolean that_present_response_list = true && that.isSetResponse_list();
    if (this_present_response_list || that_present_response_list) {
      if (!(this_present_response_list && that_present_response_list))
        return false;
      if (!this.response_list.equals(that.response_list))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_header = true && (isSetHeader());
    list.add(present_header);
    if (present_header)
      list.add(header);

    boolean present_response_list = true && (isSetResponse_list());
    list.add(present_response_list);
    if (present_response_list)
      list.add(response_list);

    return list.hashCode();
  }

  @Override
  public int compareTo(BatchQueryGroupResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetHeader()).compareTo(other.isSetHeader());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeader()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.header, other.header);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetResponse_list()).compareTo(other.isSetResponse_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResponse_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.response_list, other.response_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BatchQueryGroupResp(");
    boolean first = true;

    sb.append("header:");
    if (this.header == null) {
      sb.append("null");
    } else {
      sb.append(this.header);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("response_list:");
    if (this.response_list == null) {
      sb.append("null");
    } else {
      sb.append(this.response_list);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (header != null) {
      header.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BatchQueryGroupRespStandardSchemeFactory implements SchemeFactory {
    public BatchQueryGroupRespStandardScheme getScheme() {
      return new BatchQueryGroupRespStandardScheme();
    }
  }

  private static class BatchQueryGroupRespStandardScheme extends StandardScheme<BatchQueryGroupResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BatchQueryGroupResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // HEADER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.header = new CommonRet();
              struct.header.read(iprot);
              struct.setHeaderIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RESPONSE_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list26 = iprot.readListBegin();
                struct.response_list = new ArrayList<QueryGroupResp>(_list26.size);
                QueryGroupResp _elem27;
                for (int _i28 = 0; _i28 < _list26.size; ++_i28)
                {
                  _elem27 = new QueryGroupResp();
                  _elem27.read(iprot);
                  struct.response_list.add(_elem27);
                }
                iprot.readListEnd();
              }
              struct.setResponse_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BatchQueryGroupResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.header != null) {
        oprot.writeFieldBegin(HEADER_FIELD_DESC);
        struct.header.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.response_list != null) {
        oprot.writeFieldBegin(RESPONSE_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.response_list.size()));
          for (QueryGroupResp _iter29 : struct.response_list)
          {
            _iter29.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BatchQueryGroupRespTupleSchemeFactory implements SchemeFactory {
    public BatchQueryGroupRespTupleScheme getScheme() {
      return new BatchQueryGroupRespTupleScheme();
    }
  }

  private static class BatchQueryGroupRespTupleScheme extends TupleScheme<BatchQueryGroupResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BatchQueryGroupResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetHeader()) {
        optionals.set(0);
      }
      if (struct.isSetResponse_list()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetHeader()) {
        struct.header.write(oprot);
      }
      if (struct.isSetResponse_list()) {
        {
          oprot.writeI32(struct.response_list.size());
          for (QueryGroupResp _iter30 : struct.response_list)
          {
            _iter30.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BatchQueryGroupResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.header = new CommonRet();
        struct.header.read(iprot);
        struct.setHeaderIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list31 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.response_list = new ArrayList<QueryGroupResp>(_list31.size);
          QueryGroupResp _elem32;
          for (int _i33 = 0; _i33 < _list31.size; ++_i33)
          {
            _elem32 = new QueryGroupResp();
            _elem32.read(iprot);
            struct.response_list.add(_elem32);
          }
        }
        struct.setResponse_listIsSet(true);
      }
    }
  }

}

