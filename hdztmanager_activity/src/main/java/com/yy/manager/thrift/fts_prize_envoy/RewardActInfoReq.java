/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.fts_prize_envoy;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2025-05-23")
public class RewardActInfoReq implements org.apache.thrift.TBase<RewardActInfoReq, RewardActInfoReq._Fields>, java.io.Serializable, Cloneable, Comparable<RewardActInfoReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RewardActInfoReq");

  private static final org.apache.thrift.protocol.TField REWARD_ACT_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("rewardActInfo", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField OP_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("opType", org.apache.thrift.protocol.TType.I32, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RewardActInfoReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RewardActInfoReqTupleSchemeFactory());
  }

  public RewardActInfo rewardActInfo; // required
  public int opType; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    REWARD_ACT_INFO((short)1, "rewardActInfo"),
    OP_TYPE((short)2, "opType");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // REWARD_ACT_INFO
          return REWARD_ACT_INFO;
        case 2: // OP_TYPE
          return OP_TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __OPTYPE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.REWARD_ACT_INFO, new org.apache.thrift.meta_data.FieldMetaData("rewardActInfo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "RewardActInfo")));
    tmpMap.put(_Fields.OP_TYPE, new org.apache.thrift.meta_data.FieldMetaData("opType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RewardActInfoReq.class, metaDataMap);
  }

  public RewardActInfoReq() {
  }

  public RewardActInfoReq(
    RewardActInfo rewardActInfo,
    int opType)
  {
    this();
    this.rewardActInfo = rewardActInfo;
    this.opType = opType;
    setOpTypeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RewardActInfoReq(RewardActInfoReq other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetRewardActInfo()) {
      this.rewardActInfo = other.rewardActInfo;
    }
    this.opType = other.opType;
  }

  public RewardActInfoReq deepCopy() {
    return new RewardActInfoReq(this);
  }

  @Override
  public void clear() {
    this.rewardActInfo = null;
    setOpTypeIsSet(false);
    this.opType = 0;
  }

  public RewardActInfo getRewardActInfo() {
    return this.rewardActInfo;
  }

  public RewardActInfoReq setRewardActInfo(RewardActInfo rewardActInfo) {
    this.rewardActInfo = rewardActInfo;
    return this;
  }

  public void unsetRewardActInfo() {
    this.rewardActInfo = null;
  }

  /** Returns true if field rewardActInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetRewardActInfo() {
    return this.rewardActInfo != null;
  }

  public void setRewardActInfoIsSet(boolean value) {
    if (!value) {
      this.rewardActInfo = null;
    }
  }

  public int getOpType() {
    return this.opType;
  }

  public RewardActInfoReq setOpType(int opType) {
    this.opType = opType;
    setOpTypeIsSet(true);
    return this;
  }

  public void unsetOpType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __OPTYPE_ISSET_ID);
  }

  /** Returns true if field opType is set (has been assigned a value) and false otherwise */
  public boolean isSetOpType() {
    return EncodingUtils.testBit(__isset_bitfield, __OPTYPE_ISSET_ID);
  }

  public void setOpTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __OPTYPE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case REWARD_ACT_INFO:
      if (value == null) {
        unsetRewardActInfo();
      } else {
        setRewardActInfo((RewardActInfo)value);
      }
      break;

    case OP_TYPE:
      if (value == null) {
        unsetOpType();
      } else {
        setOpType((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case REWARD_ACT_INFO:
      return getRewardActInfo();

    case OP_TYPE:
      return getOpType();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case REWARD_ACT_INFO:
      return isSetRewardActInfo();
    case OP_TYPE:
      return isSetOpType();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RewardActInfoReq)
      return this.equals((RewardActInfoReq)that);
    return false;
  }

  public boolean equals(RewardActInfoReq that) {
    if (that == null)
      return false;

    boolean this_present_rewardActInfo = true && this.isSetRewardActInfo();
    boolean that_present_rewardActInfo = true && that.isSetRewardActInfo();
    if (this_present_rewardActInfo || that_present_rewardActInfo) {
      if (!(this_present_rewardActInfo && that_present_rewardActInfo))
        return false;
      if (!this.rewardActInfo.equals(that.rewardActInfo))
        return false;
    }

    boolean this_present_opType = true;
    boolean that_present_opType = true;
    if (this_present_opType || that_present_opType) {
      if (!(this_present_opType && that_present_opType))
        return false;
      if (this.opType != that.opType)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rewardActInfo = true && (isSetRewardActInfo());
    list.add(present_rewardActInfo);
    if (present_rewardActInfo)
      list.add(rewardActInfo);

    boolean present_opType = true;
    list.add(present_opType);
    if (present_opType)
      list.add(opType);

    return list.hashCode();
  }

  @Override
  public int compareTo(RewardActInfoReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRewardActInfo()).compareTo(other.isSetRewardActInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewardActInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewardActInfo, other.rewardActInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOpType()).compareTo(other.isSetOpType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOpType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.opType, other.opType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RewardActInfoReq(");
    boolean first = true;

    sb.append("rewardActInfo:");
    if (this.rewardActInfo == null) {
      sb.append("null");
    } else {
      sb.append(this.rewardActInfo);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("opType:");
    sb.append(this.opType);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RewardActInfoReqStandardSchemeFactory implements SchemeFactory {
    public RewardActInfoReqStandardScheme getScheme() {
      return new RewardActInfoReqStandardScheme();
    }
  }

  private static class RewardActInfoReqStandardScheme extends StandardScheme<RewardActInfoReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RewardActInfoReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // REWARD_ACT_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.rewardActInfo = new RewardActInfo();
              struct.rewardActInfo.read(iprot);
              struct.setRewardActInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // OP_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.opType = iprot.readI32();
              struct.setOpTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RewardActInfoReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.rewardActInfo != null) {
        oprot.writeFieldBegin(REWARD_ACT_INFO_FIELD_DESC);
        struct.rewardActInfo.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(OP_TYPE_FIELD_DESC);
      oprot.writeI32(struct.opType);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RewardActInfoReqTupleSchemeFactory implements SchemeFactory {
    public RewardActInfoReqTupleScheme getScheme() {
      return new RewardActInfoReqTupleScheme();
    }
  }

  private static class RewardActInfoReqTupleScheme extends TupleScheme<RewardActInfoReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RewardActInfoReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRewardActInfo()) {
        optionals.set(0);
      }
      if (struct.isSetOpType()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRewardActInfo()) {
        struct.rewardActInfo.write(oprot);
      }
      if (struct.isSetOpType()) {
        oprot.writeI32(struct.opType);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RewardActInfoReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.rewardActInfo = new RewardActInfo();
        struct.rewardActInfo.read(iprot);
        struct.setRewardActInfoIsSet(true);
      }
      if (incoming.get(1)) {
        struct.opType = iprot.readI32();
        struct.setOpTypeIsSet(true);
      }
    }
  }

}

