/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.hdzt.ranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-11-17")
public class BatchRankingItem implements org.apache.thrift.TBase<BatchRankingItem, BatchRankingItem._Fields>, java.io.Serializable, Cloneable, Comparable<BatchRankingItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BatchRankingItem");

  private static final org.apache.thrift.protocol.TField DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("data", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField POINTED_RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("pointedRank", org.apache.thrift.protocol.TType.STRUCT, (short)2);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BatchRankingItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BatchRankingItemTupleSchemeFactory());
  }

  public List<Rank> data; // required
  public Rank pointedRank; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    DATA((short)1, "data"),
    POINTED_RANK((short)2, "pointedRank"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // DATA
          return DATA;
        case 2: // POINTED_RANK
          return POINTED_RANK;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.DATA, new org.apache.thrift.meta_data.FieldMetaData("data", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, Rank.class))));
    tmpMap.put(_Fields.POINTED_RANK, new org.apache.thrift.meta_data.FieldMetaData("pointedRank", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, Rank.class)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING),
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BatchRankingItem.class, metaDataMap);
  }

  public BatchRankingItem() {
  }

  public BatchRankingItem(
    List<Rank> data,
    Rank pointedRank,
    Map<String,String> extData)
  {
    this();
    this.data = data;
    this.pointedRank = pointedRank;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BatchRankingItem(BatchRankingItem other) {
    if (other.isSetData()) {
      List<Rank> __this__data = new ArrayList<Rank>(other.data.size());
      for (Rank other_element : other.data) {
        __this__data.add(new Rank(other_element));
      }
      this.data = __this__data;
    }
    if (other.isSetPointedRank()) {
      this.pointedRank = new Rank(other.pointedRank);
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public BatchRankingItem deepCopy() {
    return new BatchRankingItem(this);
  }

  @Override
  public void clear() {
    this.data = null;
    this.pointedRank = null;
    this.extData = null;
  }

  public int getDataSize() {
    return (this.data == null) ? 0 : this.data.size();
  }

  public java.util.Iterator<Rank> getDataIterator() {
    return (this.data == null) ? null : this.data.iterator();
  }

  public void addToData(Rank elem) {
    if (this.data == null) {
      this.data = new ArrayList<Rank>();
    }
    this.data.add(elem);
  }

  public List<Rank> getData() {
    return this.data;
  }

  public BatchRankingItem setData(List<Rank> data) {
    this.data = data;
    return this;
  }

  public void unsetData() {
    this.data = null;
  }

  /** Returns true if field data is set (has been assigned a value) and false otherwise */
  public boolean isSetData() {
    return this.data != null;
  }

  public void setDataIsSet(boolean value) {
    if (!value) {
      this.data = null;
    }
  }

  public Rank getPointedRank() {
    return this.pointedRank;
  }

  public BatchRankingItem setPointedRank(Rank pointedRank) {
    this.pointedRank = pointedRank;
    return this;
  }

  public void unsetPointedRank() {
    this.pointedRank = null;
  }

  /** Returns true if field pointedRank is set (has been assigned a value) and false otherwise */
  public boolean isSetPointedRank() {
    return this.pointedRank != null;
  }

  public void setPointedRankIsSet(boolean value) {
    if (!value) {
      this.pointedRank = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public BatchRankingItem setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case DATA:
      if (value == null) {
        unsetData();
      } else {
        setData((List<Rank>)value);
      }
      break;

    case POINTED_RANK:
      if (value == null) {
        unsetPointedRank();
      } else {
        setPointedRank((Rank)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case DATA:
      return getData();

    case POINTED_RANK:
      return getPointedRank();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case DATA:
      return isSetData();
    case POINTED_RANK:
      return isSetPointedRank();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BatchRankingItem)
      return this.equals((BatchRankingItem)that);
    return false;
  }

  public boolean equals(BatchRankingItem that) {
    if (that == null)
      return false;

    boolean this_present_data = true && this.isSetData();
    boolean that_present_data = true && that.isSetData();
    if (this_present_data || that_present_data) {
      if (!(this_present_data && that_present_data))
        return false;
      if (!this.data.equals(that.data))
        return false;
    }

    boolean this_present_pointedRank = true && this.isSetPointedRank();
    boolean that_present_pointedRank = true && that.isSetPointedRank();
    if (this_present_pointedRank || that_present_pointedRank) {
      if (!(this_present_pointedRank && that_present_pointedRank))
        return false;
      if (!this.pointedRank.equals(that.pointedRank))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_data = true && (isSetData());
    list.add(present_data);
    if (present_data)
      list.add(data);

    boolean present_pointedRank = true && (isSetPointedRank());
    list.add(present_pointedRank);
    if (present_pointedRank)
      list.add(pointedRank);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(BatchRankingItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetData()).compareTo(other.isSetData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.data, other.data);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPointedRank()).compareTo(other.isSetPointedRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPointedRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pointedRank, other.pointedRank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BatchRankingItem(");
    boolean first = true;

    sb.append("data:");
    if (this.data == null) {
      sb.append("null");
    } else {
      sb.append(this.data);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pointedRank:");
    if (this.pointedRank == null) {
      sb.append("null");
    } else {
      sb.append(this.pointedRank);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (pointedRank != null) {
      pointedRank.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BatchRankingItemStandardSchemeFactory implements SchemeFactory {
    public BatchRankingItemStandardScheme getScheme() {
      return new BatchRankingItemStandardScheme();
    }
  }

  private static class BatchRankingItemStandardScheme extends StandardScheme<BatchRankingItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BatchRankingItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list522 = iprot.readListBegin();
                struct.data = new ArrayList<Rank>(_list522.size);
                Rank _elem523;
                for (int _i524 = 0; _i524 < _list522.size; ++_i524)
                {
                  _elem523 = new Rank();
                  _elem523.read(iprot);
                  struct.data.add(_elem523);
                }
                iprot.readListEnd();
              }
              struct.setDataIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // POINTED_RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.pointedRank = new Rank();
              struct.pointedRank.read(iprot);
              struct.setPointedRankIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map525 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map525.size);
                String _key526;
                String _val527;
                for (int _i528 = 0; _i528 < _map525.size; ++_i528)
                {
                  _key526 = iprot.readString();
                  _val527 = iprot.readString();
                  struct.extData.put(_key526, _val527);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BatchRankingItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.data != null) {
        oprot.writeFieldBegin(DATA_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.data.size()));
          for (Rank _iter529 : struct.data)
          {
            _iter529.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.pointedRank != null) {
        oprot.writeFieldBegin(POINTED_RANK_FIELD_DESC);
        struct.pointedRank.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter530 : struct.extData.entrySet())
          {
            oprot.writeString(_iter530.getKey());
            oprot.writeString(_iter530.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BatchRankingItemTupleSchemeFactory implements SchemeFactory {
    public BatchRankingItemTupleScheme getScheme() {
      return new BatchRankingItemTupleScheme();
    }
  }

  private static class BatchRankingItemTupleScheme extends TupleScheme<BatchRankingItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BatchRankingItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetData()) {
        optionals.set(0);
      }
      if (struct.isSetPointedRank()) {
        optionals.set(1);
      }
      if (struct.isSetExtData()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetData()) {
        {
          oprot.writeI32(struct.data.size());
          for (Rank _iter531 : struct.data)
          {
            _iter531.write(oprot);
          }
        }
      }
      if (struct.isSetPointedRank()) {
        struct.pointedRank.write(oprot);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter532 : struct.extData.entrySet())
          {
            oprot.writeString(_iter532.getKey());
            oprot.writeString(_iter532.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BatchRankingItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list533 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.data = new ArrayList<Rank>(_list533.size);
          Rank _elem534;
          for (int _i535 = 0; _i535 < _list533.size; ++_i535)
          {
            _elem534 = new Rank();
            _elem534.read(iprot);
            struct.data.add(_elem534);
          }
        }
        struct.setDataIsSet(true);
      }
      if (incoming.get(1)) {
        struct.pointedRank = new Rank();
        struct.pointedRank.read(iprot);
        struct.setPointedRankIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map536 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map536.size);
          String _key537;
          String _val538;
          for (int _i539 = 0; _i539 < _map536.size; ++_i539)
          {
            _key537 = iprot.readString();
            _val538 = iprot.readString();
            struct.extData.put(_key537, _val538);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

