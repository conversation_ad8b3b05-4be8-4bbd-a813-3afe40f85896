/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.hdzt.award;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class AwardPackageItemInfo implements org.apache.thrift.TBase<AwardPackageItemInfo, AwardPackageItemInfo._Fields>, java.io.Serializable, Cloneable, Comparable<AwardPackageItemInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("AwardPackageItemInfo");

  private static final org.apache.thrift.protocol.TField ITEM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("itemId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField GIFT_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("giftType", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField GIFT_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("giftName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField GIFT_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("giftNum", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField GIFT_IMAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("giftImage", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField CHOICE_IMAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("choiceImage", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField MOUSEOVER_TIPS_FIELD_DESC = new org.apache.thrift.protocol.TField("mouseoverTips", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField SKIP_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("skipUrl", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField EXT_JSON_FIELD_DESC = new org.apache.thrift.protocol.TField("extJson", org.apache.thrift.protocol.TType.STRING, (short)9);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new AwardPackageItemInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new AwardPackageItemInfoTupleSchemeFactory());
  }

  public long itemId; // required
  public int giftType; // required
  public String giftName; // required
  public int giftNum; // required
  public String giftImage; // required
  public String choiceImage; // required
  public String mouseoverTips; // required
  public String skipUrl; // required
  public String extJson; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ITEM_ID((short)1, "itemId"),
    GIFT_TYPE((short)2, "giftType"),
    GIFT_NAME((short)3, "giftName"),
    GIFT_NUM((short)4, "giftNum"),
    GIFT_IMAGE((short)5, "giftImage"),
    CHOICE_IMAGE((short)6, "choiceImage"),
    MOUSEOVER_TIPS((short)7, "mouseoverTips"),
    SKIP_URL((short)8, "skipUrl"),
    EXT_JSON((short)9, "extJson");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ITEM_ID
          return ITEM_ID;
        case 2: // GIFT_TYPE
          return GIFT_TYPE;
        case 3: // GIFT_NAME
          return GIFT_NAME;
        case 4: // GIFT_NUM
          return GIFT_NUM;
        case 5: // GIFT_IMAGE
          return GIFT_IMAGE;
        case 6: // CHOICE_IMAGE
          return CHOICE_IMAGE;
        case 7: // MOUSEOVER_TIPS
          return MOUSEOVER_TIPS;
        case 8: // SKIP_URL
          return SKIP_URL;
        case 9: // EXT_JSON
          return EXT_JSON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ITEMID_ISSET_ID = 0;
  private static final int __GIFTTYPE_ISSET_ID = 1;
  private static final int __GIFTNUM_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ITEM_ID, new org.apache.thrift.meta_data.FieldMetaData("itemId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.GIFT_TYPE, new org.apache.thrift.meta_data.FieldMetaData("giftType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GIFT_NAME, new org.apache.thrift.meta_data.FieldMetaData("giftName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GIFT_NUM, new org.apache.thrift.meta_data.FieldMetaData("giftNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GIFT_IMAGE, new org.apache.thrift.meta_data.FieldMetaData("giftImage", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CHOICE_IMAGE, new org.apache.thrift.meta_data.FieldMetaData("choiceImage", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MOUSEOVER_TIPS, new org.apache.thrift.meta_data.FieldMetaData("mouseoverTips", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SKIP_URL, new org.apache.thrift.meta_data.FieldMetaData("skipUrl", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_JSON, new org.apache.thrift.meta_data.FieldMetaData("extJson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(AwardPackageItemInfo.class, metaDataMap);
  }

  public AwardPackageItemInfo() {
  }

  public AwardPackageItemInfo(
    long itemId,
    int giftType,
    String giftName,
    int giftNum,
    String giftImage,
    String choiceImage,
    String mouseoverTips,
    String skipUrl,
    String extJson)
  {
    this();
    this.itemId = itemId;
    setItemIdIsSet(true);
    this.giftType = giftType;
    setGiftTypeIsSet(true);
    this.giftName = giftName;
    this.giftNum = giftNum;
    setGiftNumIsSet(true);
    this.giftImage = giftImage;
    this.choiceImage = choiceImage;
    this.mouseoverTips = mouseoverTips;
    this.skipUrl = skipUrl;
    this.extJson = extJson;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public AwardPackageItemInfo(AwardPackageItemInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.itemId = other.itemId;
    this.giftType = other.giftType;
    if (other.isSetGiftName()) {
      this.giftName = other.giftName;
    }
    this.giftNum = other.giftNum;
    if (other.isSetGiftImage()) {
      this.giftImage = other.giftImage;
    }
    if (other.isSetChoiceImage()) {
      this.choiceImage = other.choiceImage;
    }
    if (other.isSetMouseoverTips()) {
      this.mouseoverTips = other.mouseoverTips;
    }
    if (other.isSetSkipUrl()) {
      this.skipUrl = other.skipUrl;
    }
    if (other.isSetExtJson()) {
      this.extJson = other.extJson;
    }
  }

  public AwardPackageItemInfo deepCopy() {
    return new AwardPackageItemInfo(this);
  }

  @Override
  public void clear() {
    setItemIdIsSet(false);
    this.itemId = 0;
    setGiftTypeIsSet(false);
    this.giftType = 0;
    this.giftName = null;
    setGiftNumIsSet(false);
    this.giftNum = 0;
    this.giftImage = null;
    this.choiceImage = null;
    this.mouseoverTips = null;
    this.skipUrl = null;
    this.extJson = null;
  }

  public long getItemId() {
    return this.itemId;
  }

  public AwardPackageItemInfo setItemId(long itemId) {
    this.itemId = itemId;
    setItemIdIsSet(true);
    return this;
  }

  public void unsetItemId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ITEMID_ISSET_ID);
  }

  /** Returns true if field itemId is set (has been assigned a value) and false otherwise */
  public boolean isSetItemId() {
    return EncodingUtils.testBit(__isset_bitfield, __ITEMID_ISSET_ID);
  }

  public void setItemIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ITEMID_ISSET_ID, value);
  }

  public int getGiftType() {
    return this.giftType;
  }

  public AwardPackageItemInfo setGiftType(int giftType) {
    this.giftType = giftType;
    setGiftTypeIsSet(true);
    return this;
  }

  public void unsetGiftType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __GIFTTYPE_ISSET_ID);
  }

  /** Returns true if field giftType is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftType() {
    return EncodingUtils.testBit(__isset_bitfield, __GIFTTYPE_ISSET_ID);
  }

  public void setGiftTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __GIFTTYPE_ISSET_ID, value);
  }

  public String getGiftName() {
    return this.giftName;
  }

  public AwardPackageItemInfo setGiftName(String giftName) {
    this.giftName = giftName;
    return this;
  }

  public void unsetGiftName() {
    this.giftName = null;
  }

  /** Returns true if field giftName is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftName() {
    return this.giftName != null;
  }

  public void setGiftNameIsSet(boolean value) {
    if (!value) {
      this.giftName = null;
    }
  }

  public int getGiftNum() {
    return this.giftNum;
  }

  public AwardPackageItemInfo setGiftNum(int giftNum) {
    this.giftNum = giftNum;
    setGiftNumIsSet(true);
    return this;
  }

  public void unsetGiftNum() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __GIFTNUM_ISSET_ID);
  }

  /** Returns true if field giftNum is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftNum() {
    return EncodingUtils.testBit(__isset_bitfield, __GIFTNUM_ISSET_ID);
  }

  public void setGiftNumIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __GIFTNUM_ISSET_ID, value);
  }

  public String getGiftImage() {
    return this.giftImage;
  }

  public AwardPackageItemInfo setGiftImage(String giftImage) {
    this.giftImage = giftImage;
    return this;
  }

  public void unsetGiftImage() {
    this.giftImage = null;
  }

  /** Returns true if field giftImage is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftImage() {
    return this.giftImage != null;
  }

  public void setGiftImageIsSet(boolean value) {
    if (!value) {
      this.giftImage = null;
    }
  }

  public String getChoiceImage() {
    return this.choiceImage;
  }

  public AwardPackageItemInfo setChoiceImage(String choiceImage) {
    this.choiceImage = choiceImage;
    return this;
  }

  public void unsetChoiceImage() {
    this.choiceImage = null;
  }

  /** Returns true if field choiceImage is set (has been assigned a value) and false otherwise */
  public boolean isSetChoiceImage() {
    return this.choiceImage != null;
  }

  public void setChoiceImageIsSet(boolean value) {
    if (!value) {
      this.choiceImage = null;
    }
  }

  public String getMouseoverTips() {
    return this.mouseoverTips;
  }

  public AwardPackageItemInfo setMouseoverTips(String mouseoverTips) {
    this.mouseoverTips = mouseoverTips;
    return this;
  }

  public void unsetMouseoverTips() {
    this.mouseoverTips = null;
  }

  /** Returns true if field mouseoverTips is set (has been assigned a value) and false otherwise */
  public boolean isSetMouseoverTips() {
    return this.mouseoverTips != null;
  }

  public void setMouseoverTipsIsSet(boolean value) {
    if (!value) {
      this.mouseoverTips = null;
    }
  }

  public String getSkipUrl() {
    return this.skipUrl;
  }

  public AwardPackageItemInfo setSkipUrl(String skipUrl) {
    this.skipUrl = skipUrl;
    return this;
  }

  public void unsetSkipUrl() {
    this.skipUrl = null;
  }

  /** Returns true if field skipUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetSkipUrl() {
    return this.skipUrl != null;
  }

  public void setSkipUrlIsSet(boolean value) {
    if (!value) {
      this.skipUrl = null;
    }
  }

  public String getExtJson() {
    return this.extJson;
  }

  public AwardPackageItemInfo setExtJson(String extJson) {
    this.extJson = extJson;
    return this;
  }

  public void unsetExtJson() {
    this.extJson = null;
  }

  /** Returns true if field extJson is set (has been assigned a value) and false otherwise */
  public boolean isSetExtJson() {
    return this.extJson != null;
  }

  public void setExtJsonIsSet(boolean value) {
    if (!value) {
      this.extJson = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ITEM_ID:
      if (value == null) {
        unsetItemId();
      } else {
        setItemId((Long)value);
      }
      break;

    case GIFT_TYPE:
      if (value == null) {
        unsetGiftType();
      } else {
        setGiftType((Integer)value);
      }
      break;

    case GIFT_NAME:
      if (value == null) {
        unsetGiftName();
      } else {
        setGiftName((String)value);
      }
      break;

    case GIFT_NUM:
      if (value == null) {
        unsetGiftNum();
      } else {
        setGiftNum((Integer)value);
      }
      break;

    case GIFT_IMAGE:
      if (value == null) {
        unsetGiftImage();
      } else {
        setGiftImage((String)value);
      }
      break;

    case CHOICE_IMAGE:
      if (value == null) {
        unsetChoiceImage();
      } else {
        setChoiceImage((String)value);
      }
      break;

    case MOUSEOVER_TIPS:
      if (value == null) {
        unsetMouseoverTips();
      } else {
        setMouseoverTips((String)value);
      }
      break;

    case SKIP_URL:
      if (value == null) {
        unsetSkipUrl();
      } else {
        setSkipUrl((String)value);
      }
      break;

    case EXT_JSON:
      if (value == null) {
        unsetExtJson();
      } else {
        setExtJson((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ITEM_ID:
      return getItemId();

    case GIFT_TYPE:
      return getGiftType();

    case GIFT_NAME:
      return getGiftName();

    case GIFT_NUM:
      return getGiftNum();

    case GIFT_IMAGE:
      return getGiftImage();

    case CHOICE_IMAGE:
      return getChoiceImage();

    case MOUSEOVER_TIPS:
      return getMouseoverTips();

    case SKIP_URL:
      return getSkipUrl();

    case EXT_JSON:
      return getExtJson();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ITEM_ID:
      return isSetItemId();
    case GIFT_TYPE:
      return isSetGiftType();
    case GIFT_NAME:
      return isSetGiftName();
    case GIFT_NUM:
      return isSetGiftNum();
    case GIFT_IMAGE:
      return isSetGiftImage();
    case CHOICE_IMAGE:
      return isSetChoiceImage();
    case MOUSEOVER_TIPS:
      return isSetMouseoverTips();
    case SKIP_URL:
      return isSetSkipUrl();
    case EXT_JSON:
      return isSetExtJson();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof AwardPackageItemInfo)
      return this.equals((AwardPackageItemInfo)that);
    return false;
  }

  public boolean equals(AwardPackageItemInfo that) {
    if (that == null)
      return false;

    boolean this_present_itemId = true;
    boolean that_present_itemId = true;
    if (this_present_itemId || that_present_itemId) {
      if (!(this_present_itemId && that_present_itemId))
        return false;
      if (this.itemId != that.itemId)
        return false;
    }

    boolean this_present_giftType = true;
    boolean that_present_giftType = true;
    if (this_present_giftType || that_present_giftType) {
      if (!(this_present_giftType && that_present_giftType))
        return false;
      if (this.giftType != that.giftType)
        return false;
    }

    boolean this_present_giftName = true && this.isSetGiftName();
    boolean that_present_giftName = true && that.isSetGiftName();
    if (this_present_giftName || that_present_giftName) {
      if (!(this_present_giftName && that_present_giftName))
        return false;
      if (!this.giftName.equals(that.giftName))
        return false;
    }

    boolean this_present_giftNum = true;
    boolean that_present_giftNum = true;
    if (this_present_giftNum || that_present_giftNum) {
      if (!(this_present_giftNum && that_present_giftNum))
        return false;
      if (this.giftNum != that.giftNum)
        return false;
    }

    boolean this_present_giftImage = true && this.isSetGiftImage();
    boolean that_present_giftImage = true && that.isSetGiftImage();
    if (this_present_giftImage || that_present_giftImage) {
      if (!(this_present_giftImage && that_present_giftImage))
        return false;
      if (!this.giftImage.equals(that.giftImage))
        return false;
    }

    boolean this_present_choiceImage = true && this.isSetChoiceImage();
    boolean that_present_choiceImage = true && that.isSetChoiceImage();
    if (this_present_choiceImage || that_present_choiceImage) {
      if (!(this_present_choiceImage && that_present_choiceImage))
        return false;
      if (!this.choiceImage.equals(that.choiceImage))
        return false;
    }

    boolean this_present_mouseoverTips = true && this.isSetMouseoverTips();
    boolean that_present_mouseoverTips = true && that.isSetMouseoverTips();
    if (this_present_mouseoverTips || that_present_mouseoverTips) {
      if (!(this_present_mouseoverTips && that_present_mouseoverTips))
        return false;
      if (!this.mouseoverTips.equals(that.mouseoverTips))
        return false;
    }

    boolean this_present_skipUrl = true && this.isSetSkipUrl();
    boolean that_present_skipUrl = true && that.isSetSkipUrl();
    if (this_present_skipUrl || that_present_skipUrl) {
      if (!(this_present_skipUrl && that_present_skipUrl))
        return false;
      if (!this.skipUrl.equals(that.skipUrl))
        return false;
    }

    boolean this_present_extJson = true && this.isSetExtJson();
    boolean that_present_extJson = true && that.isSetExtJson();
    if (this_present_extJson || that_present_extJson) {
      if (!(this_present_extJson && that_present_extJson))
        return false;
      if (!this.extJson.equals(that.extJson))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_itemId = true;
    list.add(present_itemId);
    if (present_itemId)
      list.add(itemId);

    boolean present_giftType = true;
    list.add(present_giftType);
    if (present_giftType)
      list.add(giftType);

    boolean present_giftName = true && (isSetGiftName());
    list.add(present_giftName);
    if (present_giftName)
      list.add(giftName);

    boolean present_giftNum = true;
    list.add(present_giftNum);
    if (present_giftNum)
      list.add(giftNum);

    boolean present_giftImage = true && (isSetGiftImage());
    list.add(present_giftImage);
    if (present_giftImage)
      list.add(giftImage);

    boolean present_choiceImage = true && (isSetChoiceImage());
    list.add(present_choiceImage);
    if (present_choiceImage)
      list.add(choiceImage);

    boolean present_mouseoverTips = true && (isSetMouseoverTips());
    list.add(present_mouseoverTips);
    if (present_mouseoverTips)
      list.add(mouseoverTips);

    boolean present_skipUrl = true && (isSetSkipUrl());
    list.add(present_skipUrl);
    if (present_skipUrl)
      list.add(skipUrl);

    boolean present_extJson = true && (isSetExtJson());
    list.add(present_extJson);
    if (present_extJson)
      list.add(extJson);

    return list.hashCode();
  }

  @Override
  public int compareTo(AwardPackageItemInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetItemId()).compareTo(other.isSetItemId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemId, other.itemId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftType()).compareTo(other.isSetGiftType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftType, other.giftType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftName()).compareTo(other.isSetGiftName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftName, other.giftName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftNum()).compareTo(other.isSetGiftNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftNum, other.giftNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftImage()).compareTo(other.isSetGiftImage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftImage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftImage, other.giftImage);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetChoiceImage()).compareTo(other.isSetChoiceImage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChoiceImage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.choiceImage, other.choiceImage);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMouseoverTips()).compareTo(other.isSetMouseoverTips());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMouseoverTips()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mouseoverTips, other.mouseoverTips);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSkipUrl()).compareTo(other.isSetSkipUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSkipUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.skipUrl, other.skipUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtJson()).compareTo(other.isSetExtJson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtJson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extJson, other.extJson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("AwardPackageItemInfo(");
    boolean first = true;

    sb.append("itemId:");
    sb.append(this.itemId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftType:");
    sb.append(this.giftType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftName:");
    if (this.giftName == null) {
      sb.append("null");
    } else {
      sb.append(this.giftName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftNum:");
    sb.append(this.giftNum);
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftImage:");
    if (this.giftImage == null) {
      sb.append("null");
    } else {
      sb.append(this.giftImage);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("choiceImage:");
    if (this.choiceImage == null) {
      sb.append("null");
    } else {
      sb.append(this.choiceImage);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("mouseoverTips:");
    if (this.mouseoverTips == null) {
      sb.append("null");
    } else {
      sb.append(this.mouseoverTips);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("skipUrl:");
    if (this.skipUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.skipUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extJson:");
    if (this.extJson == null) {
      sb.append("null");
    } else {
      sb.append(this.extJson);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class AwardPackageItemInfoStandardSchemeFactory implements SchemeFactory {
    public AwardPackageItemInfoStandardScheme getScheme() {
      return new AwardPackageItemInfoStandardScheme();
    }
  }

  private static class AwardPackageItemInfoStandardScheme extends StandardScheme<AwardPackageItemInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, AwardPackageItemInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ITEM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.itemId = iprot.readI64();
              struct.setItemIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // GIFT_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.giftType = iprot.readI32();
              struct.setGiftTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // GIFT_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftName = iprot.readString();
              struct.setGiftNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // GIFT_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.giftNum = iprot.readI32();
              struct.setGiftNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // GIFT_IMAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftImage = iprot.readString();
              struct.setGiftImageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // CHOICE_IMAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.choiceImage = iprot.readString();
              struct.setChoiceImageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // MOUSEOVER_TIPS
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.mouseoverTips = iprot.readString();
              struct.setMouseoverTipsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // SKIP_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.skipUrl = iprot.readString();
              struct.setSkipUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // EXT_JSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extJson = iprot.readString();
              struct.setExtJsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, AwardPackageItemInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ITEM_ID_FIELD_DESC);
      oprot.writeI64(struct.itemId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(GIFT_TYPE_FIELD_DESC);
      oprot.writeI32(struct.giftType);
      oprot.writeFieldEnd();
      if (struct.giftName != null) {
        oprot.writeFieldBegin(GIFT_NAME_FIELD_DESC);
        oprot.writeString(struct.giftName);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(GIFT_NUM_FIELD_DESC);
      oprot.writeI32(struct.giftNum);
      oprot.writeFieldEnd();
      if (struct.giftImage != null) {
        oprot.writeFieldBegin(GIFT_IMAGE_FIELD_DESC);
        oprot.writeString(struct.giftImage);
        oprot.writeFieldEnd();
      }
      if (struct.choiceImage != null) {
        oprot.writeFieldBegin(CHOICE_IMAGE_FIELD_DESC);
        oprot.writeString(struct.choiceImage);
        oprot.writeFieldEnd();
      }
      if (struct.mouseoverTips != null) {
        oprot.writeFieldBegin(MOUSEOVER_TIPS_FIELD_DESC);
        oprot.writeString(struct.mouseoverTips);
        oprot.writeFieldEnd();
      }
      if (struct.skipUrl != null) {
        oprot.writeFieldBegin(SKIP_URL_FIELD_DESC);
        oprot.writeString(struct.skipUrl);
        oprot.writeFieldEnd();
      }
      if (struct.extJson != null) {
        oprot.writeFieldBegin(EXT_JSON_FIELD_DESC);
        oprot.writeString(struct.extJson);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class AwardPackageItemInfoTupleSchemeFactory implements SchemeFactory {
    public AwardPackageItemInfoTupleScheme getScheme() {
      return new AwardPackageItemInfoTupleScheme();
    }
  }

  private static class AwardPackageItemInfoTupleScheme extends TupleScheme<AwardPackageItemInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, AwardPackageItemInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetItemId()) {
        optionals.set(0);
      }
      if (struct.isSetGiftType()) {
        optionals.set(1);
      }
      if (struct.isSetGiftName()) {
        optionals.set(2);
      }
      if (struct.isSetGiftNum()) {
        optionals.set(3);
      }
      if (struct.isSetGiftImage()) {
        optionals.set(4);
      }
      if (struct.isSetChoiceImage()) {
        optionals.set(5);
      }
      if (struct.isSetMouseoverTips()) {
        optionals.set(6);
      }
      if (struct.isSetSkipUrl()) {
        optionals.set(7);
      }
      if (struct.isSetExtJson()) {
        optionals.set(8);
      }
      oprot.writeBitSet(optionals, 9);
      if (struct.isSetItemId()) {
        oprot.writeI64(struct.itemId);
      }
      if (struct.isSetGiftType()) {
        oprot.writeI32(struct.giftType);
      }
      if (struct.isSetGiftName()) {
        oprot.writeString(struct.giftName);
      }
      if (struct.isSetGiftNum()) {
        oprot.writeI32(struct.giftNum);
      }
      if (struct.isSetGiftImage()) {
        oprot.writeString(struct.giftImage);
      }
      if (struct.isSetChoiceImage()) {
        oprot.writeString(struct.choiceImage);
      }
      if (struct.isSetMouseoverTips()) {
        oprot.writeString(struct.mouseoverTips);
      }
      if (struct.isSetSkipUrl()) {
        oprot.writeString(struct.skipUrl);
      }
      if (struct.isSetExtJson()) {
        oprot.writeString(struct.extJson);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, AwardPackageItemInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(9);
      if (incoming.get(0)) {
        struct.itemId = iprot.readI64();
        struct.setItemIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.giftType = iprot.readI32();
        struct.setGiftTypeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.giftName = iprot.readString();
        struct.setGiftNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.giftNum = iprot.readI32();
        struct.setGiftNumIsSet(true);
      }
      if (incoming.get(4)) {
        struct.giftImage = iprot.readString();
        struct.setGiftImageIsSet(true);
      }
      if (incoming.get(5)) {
        struct.choiceImage = iprot.readString();
        struct.setChoiceImageIsSet(true);
      }
      if (incoming.get(6)) {
        struct.mouseoverTips = iprot.readString();
        struct.setMouseoverTipsIsSet(true);
      }
      if (incoming.get(7)) {
        struct.skipUrl = iprot.readString();
        struct.setSkipUrlIsSet(true);
      }
      if (incoming.get(8)) {
        struct.extJson = iprot.readString();
        struct.setExtJsonIsSet(true);
      }
    }
  }

}

