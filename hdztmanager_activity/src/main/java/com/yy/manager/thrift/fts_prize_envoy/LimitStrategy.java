/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.fts_prize_envoy;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2025-05-23")
public class LimitStrategy implements org.apache.thrift.TBase<LimitStrategy, LimitStrategy._Fields>, java.io.Serializable, Cloneable, Comparable<LimitStrategy> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("LimitStrategy");

  private static final org.apache.thrift.protocol.TField SUB_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("subID", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField MOLD_FIELD_DESC = new org.apache.thrift.protocol.TField("mold", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField VALUE_FIELD_DESC = new org.apache.thrift.protocol.TField("value", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField DESC_FIELD_DESC = new org.apache.thrift.protocol.TField("desc", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new LimitStrategyStandardSchemeFactory());
    schemes.put(TupleScheme.class, new LimitStrategyTupleSchemeFactory());
  }

  public long subID; // required
  public long mold; // required
  public long value; // required
  public String desc; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SUB_ID((short)1, "subID"),
    MOLD((short)2, "mold"),
    VALUE((short)3, "value"),
    DESC((short)4, "desc");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SUB_ID
          return SUB_ID;
        case 2: // MOLD
          return MOLD;
        case 3: // VALUE
          return VALUE;
        case 4: // DESC
          return DESC;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SUBID_ISSET_ID = 0;
  private static final int __MOLD_ISSET_ID = 1;
  private static final int __VALUE_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SUB_ID, new org.apache.thrift.meta_data.FieldMetaData("subID", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MOLD, new org.apache.thrift.meta_data.FieldMetaData("mold", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.VALUE, new org.apache.thrift.meta_data.FieldMetaData("value", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DESC, new org.apache.thrift.meta_data.FieldMetaData("desc", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(LimitStrategy.class, metaDataMap);
  }

  public LimitStrategy() {
  }

  public LimitStrategy(
    long subID,
    long mold,
    long value,
    String desc)
  {
    this();
    this.subID = subID;
    setSubIDIsSet(true);
    this.mold = mold;
    setMoldIsSet(true);
    this.value = value;
    setValueIsSet(true);
    this.desc = desc;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public LimitStrategy(LimitStrategy other) {
    __isset_bitfield = other.__isset_bitfield;
    this.subID = other.subID;
    this.mold = other.mold;
    this.value = other.value;
    if (other.isSetDesc()) {
      this.desc = other.desc;
    }
  }

  public LimitStrategy deepCopy() {
    return new LimitStrategy(this);
  }

  @Override
  public void clear() {
    setSubIDIsSet(false);
    this.subID = 0;
    setMoldIsSet(false);
    this.mold = 0;
    setValueIsSet(false);
    this.value = 0;
    this.desc = null;
  }

  public long getSubID() {
    return this.subID;
  }

  public LimitStrategy setSubID(long subID) {
    this.subID = subID;
    setSubIDIsSet(true);
    return this;
  }

  public void unsetSubID() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUBID_ISSET_ID);
  }

  /** Returns true if field subID is set (has been assigned a value) and false otherwise */
  public boolean isSetSubID() {
    return EncodingUtils.testBit(__isset_bitfield, __SUBID_ISSET_ID);
  }

  public void setSubIDIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUBID_ISSET_ID, value);
  }

  public long getMold() {
    return this.mold;
  }

  public LimitStrategy setMold(long mold) {
    this.mold = mold;
    setMoldIsSet(true);
    return this;
  }

  public void unsetMold() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __MOLD_ISSET_ID);
  }

  /** Returns true if field mold is set (has been assigned a value) and false otherwise */
  public boolean isSetMold() {
    return EncodingUtils.testBit(__isset_bitfield, __MOLD_ISSET_ID);
  }

  public void setMoldIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __MOLD_ISSET_ID, value);
  }

  public long getValue() {
    return this.value;
  }

  public LimitStrategy setValue(long value) {
    this.value = value;
    setValueIsSet(true);
    return this;
  }

  public void unsetValue() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __VALUE_ISSET_ID);
  }

  /** Returns true if field value is set (has been assigned a value) and false otherwise */
  public boolean isSetValue() {
    return EncodingUtils.testBit(__isset_bitfield, __VALUE_ISSET_ID);
  }

  public void setValueIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __VALUE_ISSET_ID, value);
  }

  public String getDesc() {
    return this.desc;
  }

  public LimitStrategy setDesc(String desc) {
    this.desc = desc;
    return this;
  }

  public void unsetDesc() {
    this.desc = null;
  }

  /** Returns true if field desc is set (has been assigned a value) and false otherwise */
  public boolean isSetDesc() {
    return this.desc != null;
  }

  public void setDescIsSet(boolean value) {
    if (!value) {
      this.desc = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SUB_ID:
      if (value == null) {
        unsetSubID();
      } else {
        setSubID((Long)value);
      }
      break;

    case MOLD:
      if (value == null) {
        unsetMold();
      } else {
        setMold((Long)value);
      }
      break;

    case VALUE:
      if (value == null) {
        unsetValue();
      } else {
        setValue((Long)value);
      }
      break;

    case DESC:
      if (value == null) {
        unsetDesc();
      } else {
        setDesc((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SUB_ID:
      return getSubID();

    case MOLD:
      return getMold();

    case VALUE:
      return getValue();

    case DESC:
      return getDesc();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SUB_ID:
      return isSetSubID();
    case MOLD:
      return isSetMold();
    case VALUE:
      return isSetValue();
    case DESC:
      return isSetDesc();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof LimitStrategy)
      return this.equals((LimitStrategy)that);
    return false;
  }

  public boolean equals(LimitStrategy that) {
    if (that == null)
      return false;

    boolean this_present_subID = true;
    boolean that_present_subID = true;
    if (this_present_subID || that_present_subID) {
      if (!(this_present_subID && that_present_subID))
        return false;
      if (this.subID != that.subID)
        return false;
    }

    boolean this_present_mold = true;
    boolean that_present_mold = true;
    if (this_present_mold || that_present_mold) {
      if (!(this_present_mold && that_present_mold))
        return false;
      if (this.mold != that.mold)
        return false;
    }

    boolean this_present_value = true;
    boolean that_present_value = true;
    if (this_present_value || that_present_value) {
      if (!(this_present_value && that_present_value))
        return false;
      if (this.value != that.value)
        return false;
    }

    boolean this_present_desc = true && this.isSetDesc();
    boolean that_present_desc = true && that.isSetDesc();
    if (this_present_desc || that_present_desc) {
      if (!(this_present_desc && that_present_desc))
        return false;
      if (!this.desc.equals(that.desc))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_subID = true;
    list.add(present_subID);
    if (present_subID)
      list.add(subID);

    boolean present_mold = true;
    list.add(present_mold);
    if (present_mold)
      list.add(mold);

    boolean present_value = true;
    list.add(present_value);
    if (present_value)
      list.add(value);

    boolean present_desc = true && (isSetDesc());
    list.add(present_desc);
    if (present_desc)
      list.add(desc);

    return list.hashCode();
  }

  @Override
  public int compareTo(LimitStrategy other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSubID()).compareTo(other.isSetSubID());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSubID()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.subID, other.subID);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMold()).compareTo(other.isSetMold());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMold()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mold, other.mold);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetValue()).compareTo(other.isSetValue());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetValue()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.value, other.value);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDesc()).compareTo(other.isSetDesc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDesc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.desc, other.desc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("LimitStrategy(");
    boolean first = true;

    sb.append("subID:");
    sb.append(this.subID);
    first = false;
    if (!first) sb.append(", ");
    sb.append("mold:");
    sb.append(this.mold);
    first = false;
    if (!first) sb.append(", ");
    sb.append("value:");
    sb.append(this.value);
    first = false;
    if (!first) sb.append(", ");
    sb.append("desc:");
    if (this.desc == null) {
      sb.append("null");
    } else {
      sb.append(this.desc);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class LimitStrategyStandardSchemeFactory implements SchemeFactory {
    public LimitStrategyStandardScheme getScheme() {
      return new LimitStrategyStandardScheme();
    }
  }

  private static class LimitStrategyStandardScheme extends StandardScheme<LimitStrategy> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, LimitStrategy struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SUB_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.subID = iprot.readI64();
              struct.setSubIDIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MOLD
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.mold = iprot.readI64();
              struct.setMoldIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // VALUE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.value = iprot.readI64();
              struct.setValueIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DESC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.desc = iprot.readString();
              struct.setDescIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, LimitStrategy struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(SUB_ID_FIELD_DESC);
      oprot.writeI64(struct.subID);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(MOLD_FIELD_DESC);
      oprot.writeI64(struct.mold);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(VALUE_FIELD_DESC);
      oprot.writeI64(struct.value);
      oprot.writeFieldEnd();
      if (struct.desc != null) {
        oprot.writeFieldBegin(DESC_FIELD_DESC);
        oprot.writeString(struct.desc);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class LimitStrategyTupleSchemeFactory implements SchemeFactory {
    public LimitStrategyTupleScheme getScheme() {
      return new LimitStrategyTupleScheme();
    }
  }

  private static class LimitStrategyTupleScheme extends TupleScheme<LimitStrategy> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, LimitStrategy struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSubID()) {
        optionals.set(0);
      }
      if (struct.isSetMold()) {
        optionals.set(1);
      }
      if (struct.isSetValue()) {
        optionals.set(2);
      }
      if (struct.isSetDesc()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetSubID()) {
        oprot.writeI64(struct.subID);
      }
      if (struct.isSetMold()) {
        oprot.writeI64(struct.mold);
      }
      if (struct.isSetValue()) {
        oprot.writeI64(struct.value);
      }
      if (struct.isSetDesc()) {
        oprot.writeString(struct.desc);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, LimitStrategy struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.subID = iprot.readI64();
        struct.setSubIDIsSet(true);
      }
      if (incoming.get(1)) {
        struct.mold = iprot.readI64();
        struct.setMoldIsSet(true);
      }
      if (incoming.get(2)) {
        struct.value = iprot.readI64();
        struct.setValueIsSet(true);
      }
      if (incoming.get(3)) {
        struct.desc = iprot.readString();
        struct.setDescIsSet(true);
      }
    }
  }

}

