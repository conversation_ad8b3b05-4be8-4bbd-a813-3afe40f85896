/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 禁止用户修改个人资料(昵称/签名/个性签名)
 * @param   appkey        客户端标识
 * @param   uid           被封禁uid
 * @param   beginTime     封禁开始时间，格式为"2018-09-13 09:08:20"
 * @param   endTime       封禁结束时间，格式为"2018-09-14 21:10:00"
 * @param   reasonCode    封禁原因: 0 - 违规; 1 - 公会uid
 * 
 * @SaResponse			  返回值rescode，设置成功返回0
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class SaRequestLmtUptUser implements org.apache.thrift.TBase<SaRequestLmtUptUser, SaRequestLmtUptUser._Fields>, java.io.Serializable, Cloneable, Comparable<SaRequestLmtUptUser> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaRequestLmtUptUser");

  private static final org.apache.thrift.protocol.TField AUTH_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authMsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPKEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appkey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("beginTime", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField REASON_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("reasonCode", org.apache.thrift.protocol.TType.I16, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaRequestLmtUptUserStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaRequestLmtUptUserTupleSchemeFactory());
  }

  public AuthorizeMsg authMsg; // required
  public String appkey; // required
  public String uid; // required
  public String beginTime; // required
  public String endTime; // required
  public short reasonCode; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_MSG((short)1, "authMsg"),
    APPKEY((short)2, "appkey"),
    UID((short)3, "uid"),
    BEGIN_TIME((short)4, "beginTime"),
    END_TIME((short)5, "endTime"),
    REASON_CODE((short)6, "reasonCode");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_MSG
          return AUTH_MSG;
        case 2: // APPKEY
          return APPKEY;
        case 3: // UID
          return UID;
        case 4: // BEGIN_TIME
          return BEGIN_TIME;
        case 5: // END_TIME
          return END_TIME;
        case 6: // REASON_CODE
          return REASON_CODE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __REASONCODE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_MSG, new org.apache.thrift.meta_data.FieldMetaData("authMsg", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPKEY, new org.apache.thrift.meta_data.FieldMetaData("appkey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("beginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REASON_CODE, new org.apache.thrift.meta_data.FieldMetaData("reasonCode", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I16)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaRequestLmtUptUser.class, metaDataMap);
  }

  public SaRequestLmtUptUser() {
  }

  public SaRequestLmtUptUser(
    AuthorizeMsg authMsg,
    String appkey,
    String uid,
    String beginTime,
    String endTime,
    short reasonCode)
  {
    this();
    this.authMsg = authMsg;
    this.appkey = appkey;
    this.uid = uid;
    this.beginTime = beginTime;
    this.endTime = endTime;
    this.reasonCode = reasonCode;
    setReasonCodeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaRequestLmtUptUser(SaRequestLmtUptUser other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAuthMsg()) {
      this.authMsg = new AuthorizeMsg(other.authMsg);
    }
    if (other.isSetAppkey()) {
      this.appkey = other.appkey;
    }
    if (other.isSetUid()) {
      this.uid = other.uid;
    }
    if (other.isSetBeginTime()) {
      this.beginTime = other.beginTime;
    }
    if (other.isSetEndTime()) {
      this.endTime = other.endTime;
    }
    this.reasonCode = other.reasonCode;
  }

  public SaRequestLmtUptUser deepCopy() {
    return new SaRequestLmtUptUser(this);
  }

  @Override
  public void clear() {
    this.authMsg = null;
    this.appkey = null;
    this.uid = null;
    this.beginTime = null;
    this.endTime = null;
    setReasonCodeIsSet(false);
    this.reasonCode = 0;
  }

  public AuthorizeMsg getAuthMsg() {
    return this.authMsg;
  }

  public SaRequestLmtUptUser setAuthMsg(AuthorizeMsg authMsg) {
    this.authMsg = authMsg;
    return this;
  }

  public void unsetAuthMsg() {
    this.authMsg = null;
  }

  /** Returns true if field authMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthMsg() {
    return this.authMsg != null;
  }

  public void setAuthMsgIsSet(boolean value) {
    if (!value) {
      this.authMsg = null;
    }
  }

  public String getAppkey() {
    return this.appkey;
  }

  public SaRequestLmtUptUser setAppkey(String appkey) {
    this.appkey = appkey;
    return this;
  }

  public void unsetAppkey() {
    this.appkey = null;
  }

  /** Returns true if field appkey is set (has been assigned a value) and false otherwise */
  public boolean isSetAppkey() {
    return this.appkey != null;
  }

  public void setAppkeyIsSet(boolean value) {
    if (!value) {
      this.appkey = null;
    }
  }

  public String getUid() {
    return this.uid;
  }

  public SaRequestLmtUptUser setUid(String uid) {
    this.uid = uid;
    return this;
  }

  public void unsetUid() {
    this.uid = null;
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return this.uid != null;
  }

  public void setUidIsSet(boolean value) {
    if (!value) {
      this.uid = null;
    }
  }

  public String getBeginTime() {
    return this.beginTime;
  }

  public SaRequestLmtUptUser setBeginTime(String beginTime) {
    this.beginTime = beginTime;
    return this;
  }

  public void unsetBeginTime() {
    this.beginTime = null;
  }

  /** Returns true if field beginTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBeginTime() {
    return this.beginTime != null;
  }

  public void setBeginTimeIsSet(boolean value) {
    if (!value) {
      this.beginTime = null;
    }
  }

  public String getEndTime() {
    return this.endTime;
  }

  public SaRequestLmtUptUser setEndTime(String endTime) {
    this.endTime = endTime;
    return this;
  }

  public void unsetEndTime() {
    this.endTime = null;
  }

  /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
  public boolean isSetEndTime() {
    return this.endTime != null;
  }

  public void setEndTimeIsSet(boolean value) {
    if (!value) {
      this.endTime = null;
    }
  }

  public short getReasonCode() {
    return this.reasonCode;
  }

  public SaRequestLmtUptUser setReasonCode(short reasonCode) {
    this.reasonCode = reasonCode;
    setReasonCodeIsSet(true);
    return this;
  }

  public void unsetReasonCode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REASONCODE_ISSET_ID);
  }

  /** Returns true if field reasonCode is set (has been assigned a value) and false otherwise */
  public boolean isSetReasonCode() {
    return EncodingUtils.testBit(__isset_bitfield, __REASONCODE_ISSET_ID);
  }

  public void setReasonCodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REASONCODE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_MSG:
      if (value == null) {
        unsetAuthMsg();
      } else {
        setAuthMsg((AuthorizeMsg)value);
      }
      break;

    case APPKEY:
      if (value == null) {
        unsetAppkey();
      } else {
        setAppkey((String)value);
      }
      break;

    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((String)value);
      }
      break;

    case BEGIN_TIME:
      if (value == null) {
        unsetBeginTime();
      } else {
        setBeginTime((String)value);
      }
      break;

    case END_TIME:
      if (value == null) {
        unsetEndTime();
      } else {
        setEndTime((String)value);
      }
      break;

    case REASON_CODE:
      if (value == null) {
        unsetReasonCode();
      } else {
        setReasonCode((Short)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_MSG:
      return getAuthMsg();

    case APPKEY:
      return getAppkey();

    case UID:
      return getUid();

    case BEGIN_TIME:
      return getBeginTime();

    case END_TIME:
      return getEndTime();

    case REASON_CODE:
      return getReasonCode();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_MSG:
      return isSetAuthMsg();
    case APPKEY:
      return isSetAppkey();
    case UID:
      return isSetUid();
    case BEGIN_TIME:
      return isSetBeginTime();
    case END_TIME:
      return isSetEndTime();
    case REASON_CODE:
      return isSetReasonCode();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaRequestLmtUptUser)
      return this.equals((SaRequestLmtUptUser)that);
    return false;
  }

  public boolean equals(SaRequestLmtUptUser that) {
    if (that == null)
      return false;

    boolean this_present_authMsg = true && this.isSetAuthMsg();
    boolean that_present_authMsg = true && that.isSetAuthMsg();
    if (this_present_authMsg || that_present_authMsg) {
      if (!(this_present_authMsg && that_present_authMsg))
        return false;
      if (!this.authMsg.equals(that.authMsg))
        return false;
    }

    boolean this_present_appkey = true && this.isSetAppkey();
    boolean that_present_appkey = true && that.isSetAppkey();
    if (this_present_appkey || that_present_appkey) {
      if (!(this_present_appkey && that_present_appkey))
        return false;
      if (!this.appkey.equals(that.appkey))
        return false;
    }

    boolean this_present_uid = true && this.isSetUid();
    boolean that_present_uid = true && that.isSetUid();
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (!this.uid.equals(that.uid))
        return false;
    }

    boolean this_present_beginTime = true && this.isSetBeginTime();
    boolean that_present_beginTime = true && that.isSetBeginTime();
    if (this_present_beginTime || that_present_beginTime) {
      if (!(this_present_beginTime && that_present_beginTime))
        return false;
      if (!this.beginTime.equals(that.beginTime))
        return false;
    }

    boolean this_present_endTime = true && this.isSetEndTime();
    boolean that_present_endTime = true && that.isSetEndTime();
    if (this_present_endTime || that_present_endTime) {
      if (!(this_present_endTime && that_present_endTime))
        return false;
      if (!this.endTime.equals(that.endTime))
        return false;
    }

    boolean this_present_reasonCode = true;
    boolean that_present_reasonCode = true;
    if (this_present_reasonCode || that_present_reasonCode) {
      if (!(this_present_reasonCode && that_present_reasonCode))
        return false;
      if (this.reasonCode != that.reasonCode)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authMsg = true && (isSetAuthMsg());
    list.add(present_authMsg);
    if (present_authMsg)
      list.add(authMsg);

    boolean present_appkey = true && (isSetAppkey());
    list.add(present_appkey);
    if (present_appkey)
      list.add(appkey);

    boolean present_uid = true && (isSetUid());
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    boolean present_beginTime = true && (isSetBeginTime());
    list.add(present_beginTime);
    if (present_beginTime)
      list.add(beginTime);

    boolean present_endTime = true && (isSetEndTime());
    list.add(present_endTime);
    if (present_endTime)
      list.add(endTime);

    boolean present_reasonCode = true;
    list.add(present_reasonCode);
    if (present_reasonCode)
      list.add(reasonCode);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaRequestLmtUptUser other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthMsg()).compareTo(other.isSetAuthMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authMsg, other.authMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppkey()).compareTo(other.isSetAppkey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppkey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appkey, other.appkey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBeginTime()).compareTo(other.isSetBeginTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBeginTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.beginTime, other.beginTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEndTime()).compareTo(other.isSetEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReasonCode()).compareTo(other.isSetReasonCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReasonCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reasonCode, other.reasonCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaRequestLmtUptUser(");
    boolean first = true;

    sb.append("authMsg:");
    if (this.authMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authMsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appkey:");
    if (this.appkey == null) {
      sb.append("null");
    } else {
      sb.append(this.appkey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("uid:");
    if (this.uid == null) {
      sb.append("null");
    } else {
      sb.append(this.uid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("beginTime:");
    if (this.beginTime == null) {
      sb.append("null");
    } else {
      sb.append(this.beginTime);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("endTime:");
    if (this.endTime == null) {
      sb.append("null");
    } else {
      sb.append(this.endTime);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("reasonCode:");
    sb.append(this.reasonCode);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
    if (authMsg != null) {
      authMsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaRequestLmtUptUserStandardSchemeFactory implements SchemeFactory {
    public SaRequestLmtUptUserStandardScheme getScheme() {
      return new SaRequestLmtUptUserStandardScheme();
    }
  }

  private static class SaRequestLmtUptUserStandardScheme extends StandardScheme<SaRequestLmtUptUser> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaRequestLmtUptUser struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authMsg = new AuthorizeMsg();
              struct.authMsg.read(iprot);
              struct.setAuthMsgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPKEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appkey = iprot.readString();
              struct.setAppkeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.uid = iprot.readString();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // BEGIN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.beginTime = iprot.readString();
              struct.setBeginTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.endTime = iprot.readString();
              struct.setEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // REASON_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I16) {
              struct.reasonCode = iprot.readI16();
              struct.setReasonCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaRequestLmtUptUser struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authMsg != null) {
        oprot.writeFieldBegin(AUTH_MSG_FIELD_DESC);
        struct.authMsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appkey != null) {
        oprot.writeFieldBegin(APPKEY_FIELD_DESC);
        oprot.writeString(struct.appkey);
        oprot.writeFieldEnd();
      }
      if (struct.uid != null) {
        oprot.writeFieldBegin(UID_FIELD_DESC);
        oprot.writeString(struct.uid);
        oprot.writeFieldEnd();
      }
      if (struct.beginTime != null) {
        oprot.writeFieldBegin(BEGIN_TIME_FIELD_DESC);
        oprot.writeString(struct.beginTime);
        oprot.writeFieldEnd();
      }
      if (struct.endTime != null) {
        oprot.writeFieldBegin(END_TIME_FIELD_DESC);
        oprot.writeString(struct.endTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(REASON_CODE_FIELD_DESC);
      oprot.writeI16(struct.reasonCode);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaRequestLmtUptUserTupleSchemeFactory implements SchemeFactory {
    public SaRequestLmtUptUserTupleScheme getScheme() {
      return new SaRequestLmtUptUserTupleScheme();
    }
  }

  private static class SaRequestLmtUptUserTupleScheme extends TupleScheme<SaRequestLmtUptUser> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaRequestLmtUptUser struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthMsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppkey()) {
        optionals.set(1);
      }
      if (struct.isSetUid()) {
        optionals.set(2);
      }
      if (struct.isSetBeginTime()) {
        optionals.set(3);
      }
      if (struct.isSetEndTime()) {
        optionals.set(4);
      }
      if (struct.isSetReasonCode()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetAuthMsg()) {
        struct.authMsg.write(oprot);
      }
      if (struct.isSetAppkey()) {
        oprot.writeString(struct.appkey);
      }
      if (struct.isSetUid()) {
        oprot.writeString(struct.uid);
      }
      if (struct.isSetBeginTime()) {
        oprot.writeString(struct.beginTime);
      }
      if (struct.isSetEndTime()) {
        oprot.writeString(struct.endTime);
      }
      if (struct.isSetReasonCode()) {
        oprot.writeI16(struct.reasonCode);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaRequestLmtUptUser struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.authMsg = new AuthorizeMsg();
        struct.authMsg.read(iprot);
        struct.setAuthMsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appkey = iprot.readString();
        struct.setAppkeyIsSet(true);
      }
      if (incoming.get(2)) {
        struct.uid = iprot.readString();
        struct.setUidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.beginTime = iprot.readString();
        struct.setBeginTimeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.endTime = iprot.readString();
        struct.setEndTimeIsSet(true);
      }
      if (incoming.get(5)) {
        struct.reasonCode = iprot.readI16();
        struct.setReasonCodeIsSet(true);
      }
    }
  }

}

