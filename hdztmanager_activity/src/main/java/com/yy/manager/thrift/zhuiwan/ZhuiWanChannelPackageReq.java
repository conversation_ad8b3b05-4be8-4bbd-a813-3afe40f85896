/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.zhuiwan;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class ZhuiWanChannelPackageReq implements org.apache.thrift.TBase<ZhuiWanChannelPackageReq, ZhuiWanChannelPackageReq._Fields>, java.io.Serializable, Cloneable, Comparable<ZhuiWanChannelPackageReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ZhuiWanChannelPackageReq");

  private static final org.apache.thrift.protocol.TField PACKAGE_INFO_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("packageInfoList", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ZhuiWanChannelPackageReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ZhuiWanChannelPackageReqTupleSchemeFactory());
  }

  public List<ChannelPackageInfo> packageInfoList; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    PACKAGE_INFO_LIST((short)1, "packageInfoList"),
    EXT_DATA((short)2, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // PACKAGE_INFO_LIST
          return PACKAGE_INFO_LIST;
        case 2: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.PACKAGE_INFO_LIST, new org.apache.thrift.meta_data.FieldMetaData("packageInfoList", org.apache.thrift.TFieldRequirementType.REQUIRED,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "ChannelPackageInfo"))));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.REQUIRED,
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING),
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ZhuiWanChannelPackageReq.class, metaDataMap);
  }

  public ZhuiWanChannelPackageReq() {
  }

  public ZhuiWanChannelPackageReq(
    List<ChannelPackageInfo> packageInfoList,
    Map<String,String> extData)
  {
    this();
    this.packageInfoList = packageInfoList;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ZhuiWanChannelPackageReq(ZhuiWanChannelPackageReq other) {
    if (other.isSetPackageInfoList()) {
      List<ChannelPackageInfo> __this__packageInfoList = new ArrayList<ChannelPackageInfo>(other.packageInfoList.size());
      for (ChannelPackageInfo other_element : other.packageInfoList) {
        __this__packageInfoList.add(other_element);
      }
      this.packageInfoList = __this__packageInfoList;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public ZhuiWanChannelPackageReq deepCopy() {
    return new ZhuiWanChannelPackageReq(this);
  }

  @Override
  public void clear() {
    this.packageInfoList = null;
    this.extData = null;
  }

  public int getPackageInfoListSize() {
    return (this.packageInfoList == null) ? 0 : this.packageInfoList.size();
  }

  public java.util.Iterator<ChannelPackageInfo> getPackageInfoListIterator() {
    return (this.packageInfoList == null) ? null : this.packageInfoList.iterator();
  }

  public void addToPackageInfoList(ChannelPackageInfo elem) {
    if (this.packageInfoList == null) {
      this.packageInfoList = new ArrayList<ChannelPackageInfo>();
    }
    this.packageInfoList.add(elem);
  }

  public List<ChannelPackageInfo> getPackageInfoList() {
    return this.packageInfoList;
  }

  public ZhuiWanChannelPackageReq setPackageInfoList(List<ChannelPackageInfo> packageInfoList) {
    this.packageInfoList = packageInfoList;
    return this;
  }

  public void unsetPackageInfoList() {
    this.packageInfoList = null;
  }

  /** Returns true if field packageInfoList is set (has been assigned a value) and false otherwise */
  public boolean isSetPackageInfoList() {
    return this.packageInfoList != null;
  }

  public void setPackageInfoListIsSet(boolean value) {
    if (!value) {
      this.packageInfoList = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public ZhuiWanChannelPackageReq setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case PACKAGE_INFO_LIST:
      if (value == null) {
        unsetPackageInfoList();
      } else {
        setPackageInfoList((List<ChannelPackageInfo>)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case PACKAGE_INFO_LIST:
      return getPackageInfoList();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case PACKAGE_INFO_LIST:
      return isSetPackageInfoList();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ZhuiWanChannelPackageReq)
      return this.equals((ZhuiWanChannelPackageReq)that);
    return false;
  }

  public boolean equals(ZhuiWanChannelPackageReq that) {
    if (that == null)
      return false;

    boolean this_present_packageInfoList = true && this.isSetPackageInfoList();
    boolean that_present_packageInfoList = true && that.isSetPackageInfoList();
    if (this_present_packageInfoList || that_present_packageInfoList) {
      if (!(this_present_packageInfoList && that_present_packageInfoList))
        return false;
      if (!this.packageInfoList.equals(that.packageInfoList))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_packageInfoList = true && (isSetPackageInfoList());
    list.add(present_packageInfoList);
    if (present_packageInfoList)
      list.add(packageInfoList);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(ZhuiWanChannelPackageReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetPackageInfoList()).compareTo(other.isSetPackageInfoList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPackageInfoList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.packageInfoList, other.packageInfoList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ZhuiWanChannelPackageReq(");
    boolean first = true;

    sb.append("packageInfoList:");
    if (this.packageInfoList == null) {
      sb.append("null");
    } else {
      sb.append(this.packageInfoList);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (packageInfoList == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'packageInfoList' was not present! Struct: " + toString());
    }
    if (extData == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'extData' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ZhuiWanChannelPackageReqStandardSchemeFactory implements SchemeFactory {
    public ZhuiWanChannelPackageReqStandardScheme getScheme() {
      return new ZhuiWanChannelPackageReqStandardScheme();
    }
  }

  private static class ZhuiWanChannelPackageReqStandardScheme extends StandardScheme<ZhuiWanChannelPackageReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ZhuiWanChannelPackageReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // PACKAGE_INFO_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list58 = iprot.readListBegin();
                struct.packageInfoList = new ArrayList<ChannelPackageInfo>(_list58.size);
                ChannelPackageInfo _elem59;
                for (int _i60 = 0; _i60 < _list58.size; ++_i60)
                {
                  _elem59 = new ChannelPackageInfo();
                  _elem59.read(iprot);
                  struct.packageInfoList.add(_elem59);
                }
                iprot.readListEnd();
              }
              struct.setPackageInfoListIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map61 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map61.size);
                String _key62;
                String _val63;
                for (int _i64 = 0; _i64 < _map61.size; ++_i64)
                {
                  _key62 = iprot.readString();
                  _val63 = iprot.readString();
                  struct.extData.put(_key62, _val63);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ZhuiWanChannelPackageReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.packageInfoList != null) {
        oprot.writeFieldBegin(PACKAGE_INFO_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.packageInfoList.size()));
          for (ChannelPackageInfo _iter65 : struct.packageInfoList)
          {
            _iter65.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter66 : struct.extData.entrySet())
          {
            oprot.writeString(_iter66.getKey());
            oprot.writeString(_iter66.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ZhuiWanChannelPackageReqTupleSchemeFactory implements SchemeFactory {
    public ZhuiWanChannelPackageReqTupleScheme getScheme() {
      return new ZhuiWanChannelPackageReqTupleScheme();
    }
  }

  private static class ZhuiWanChannelPackageReqTupleScheme extends TupleScheme<ZhuiWanChannelPackageReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ZhuiWanChannelPackageReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      {
        oprot.writeI32(struct.packageInfoList.size());
        for (ChannelPackageInfo _iter67 : struct.packageInfoList)
        {
          _iter67.write(oprot);
        }
      }
      {
        oprot.writeI32(struct.extData.size());
        for (Map.Entry<String, String> _iter68 : struct.extData.entrySet())
        {
          oprot.writeString(_iter68.getKey());
          oprot.writeString(_iter68.getValue());
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ZhuiWanChannelPackageReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      {
        org.apache.thrift.protocol.TList _list69 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
        struct.packageInfoList = new ArrayList<ChannelPackageInfo>(_list69.size);
        ChannelPackageInfo _elem70;
        for (int _i71 = 0; _i71 < _list69.size; ++_i71)
        {
          _elem70 = new ChannelPackageInfo();
          _elem70.read(iprot);
          struct.packageInfoList.add(_elem70);
        }
      }
      struct.setPackageInfoListIsSet(true);
      {
        org.apache.thrift.protocol.TMap _map72 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
        struct.extData = new HashMap<String,String>(2*_map72.size);
        String _key73;
        String _val74;
        for (int _i75 = 0; _i75 < _map72.size; ++_i75)
        {
          _key73 = iprot.readString();
          _val74 = iprot.readString();
          struct.extData.put(_key73, _val74);
        }
      }
      struct.setExtDataIsSet(true);
    }
  }

}

