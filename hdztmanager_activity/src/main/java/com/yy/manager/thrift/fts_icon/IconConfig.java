/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.fts_icon;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-08-21")
public class IconConfig implements org.apache.thrift.TBase<IconConfig, IconConfig._Fields>, java.io.Serializable, Cloneable, Comparable<IconConfig> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("IconConfig");

  private static final org.apache.thrift.protocol.TField ICON_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("icon_id", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("start_time", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("end_time", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SERVICE_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("service_name", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField PREFIX_FIELD_DESC = new org.apache.thrift.protocol.TField("prefix", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField SUPPORT_DOWNGRADE_FIELD_DESC = new org.apache.thrift.protocol.TField("support_downgrade", org.apache.thrift.protocol.TType.BOOL, (short)6);
  private static final org.apache.thrift.protocol.TField ICON_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("icon_type", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField ACTIVITY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("activity_id", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField SMOKE_START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("smoke_start_time", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField SMOKE_END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("smoke_end_time", org.apache.thrift.protocol.TType.I64, (short)10);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new IconConfigStandardSchemeFactory());
    schemes.put(TupleScheme.class, new IconConfigTupleSchemeFactory());
  }

  public int icon_id; // required
  public long start_time; // required
  public long end_time; // required
  public String service_name; // required
  public String prefix; // required
  public boolean support_downgrade; // required
  public int icon_type; // required
  public int activity_id; // required
  public long smoke_start_time; // required
  public long smoke_end_time; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ICON_ID((short)1, "icon_id"),
    START_TIME((short)2, "start_time"),
    END_TIME((short)3, "end_time"),
    SERVICE_NAME((short)4, "service_name"),
    PREFIX((short)5, "prefix"),
    SUPPORT_DOWNGRADE((short)6, "support_downgrade"),
    ICON_TYPE((short)7, "icon_type"),
    ACTIVITY_ID((short)8, "activity_id"),
    SMOKE_START_TIME((short)9, "smoke_start_time"),
    SMOKE_END_TIME((short)10, "smoke_end_time");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ICON_ID
          return ICON_ID;
        case 2: // START_TIME
          return START_TIME;
        case 3: // END_TIME
          return END_TIME;
        case 4: // SERVICE_NAME
          return SERVICE_NAME;
        case 5: // PREFIX
          return PREFIX;
        case 6: // SUPPORT_DOWNGRADE
          return SUPPORT_DOWNGRADE;
        case 7: // ICON_TYPE
          return ICON_TYPE;
        case 8: // ACTIVITY_ID
          return ACTIVITY_ID;
        case 9: // SMOKE_START_TIME
          return SMOKE_START_TIME;
        case 10: // SMOKE_END_TIME
          return SMOKE_END_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ICON_ID_ISSET_ID = 0;
  private static final int __START_TIME_ISSET_ID = 1;
  private static final int __END_TIME_ISSET_ID = 2;
  private static final int __SUPPORT_DOWNGRADE_ISSET_ID = 3;
  private static final int __ICON_TYPE_ISSET_ID = 4;
  private static final int __ACTIVITY_ID_ISSET_ID = 5;
  private static final int __SMOKE_START_TIME_ISSET_ID = 6;
  private static final int __SMOKE_END_TIME_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ICON_ID, new org.apache.thrift.meta_data.FieldMetaData("icon_id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.START_TIME, new org.apache.thrift.meta_data.FieldMetaData("start_time", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("end_time", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SERVICE_NAME, new org.apache.thrift.meta_data.FieldMetaData("service_name", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PREFIX, new org.apache.thrift.meta_data.FieldMetaData("prefix", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SUPPORT_DOWNGRADE, new org.apache.thrift.meta_data.FieldMetaData("support_downgrade", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.ICON_TYPE, new org.apache.thrift.meta_data.FieldMetaData("icon_type", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ACTIVITY_ID, new org.apache.thrift.meta_data.FieldMetaData("activity_id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SMOKE_START_TIME, new org.apache.thrift.meta_data.FieldMetaData("smoke_start_time", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SMOKE_END_TIME, new org.apache.thrift.meta_data.FieldMetaData("smoke_end_time", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(IconConfig.class, metaDataMap);
  }

  public IconConfig() {
  }

  public IconConfig(
    int icon_id,
    long start_time,
    long end_time,
    String service_name,
    String prefix,
    boolean support_downgrade,
    int icon_type,
    int activity_id,
    long smoke_start_time,
    long smoke_end_time)
  {
    this();
    this.icon_id = icon_id;
    setIcon_idIsSet(true);
    this.start_time = start_time;
    setStart_timeIsSet(true);
    this.end_time = end_time;
    setEnd_timeIsSet(true);
    this.service_name = service_name;
    this.prefix = prefix;
    this.support_downgrade = support_downgrade;
    setSupport_downgradeIsSet(true);
    this.icon_type = icon_type;
    setIcon_typeIsSet(true);
    this.activity_id = activity_id;
    setActivity_idIsSet(true);
    this.smoke_start_time = smoke_start_time;
    setSmoke_start_timeIsSet(true);
    this.smoke_end_time = smoke_end_time;
    setSmoke_end_timeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public IconConfig(IconConfig other) {
    __isset_bitfield = other.__isset_bitfield;
    this.icon_id = other.icon_id;
    this.start_time = other.start_time;
    this.end_time = other.end_time;
    if (other.isSetService_name()) {
      this.service_name = other.service_name;
    }
    if (other.isSetPrefix()) {
      this.prefix = other.prefix;
    }
    this.support_downgrade = other.support_downgrade;
    this.icon_type = other.icon_type;
    this.activity_id = other.activity_id;
    this.smoke_start_time = other.smoke_start_time;
    this.smoke_end_time = other.smoke_end_time;
  }

  public IconConfig deepCopy() {
    return new IconConfig(this);
  }

  @Override
  public void clear() {
    setIcon_idIsSet(false);
    this.icon_id = 0;
    setStart_timeIsSet(false);
    this.start_time = 0;
    setEnd_timeIsSet(false);
    this.end_time = 0;
    this.service_name = null;
    this.prefix = null;
    setSupport_downgradeIsSet(false);
    this.support_downgrade = false;
    setIcon_typeIsSet(false);
    this.icon_type = 0;
    setActivity_idIsSet(false);
    this.activity_id = 0;
    setSmoke_start_timeIsSet(false);
    this.smoke_start_time = 0;
    setSmoke_end_timeIsSet(false);
    this.smoke_end_time = 0;
  }

  public int getIcon_id() {
    return this.icon_id;
  }

  public IconConfig setIcon_id(int icon_id) {
    this.icon_id = icon_id;
    setIcon_idIsSet(true);
    return this;
  }

  public void unsetIcon_id() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ICON_ID_ISSET_ID);
  }

  /** Returns true if field icon_id is set (has been assigned a value) and false otherwise */
  public boolean isSetIcon_id() {
    return EncodingUtils.testBit(__isset_bitfield, __ICON_ID_ISSET_ID);
  }

  public void setIcon_idIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ICON_ID_ISSET_ID, value);
  }

  public long getStart_time() {
    return this.start_time;
  }

  public IconConfig setStart_time(long start_time) {
    this.start_time = start_time;
    setStart_timeIsSet(true);
    return this;
  }

  public void unsetStart_time() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __START_TIME_ISSET_ID);
  }

  /** Returns true if field start_time is set (has been assigned a value) and false otherwise */
  public boolean isSetStart_time() {
    return EncodingUtils.testBit(__isset_bitfield, __START_TIME_ISSET_ID);
  }

  public void setStart_timeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __START_TIME_ISSET_ID, value);
  }

  public long getEnd_time() {
    return this.end_time;
  }

  public IconConfig setEnd_time(long end_time) {
    this.end_time = end_time;
    setEnd_timeIsSet(true);
    return this;
  }

  public void unsetEnd_time() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __END_TIME_ISSET_ID);
  }

  /** Returns true if field end_time is set (has been assigned a value) and false otherwise */
  public boolean isSetEnd_time() {
    return EncodingUtils.testBit(__isset_bitfield, __END_TIME_ISSET_ID);
  }

  public void setEnd_timeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __END_TIME_ISSET_ID, value);
  }

  public String getService_name() {
    return this.service_name;
  }

  public IconConfig setService_name(String service_name) {
    this.service_name = service_name;
    return this;
  }

  public void unsetService_name() {
    this.service_name = null;
  }

  /** Returns true if field service_name is set (has been assigned a value) and false otherwise */
  public boolean isSetService_name() {
    return this.service_name != null;
  }

  public void setService_nameIsSet(boolean value) {
    if (!value) {
      this.service_name = null;
    }
  }

  public String getPrefix() {
    return this.prefix;
  }

  public IconConfig setPrefix(String prefix) {
    this.prefix = prefix;
    return this;
  }

  public void unsetPrefix() {
    this.prefix = null;
  }

  /** Returns true if field prefix is set (has been assigned a value) and false otherwise */
  public boolean isSetPrefix() {
    return this.prefix != null;
  }

  public void setPrefixIsSet(boolean value) {
    if (!value) {
      this.prefix = null;
    }
  }

  public boolean isSupport_downgrade() {
    return this.support_downgrade;
  }

  public IconConfig setSupport_downgrade(boolean support_downgrade) {
    this.support_downgrade = support_downgrade;
    setSupport_downgradeIsSet(true);
    return this;
  }

  public void unsetSupport_downgrade() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUPPORT_DOWNGRADE_ISSET_ID);
  }

  /** Returns true if field support_downgrade is set (has been assigned a value) and false otherwise */
  public boolean isSetSupport_downgrade() {
    return EncodingUtils.testBit(__isset_bitfield, __SUPPORT_DOWNGRADE_ISSET_ID);
  }

  public void setSupport_downgradeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUPPORT_DOWNGRADE_ISSET_ID, value);
  }

  public int getIcon_type() {
    return this.icon_type;
  }

  public IconConfig setIcon_type(int icon_type) {
    this.icon_type = icon_type;
    setIcon_typeIsSet(true);
    return this;
  }

  public void unsetIcon_type() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ICON_TYPE_ISSET_ID);
  }

  /** Returns true if field icon_type is set (has been assigned a value) and false otherwise */
  public boolean isSetIcon_type() {
    return EncodingUtils.testBit(__isset_bitfield, __ICON_TYPE_ISSET_ID);
  }

  public void setIcon_typeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ICON_TYPE_ISSET_ID, value);
  }

  public int getActivity_id() {
    return this.activity_id;
  }

  public IconConfig setActivity_id(int activity_id) {
    this.activity_id = activity_id;
    setActivity_idIsSet(true);
    return this;
  }

  public void unsetActivity_id() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTIVITY_ID_ISSET_ID);
  }

  /** Returns true if field activity_id is set (has been assigned a value) and false otherwise */
  public boolean isSetActivity_id() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTIVITY_ID_ISSET_ID);
  }

  public void setActivity_idIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTIVITY_ID_ISSET_ID, value);
  }

  public long getSmoke_start_time() {
    return this.smoke_start_time;
  }

  public IconConfig setSmoke_start_time(long smoke_start_time) {
    this.smoke_start_time = smoke_start_time;
    setSmoke_start_timeIsSet(true);
    return this;
  }

  public void unsetSmoke_start_time() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SMOKE_START_TIME_ISSET_ID);
  }

  /** Returns true if field smoke_start_time is set (has been assigned a value) and false otherwise */
  public boolean isSetSmoke_start_time() {
    return EncodingUtils.testBit(__isset_bitfield, __SMOKE_START_TIME_ISSET_ID);
  }

  public void setSmoke_start_timeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SMOKE_START_TIME_ISSET_ID, value);
  }

  public long getSmoke_end_time() {
    return this.smoke_end_time;
  }

  public IconConfig setSmoke_end_time(long smoke_end_time) {
    this.smoke_end_time = smoke_end_time;
    setSmoke_end_timeIsSet(true);
    return this;
  }

  public void unsetSmoke_end_time() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SMOKE_END_TIME_ISSET_ID);
  }

  /** Returns true if field smoke_end_time is set (has been assigned a value) and false otherwise */
  public boolean isSetSmoke_end_time() {
    return EncodingUtils.testBit(__isset_bitfield, __SMOKE_END_TIME_ISSET_ID);
  }

  public void setSmoke_end_timeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SMOKE_END_TIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ICON_ID:
      if (value == null) {
        unsetIcon_id();
      } else {
        setIcon_id((Integer)value);
      }
      break;

    case START_TIME:
      if (value == null) {
        unsetStart_time();
      } else {
        setStart_time((Long)value);
      }
      break;

    case END_TIME:
      if (value == null) {
        unsetEnd_time();
      } else {
        setEnd_time((Long)value);
      }
      break;

    case SERVICE_NAME:
      if (value == null) {
        unsetService_name();
      } else {
        setService_name((String)value);
      }
      break;

    case PREFIX:
      if (value == null) {
        unsetPrefix();
      } else {
        setPrefix((String)value);
      }
      break;

    case SUPPORT_DOWNGRADE:
      if (value == null) {
        unsetSupport_downgrade();
      } else {
        setSupport_downgrade((Boolean)value);
      }
      break;

    case ICON_TYPE:
      if (value == null) {
        unsetIcon_type();
      } else {
        setIcon_type((Integer)value);
      }
      break;

    case ACTIVITY_ID:
      if (value == null) {
        unsetActivity_id();
      } else {
        setActivity_id((Integer)value);
      }
      break;

    case SMOKE_START_TIME:
      if (value == null) {
        unsetSmoke_start_time();
      } else {
        setSmoke_start_time((Long)value);
      }
      break;

    case SMOKE_END_TIME:
      if (value == null) {
        unsetSmoke_end_time();
      } else {
        setSmoke_end_time((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ICON_ID:
      return getIcon_id();

    case START_TIME:
      return getStart_time();

    case END_TIME:
      return getEnd_time();

    case SERVICE_NAME:
      return getService_name();

    case PREFIX:
      return getPrefix();

    case SUPPORT_DOWNGRADE:
      return isSupport_downgrade();

    case ICON_TYPE:
      return getIcon_type();

    case ACTIVITY_ID:
      return getActivity_id();

    case SMOKE_START_TIME:
      return getSmoke_start_time();

    case SMOKE_END_TIME:
      return getSmoke_end_time();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ICON_ID:
      return isSetIcon_id();
    case START_TIME:
      return isSetStart_time();
    case END_TIME:
      return isSetEnd_time();
    case SERVICE_NAME:
      return isSetService_name();
    case PREFIX:
      return isSetPrefix();
    case SUPPORT_DOWNGRADE:
      return isSetSupport_downgrade();
    case ICON_TYPE:
      return isSetIcon_type();
    case ACTIVITY_ID:
      return isSetActivity_id();
    case SMOKE_START_TIME:
      return isSetSmoke_start_time();
    case SMOKE_END_TIME:
      return isSetSmoke_end_time();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof IconConfig)
      return this.equals((IconConfig)that);
    return false;
  }

  public boolean equals(IconConfig that) {
    if (that == null)
      return false;

    boolean this_present_icon_id = true;
    boolean that_present_icon_id = true;
    if (this_present_icon_id || that_present_icon_id) {
      if (!(this_present_icon_id && that_present_icon_id))
        return false;
      if (this.icon_id != that.icon_id)
        return false;
    }

    boolean this_present_start_time = true;
    boolean that_present_start_time = true;
    if (this_present_start_time || that_present_start_time) {
      if (!(this_present_start_time && that_present_start_time))
        return false;
      if (this.start_time != that.start_time)
        return false;
    }

    boolean this_present_end_time = true;
    boolean that_present_end_time = true;
    if (this_present_end_time || that_present_end_time) {
      if (!(this_present_end_time && that_present_end_time))
        return false;
      if (this.end_time != that.end_time)
        return false;
    }

    boolean this_present_service_name = true && this.isSetService_name();
    boolean that_present_service_name = true && that.isSetService_name();
    if (this_present_service_name || that_present_service_name) {
      if (!(this_present_service_name && that_present_service_name))
        return false;
      if (!this.service_name.equals(that.service_name))
        return false;
    }

    boolean this_present_prefix = true && this.isSetPrefix();
    boolean that_present_prefix = true && that.isSetPrefix();
    if (this_present_prefix || that_present_prefix) {
      if (!(this_present_prefix && that_present_prefix))
        return false;
      if (!this.prefix.equals(that.prefix))
        return false;
    }

    boolean this_present_support_downgrade = true;
    boolean that_present_support_downgrade = true;
    if (this_present_support_downgrade || that_present_support_downgrade) {
      if (!(this_present_support_downgrade && that_present_support_downgrade))
        return false;
      if (this.support_downgrade != that.support_downgrade)
        return false;
    }

    boolean this_present_icon_type = true;
    boolean that_present_icon_type = true;
    if (this_present_icon_type || that_present_icon_type) {
      if (!(this_present_icon_type && that_present_icon_type))
        return false;
      if (this.icon_type != that.icon_type)
        return false;
    }

    boolean this_present_activity_id = true;
    boolean that_present_activity_id = true;
    if (this_present_activity_id || that_present_activity_id) {
      if (!(this_present_activity_id && that_present_activity_id))
        return false;
      if (this.activity_id != that.activity_id)
        return false;
    }

    boolean this_present_smoke_start_time = true;
    boolean that_present_smoke_start_time = true;
    if (this_present_smoke_start_time || that_present_smoke_start_time) {
      if (!(this_present_smoke_start_time && that_present_smoke_start_time))
        return false;
      if (this.smoke_start_time != that.smoke_start_time)
        return false;
    }

    boolean this_present_smoke_end_time = true;
    boolean that_present_smoke_end_time = true;
    if (this_present_smoke_end_time || that_present_smoke_end_time) {
      if (!(this_present_smoke_end_time && that_present_smoke_end_time))
        return false;
      if (this.smoke_end_time != that.smoke_end_time)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_icon_id = true;
    list.add(present_icon_id);
    if (present_icon_id)
      list.add(icon_id);

    boolean present_start_time = true;
    list.add(present_start_time);
    if (present_start_time)
      list.add(start_time);

    boolean present_end_time = true;
    list.add(present_end_time);
    if (present_end_time)
      list.add(end_time);

    boolean present_service_name = true && (isSetService_name());
    list.add(present_service_name);
    if (present_service_name)
      list.add(service_name);

    boolean present_prefix = true && (isSetPrefix());
    list.add(present_prefix);
    if (present_prefix)
      list.add(prefix);

    boolean present_support_downgrade = true;
    list.add(present_support_downgrade);
    if (present_support_downgrade)
      list.add(support_downgrade);

    boolean present_icon_type = true;
    list.add(present_icon_type);
    if (present_icon_type)
      list.add(icon_type);

    boolean present_activity_id = true;
    list.add(present_activity_id);
    if (present_activity_id)
      list.add(activity_id);

    boolean present_smoke_start_time = true;
    list.add(present_smoke_start_time);
    if (present_smoke_start_time)
      list.add(smoke_start_time);

    boolean present_smoke_end_time = true;
    list.add(present_smoke_end_time);
    if (present_smoke_end_time)
      list.add(smoke_end_time);

    return list.hashCode();
  }

  @Override
  public int compareTo(IconConfig other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetIcon_id()).compareTo(other.isSetIcon_id());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIcon_id()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.icon_id, other.icon_id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStart_time()).compareTo(other.isSetStart_time());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStart_time()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.start_time, other.start_time);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEnd_time()).compareTo(other.isSetEnd_time());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEnd_time()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.end_time, other.end_time);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetService_name()).compareTo(other.isSetService_name());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetService_name()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.service_name, other.service_name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPrefix()).compareTo(other.isSetPrefix());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPrefix()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.prefix, other.prefix);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSupport_downgrade()).compareTo(other.isSetSupport_downgrade());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSupport_downgrade()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.support_downgrade, other.support_downgrade);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIcon_type()).compareTo(other.isSetIcon_type());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIcon_type()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.icon_type, other.icon_type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActivity_id()).compareTo(other.isSetActivity_id());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActivity_id()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.activity_id, other.activity_id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSmoke_start_time()).compareTo(other.isSetSmoke_start_time());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSmoke_start_time()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.smoke_start_time, other.smoke_start_time);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSmoke_end_time()).compareTo(other.isSetSmoke_end_time());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSmoke_end_time()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.smoke_end_time, other.smoke_end_time);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("IconConfig(");
    boolean first = true;

    sb.append("icon_id:");
    sb.append(this.icon_id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("start_time:");
    sb.append(this.start_time);
    first = false;
    if (!first) sb.append(", ");
    sb.append("end_time:");
    sb.append(this.end_time);
    first = false;
    if (!first) sb.append(", ");
    sb.append("service_name:");
    if (this.service_name == null) {
      sb.append("null");
    } else {
      sb.append(this.service_name);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("prefix:");
    if (this.prefix == null) {
      sb.append("null");
    } else {
      sb.append(this.prefix);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("support_downgrade:");
    sb.append(this.support_downgrade);
    first = false;
    if (!first) sb.append(", ");
    sb.append("icon_type:");
    sb.append(this.icon_type);
    first = false;
    if (!first) sb.append(", ");
    sb.append("activity_id:");
    sb.append(this.activity_id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("smoke_start_time:");
    sb.append(this.smoke_start_time);
    first = false;
    if (!first) sb.append(", ");
    sb.append("smoke_end_time:");
    sb.append(this.smoke_end_time);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class IconConfigStandardSchemeFactory implements SchemeFactory {
    public IconConfigStandardScheme getScheme() {
      return new IconConfigStandardScheme();
    }
  }

  private static class IconConfigStandardScheme extends StandardScheme<IconConfig> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, IconConfig struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ICON_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.icon_id = iprot.readI32();
              struct.setIcon_idIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.start_time = iprot.readI64();
              struct.setStart_timeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.end_time = iprot.readI64();
              struct.setEnd_timeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SERVICE_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.service_name = iprot.readString();
              struct.setService_nameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // PREFIX
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.prefix = iprot.readString();
              struct.setPrefixIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SUPPORT_DOWNGRADE
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.support_downgrade = iprot.readBool();
              struct.setSupport_downgradeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // ICON_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.icon_type = iprot.readI32();
              struct.setIcon_typeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // ACTIVITY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.activity_id = iprot.readI32();
              struct.setActivity_idIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // SMOKE_START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.smoke_start_time = iprot.readI64();
              struct.setSmoke_start_timeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // SMOKE_END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.smoke_end_time = iprot.readI64();
              struct.setSmoke_end_timeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, IconConfig struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ICON_ID_FIELD_DESC);
      oprot.writeI32(struct.icon_id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(START_TIME_FIELD_DESC);
      oprot.writeI64(struct.start_time);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(END_TIME_FIELD_DESC);
      oprot.writeI64(struct.end_time);
      oprot.writeFieldEnd();
      if (struct.service_name != null) {
        oprot.writeFieldBegin(SERVICE_NAME_FIELD_DESC);
        oprot.writeString(struct.service_name);
        oprot.writeFieldEnd();
      }
      if (struct.prefix != null) {
        oprot.writeFieldBegin(PREFIX_FIELD_DESC);
        oprot.writeString(struct.prefix);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SUPPORT_DOWNGRADE_FIELD_DESC);
      oprot.writeBool(struct.support_downgrade);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ICON_TYPE_FIELD_DESC);
      oprot.writeI32(struct.icon_type);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACTIVITY_ID_FIELD_DESC);
      oprot.writeI32(struct.activity_id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SMOKE_START_TIME_FIELD_DESC);
      oprot.writeI64(struct.smoke_start_time);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SMOKE_END_TIME_FIELD_DESC);
      oprot.writeI64(struct.smoke_end_time);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class IconConfigTupleSchemeFactory implements SchemeFactory {
    public IconConfigTupleScheme getScheme() {
      return new IconConfigTupleScheme();
    }
  }

  private static class IconConfigTupleScheme extends TupleScheme<IconConfig> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, IconConfig struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetIcon_id()) {
        optionals.set(0);
      }
      if (struct.isSetStart_time()) {
        optionals.set(1);
      }
      if (struct.isSetEnd_time()) {
        optionals.set(2);
      }
      if (struct.isSetService_name()) {
        optionals.set(3);
      }
      if (struct.isSetPrefix()) {
        optionals.set(4);
      }
      if (struct.isSetSupport_downgrade()) {
        optionals.set(5);
      }
      if (struct.isSetIcon_type()) {
        optionals.set(6);
      }
      if (struct.isSetActivity_id()) {
        optionals.set(7);
      }
      if (struct.isSetSmoke_start_time()) {
        optionals.set(8);
      }
      if (struct.isSetSmoke_end_time()) {
        optionals.set(9);
      }
      oprot.writeBitSet(optionals, 10);
      if (struct.isSetIcon_id()) {
        oprot.writeI32(struct.icon_id);
      }
      if (struct.isSetStart_time()) {
        oprot.writeI64(struct.start_time);
      }
      if (struct.isSetEnd_time()) {
        oprot.writeI64(struct.end_time);
      }
      if (struct.isSetService_name()) {
        oprot.writeString(struct.service_name);
      }
      if (struct.isSetPrefix()) {
        oprot.writeString(struct.prefix);
      }
      if (struct.isSetSupport_downgrade()) {
        oprot.writeBool(struct.support_downgrade);
      }
      if (struct.isSetIcon_type()) {
        oprot.writeI32(struct.icon_type);
      }
      if (struct.isSetActivity_id()) {
        oprot.writeI32(struct.activity_id);
      }
      if (struct.isSetSmoke_start_time()) {
        oprot.writeI64(struct.smoke_start_time);
      }
      if (struct.isSetSmoke_end_time()) {
        oprot.writeI64(struct.smoke_end_time);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, IconConfig struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(10);
      if (incoming.get(0)) {
        struct.icon_id = iprot.readI32();
        struct.setIcon_idIsSet(true);
      }
      if (incoming.get(1)) {
        struct.start_time = iprot.readI64();
        struct.setStart_timeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.end_time = iprot.readI64();
        struct.setEnd_timeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.service_name = iprot.readString();
        struct.setService_nameIsSet(true);
      }
      if (incoming.get(4)) {
        struct.prefix = iprot.readString();
        struct.setPrefixIsSet(true);
      }
      if (incoming.get(5)) {
        struct.support_downgrade = iprot.readBool();
        struct.setSupport_downgradeIsSet(true);
      }
      if (incoming.get(6)) {
        struct.icon_type = iprot.readI32();
        struct.setIcon_typeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.activity_id = iprot.readI32();
        struct.setActivity_idIsSet(true);
      }
      if (incoming.get(8)) {
        struct.smoke_start_time = iprot.readI64();
        struct.setSmoke_start_timeIsSet(true);
      }
      if (incoming.get(9)) {
        struct.smoke_end_time = iprot.readI64();
        struct.setSmoke_end_timeIsSet(true);
      }
    }
  }

}

