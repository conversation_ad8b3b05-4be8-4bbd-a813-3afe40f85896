/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.bos_sentry_gate;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 添加 bucket
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-07-30")
public class AddBucketReq implements org.apache.thrift.TBase<AddBucketReq, AddBucketReq._Fields>, java.io.Serializable, Cloneable, Comparable<AddBucketReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("AddBucketReq");

  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SOURCE_FIELD_DESC = new org.apache.thrift.protocol.TField("source", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField ACCESS_KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("accessKey", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField SECRET_KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("secretKey", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField BUCKET_FIELD_DESC = new org.apache.thrift.protocol.TField("bucket", org.apache.thrift.protocol.TType.STRUCT, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new AddBucketReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new AddBucketReqTupleSchemeFactory());
  }

  public long uid; // required
  public String source; // required
  public String accessKey; // required
  public String secretKey; // required
  public Bucket bucket; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UID((short)1, "uid"),
    SOURCE((short)2, "source"),
    ACCESS_KEY((short)3, "accessKey"),
    SECRET_KEY((short)4, "secretKey"),
    BUCKET((short)5, "bucket");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UID
          return UID;
        case 2: // SOURCE
          return SOURCE;
        case 3: // ACCESS_KEY
          return ACCESS_KEY;
        case 4: // SECRET_KEY
          return SECRET_KEY;
        case 5: // BUCKET
          return BUCKET;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __UID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SOURCE, new org.apache.thrift.meta_data.FieldMetaData("source", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ACCESS_KEY, new org.apache.thrift.meta_data.FieldMetaData("accessKey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SECRET_KEY, new org.apache.thrift.meta_data.FieldMetaData("secretKey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUCKET, new org.apache.thrift.meta_data.FieldMetaData("bucket", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, Bucket.class)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(AddBucketReq.class, metaDataMap);
  }

  public AddBucketReq() {
  }

  public AddBucketReq(
    long uid,
    String source,
    String accessKey,
    String secretKey,
    Bucket bucket)
  {
    this();
    this.uid = uid;
    setUidIsSet(true);
    this.source = source;
    this.accessKey = accessKey;
    this.secretKey = secretKey;
    this.bucket = bucket;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public AddBucketReq(AddBucketReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.uid = other.uid;
    if (other.isSetSource()) {
      this.source = other.source;
    }
    if (other.isSetAccessKey()) {
      this.accessKey = other.accessKey;
    }
    if (other.isSetSecretKey()) {
      this.secretKey = other.secretKey;
    }
    if (other.isSetBucket()) {
      this.bucket = new Bucket(other.bucket);
    }
  }

  public AddBucketReq deepCopy() {
    return new AddBucketReq(this);
  }

  @Override
  public void clear() {
    setUidIsSet(false);
    this.uid = 0;
    this.source = null;
    this.accessKey = null;
    this.secretKey = null;
    this.bucket = null;
  }

  public long getUid() {
    return this.uid;
  }

  public AddBucketReq setUid(long uid) {
    this.uid = uid;
    setUidIsSet(true);
    return this;
  }

  public void unsetUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UID_ISSET_ID);
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return EncodingUtils.testBit(__isset_bitfield, __UID_ISSET_ID);
  }

  public void setUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UID_ISSET_ID, value);
  }

  public String getSource() {
    return this.source;
  }

  public AddBucketReq setSource(String source) {
    this.source = source;
    return this;
  }

  public void unsetSource() {
    this.source = null;
  }

  /** Returns true if field source is set (has been assigned a value) and false otherwise */
  public boolean isSetSource() {
    return this.source != null;
  }

  public void setSourceIsSet(boolean value) {
    if (!value) {
      this.source = null;
    }
  }

  public String getAccessKey() {
    return this.accessKey;
  }

  public AddBucketReq setAccessKey(String accessKey) {
    this.accessKey = accessKey;
    return this;
  }

  public void unsetAccessKey() {
    this.accessKey = null;
  }

  /** Returns true if field accessKey is set (has been assigned a value) and false otherwise */
  public boolean isSetAccessKey() {
    return this.accessKey != null;
  }

  public void setAccessKeyIsSet(boolean value) {
    if (!value) {
      this.accessKey = null;
    }
  }

  public String getSecretKey() {
    return this.secretKey;
  }

  public AddBucketReq setSecretKey(String secretKey) {
    this.secretKey = secretKey;
    return this;
  }

  public void unsetSecretKey() {
    this.secretKey = null;
  }

  /** Returns true if field secretKey is set (has been assigned a value) and false otherwise */
  public boolean isSetSecretKey() {
    return this.secretKey != null;
  }

  public void setSecretKeyIsSet(boolean value) {
    if (!value) {
      this.secretKey = null;
    }
  }

  public Bucket getBucket() {
    return this.bucket;
  }

  public AddBucketReq setBucket(Bucket bucket) {
    this.bucket = bucket;
    return this;
  }

  public void unsetBucket() {
    this.bucket = null;
  }

  /** Returns true if field bucket is set (has been assigned a value) and false otherwise */
  public boolean isSetBucket() {
    return this.bucket != null;
  }

  public void setBucketIsSet(boolean value) {
    if (!value) {
      this.bucket = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((Long)value);
      }
      break;

    case SOURCE:
      if (value == null) {
        unsetSource();
      } else {
        setSource((String)value);
      }
      break;

    case ACCESS_KEY:
      if (value == null) {
        unsetAccessKey();
      } else {
        setAccessKey((String)value);
      }
      break;

    case SECRET_KEY:
      if (value == null) {
        unsetSecretKey();
      } else {
        setSecretKey((String)value);
      }
      break;

    case BUCKET:
      if (value == null) {
        unsetBucket();
      } else {
        setBucket((Bucket)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case UID:
      return getUid();

    case SOURCE:
      return getSource();

    case ACCESS_KEY:
      return getAccessKey();

    case SECRET_KEY:
      return getSecretKey();

    case BUCKET:
      return getBucket();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case UID:
      return isSetUid();
    case SOURCE:
      return isSetSource();
    case ACCESS_KEY:
      return isSetAccessKey();
    case SECRET_KEY:
      return isSetSecretKey();
    case BUCKET:
      return isSetBucket();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof AddBucketReq)
      return this.equals((AddBucketReq)that);
    return false;
  }

  public boolean equals(AddBucketReq that) {
    if (that == null)
      return false;

    boolean this_present_uid = true;
    boolean that_present_uid = true;
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (this.uid != that.uid)
        return false;
    }

    boolean this_present_source = true && this.isSetSource();
    boolean that_present_source = true && that.isSetSource();
    if (this_present_source || that_present_source) {
      if (!(this_present_source && that_present_source))
        return false;
      if (!this.source.equals(that.source))
        return false;
    }

    boolean this_present_accessKey = true && this.isSetAccessKey();
    boolean that_present_accessKey = true && that.isSetAccessKey();
    if (this_present_accessKey || that_present_accessKey) {
      if (!(this_present_accessKey && that_present_accessKey))
        return false;
      if (!this.accessKey.equals(that.accessKey))
        return false;
    }

    boolean this_present_secretKey = true && this.isSetSecretKey();
    boolean that_present_secretKey = true && that.isSetSecretKey();
    if (this_present_secretKey || that_present_secretKey) {
      if (!(this_present_secretKey && that_present_secretKey))
        return false;
      if (!this.secretKey.equals(that.secretKey))
        return false;
    }

    boolean this_present_bucket = true && this.isSetBucket();
    boolean that_present_bucket = true && that.isSetBucket();
    if (this_present_bucket || that_present_bucket) {
      if (!(this_present_bucket && that_present_bucket))
        return false;
      if (!this.bucket.equals(that.bucket))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_uid = true;
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    boolean present_source = true && (isSetSource());
    list.add(present_source);
    if (present_source)
      list.add(source);

    boolean present_accessKey = true && (isSetAccessKey());
    list.add(present_accessKey);
    if (present_accessKey)
      list.add(accessKey);

    boolean present_secretKey = true && (isSetSecretKey());
    list.add(present_secretKey);
    if (present_secretKey)
      list.add(secretKey);

    boolean present_bucket = true && (isSetBucket());
    list.add(present_bucket);
    if (present_bucket)
      list.add(bucket);

    return list.hashCode();
  }

  @Override
  public int compareTo(AddBucketReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSource()).compareTo(other.isSetSource());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSource()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.source, other.source);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAccessKey()).compareTo(other.isSetAccessKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAccessKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.accessKey, other.accessKey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSecretKey()).compareTo(other.isSetSecretKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSecretKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.secretKey, other.secretKey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBucket()).compareTo(other.isSetBucket());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBucket()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bucket, other.bucket);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("AddBucketReq(");
    boolean first = true;

    sb.append("uid:");
    sb.append(this.uid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("source:");
    if (this.source == null) {
      sb.append("null");
    } else {
      sb.append(this.source);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("accessKey:");
    if (this.accessKey == null) {
      sb.append("null");
    } else {
      sb.append(this.accessKey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("secretKey:");
    if (this.secretKey == null) {
      sb.append("null");
    } else {
      sb.append(this.secretKey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("bucket:");
    if (this.bucket == null) {
      sb.append("null");
    } else {
      sb.append(this.bucket);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
    if (bucket != null) {
      bucket.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class AddBucketReqStandardSchemeFactory implements SchemeFactory {
    public AddBucketReqStandardScheme getScheme() {
      return new AddBucketReqStandardScheme();
    }
  }

  private static class AddBucketReqStandardScheme extends StandardScheme<AddBucketReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, AddBucketReq struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.uid = iprot.readI64();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SOURCE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.source = iprot.readString();
              struct.setSourceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ACCESS_KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.accessKey = iprot.readString();
              struct.setAccessKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SECRET_KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.secretKey = iprot.readString();
              struct.setSecretKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // BUCKET
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.bucket = new Bucket();
              struct.bucket.read(iprot);
              struct.setBucketIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, AddBucketReq struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(UID_FIELD_DESC);
      oprot.writeI64(struct.uid);
      oprot.writeFieldEnd();
      if (struct.source != null) {
        oprot.writeFieldBegin(SOURCE_FIELD_DESC);
        oprot.writeString(struct.source);
        oprot.writeFieldEnd();
      }
      if (struct.accessKey != null) {
        oprot.writeFieldBegin(ACCESS_KEY_FIELD_DESC);
        oprot.writeString(struct.accessKey);
        oprot.writeFieldEnd();
      }
      if (struct.secretKey != null) {
        oprot.writeFieldBegin(SECRET_KEY_FIELD_DESC);
        oprot.writeString(struct.secretKey);
        oprot.writeFieldEnd();
      }
      if (struct.bucket != null) {
        oprot.writeFieldBegin(BUCKET_FIELD_DESC);
        struct.bucket.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class AddBucketReqTupleSchemeFactory implements SchemeFactory {
    public AddBucketReqTupleScheme getScheme() {
      return new AddBucketReqTupleScheme();
    }
  }

  private static class AddBucketReqTupleScheme extends TupleScheme<AddBucketReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, AddBucketReq struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetUid()) {
        optionals.set(0);
      }
      if (struct.isSetSource()) {
        optionals.set(1);
      }
      if (struct.isSetAccessKey()) {
        optionals.set(2);
      }
      if (struct.isSetSecretKey()) {
        optionals.set(3);
      }
      if (struct.isSetBucket()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetUid()) {
        oprot.writeI64(struct.uid);
      }
      if (struct.isSetSource()) {
        oprot.writeString(struct.source);
      }
      if (struct.isSetAccessKey()) {
        oprot.writeString(struct.accessKey);
      }
      if (struct.isSetSecretKey()) {
        oprot.writeString(struct.secretKey);
      }
      if (struct.isSetBucket()) {
        struct.bucket.write(oprot);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, AddBucketReq struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.uid = iprot.readI64();
        struct.setUidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.source = iprot.readString();
        struct.setSourceIsSet(true);
      }
      if (incoming.get(2)) {
        struct.accessKey = iprot.readString();
        struct.setAccessKeyIsSet(true);
      }
      if (incoming.get(3)) {
        struct.secretKey = iprot.readString();
        struct.setSecretKeyIsSet(true);
      }
      if (incoming.get(4)) {
        struct.bucket = new Bucket();
        struct.bucket.read(iprot);
        struct.setBucketIsSet(true);
      }
    }
  }

}

