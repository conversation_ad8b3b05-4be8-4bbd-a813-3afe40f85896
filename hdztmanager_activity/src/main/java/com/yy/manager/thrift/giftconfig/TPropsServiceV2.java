package com.yy.manager.thrift.giftconfig; /**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class TPropsServiceV2 {

  public interface Iface {

    /**
     * 查看礼物配置信息
     * @param ids 礼物ID
     * @return PropsConfig
     * @throws ServiceException
     *
     * @param ids
     */
    public Map<Integer,TPropsConfig> getPropsConfig(List<Integer> ids) throws TServiceException, TException;

  }

  public interface AsyncIface {

    public void getPropsConfig(List<Integer> ids, AsyncMethodCallback resultHandler) throws TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public Map<Integer,TPropsConfig> getPropsConfig(List<Integer> ids) throws TServiceException, TException
    {
      send_getPropsConfig(ids);
      return recv_getPropsConfig();
    }

    public void send_getPropsConfig(List<Integer> ids) throws TException
    {
      getPropsConfig_args args = new getPropsConfig_args();
      args.setIds(ids);
      sendBase("getPropsConfig", args);
    }

    public Map<Integer,TPropsConfig> recv_getPropsConfig() throws TServiceException, TException
    {
      getPropsConfig_result result = new getPropsConfig_result();
      receiveBase(result, "getPropsConfig");
      if (result.isSetSuccess()) {
        return result.success;
      }
      if (result.ex1 != null) {
        throw result.ex1;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getPropsConfig failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void getPropsConfig(List<Integer> ids, AsyncMethodCallback resultHandler) throws TException {
      checkReady();
      getPropsConfig_call method_call = new getPropsConfig_call(ids, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getPropsConfig_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Integer> ids;
      public getPropsConfig_call(List<Integer> ids, AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.ids = ids;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getPropsConfig", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getPropsConfig_args args = new getPropsConfig_args();
        args.setIds(ids);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public Map<Integer,TPropsConfig> getResult() throws TServiceException, TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getPropsConfig();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("getPropsConfig", new getPropsConfig());
      return processMap;
    }

    public static class getPropsConfig<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getPropsConfig_args> {
      public getPropsConfig() {
        super("getPropsConfig");
      }

      public getPropsConfig_args getEmptyArgsInstance() {
        return new getPropsConfig_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getPropsConfig_result getResult(I iface, getPropsConfig_args args) throws TException {
        getPropsConfig_result result = new getPropsConfig_result();
        try {
          result.success = iface.getPropsConfig(args.ids);
        } catch (TServiceException ex1) {
          result.ex1 = ex1;
        }
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("getPropsConfig", new getPropsConfig());
      return processMap;
    }

    public static class getPropsConfig<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getPropsConfig_args, Map<Integer,TPropsConfig>> {
      public getPropsConfig() {
        super("getPropsConfig");
      }

      public getPropsConfig_args getEmptyArgsInstance() {
        return new getPropsConfig_args();
      }

      public AsyncMethodCallback<Map<Integer,TPropsConfig>> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Map<Integer,TPropsConfig>>() {
          public void onComplete(Map<Integer,TPropsConfig> o) {
            getPropsConfig_result result = new getPropsConfig_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getPropsConfig_result result = new getPropsConfig_result();
            if (e instanceof TServiceException) {
                        result.ex1 = (TServiceException) e;
                        result.setEx1IsSet(true);
                        msg = result;
            }
             else
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getPropsConfig_args args, AsyncMethodCallback<Map<Integer,TPropsConfig>> resultHandler) throws TException {
        iface.getPropsConfig(args.ids,resultHandler);
      }
    }

  }

  public static class getPropsConfig_args implements org.apache.thrift.TBase<getPropsConfig_args, getPropsConfig_args._Fields>, java.io.Serializable, Cloneable, Comparable<getPropsConfig_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPropsConfig_args");

    private static final org.apache.thrift.protocol.TField IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("ids", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPropsConfig_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPropsConfig_argsTupleSchemeFactory());
    }

    public List<Integer> ids; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      IDS((short)1, "ids");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // IDS
            return IDS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.IDS, new org.apache.thrift.meta_data.FieldMetaData("ids", org.apache.thrift.TFieldRequirementType.DEFAULT,
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPropsConfig_args.class, metaDataMap);
    }

    public getPropsConfig_args() {
    }

    public getPropsConfig_args(
      List<Integer> ids)
    {
      this();
      this.ids = ids;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPropsConfig_args(getPropsConfig_args other) {
      if (other.isSetIds()) {
        List<Integer> __this__ids = new ArrayList<Integer>(other.ids);
        this.ids = __this__ids;
      }
    }

    public getPropsConfig_args deepCopy() {
      return new getPropsConfig_args(this);
    }

    @Override
    public void clear() {
      this.ids = null;
    }

    public int getIdsSize() {
      return (this.ids == null) ? 0 : this.ids.size();
    }

    public java.util.Iterator<Integer> getIdsIterator() {
      return (this.ids == null) ? null : this.ids.iterator();
    }

    public void addToIds(int elem) {
      if (this.ids == null) {
        this.ids = new ArrayList<Integer>();
      }
      this.ids.add(elem);
    }

    public List<Integer> getIds() {
      return this.ids;
    }

    public getPropsConfig_args setIds(List<Integer> ids) {
      this.ids = ids;
      return this;
    }

    public void unsetIds() {
      this.ids = null;
    }

    /** Returns true if field ids is set (has been assigned a value) and false otherwise */
    public boolean isSetIds() {
      return this.ids != null;
    }

    public void setIdsIsSet(boolean value) {
      if (!value) {
        this.ids = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case IDS:
        if (value == null) {
          unsetIds();
        } else {
          setIds((List<Integer>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case IDS:
        return getIds();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case IDS:
        return isSetIds();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPropsConfig_args)
        return this.equals((getPropsConfig_args)that);
      return false;
    }

    public boolean equals(getPropsConfig_args that) {
      if (that == null)
        return false;

      boolean this_present_ids = true && this.isSetIds();
      boolean that_present_ids = true && that.isSetIds();
      if (this_present_ids || that_present_ids) {
        if (!(this_present_ids && that_present_ids))
          return false;
        if (!this.ids.equals(that.ids))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_ids = true && (isSetIds());
      list.add(present_ids);
      if (present_ids)
        list.add(ids);

      return list.hashCode();
    }

    @Override
    public int compareTo(getPropsConfig_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetIds()).compareTo(other.isSetIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ids, other.ids);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPropsConfig_args(");
      boolean first = true;

      sb.append("ids:");
      if (this.ids == null) {
        sb.append("null");
      } else {
        sb.append(this.ids);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPropsConfig_argsStandardSchemeFactory implements SchemeFactory {
      public getPropsConfig_argsStandardScheme getScheme() {
        return new getPropsConfig_argsStandardScheme();
      }
    }

    private static class getPropsConfig_argsStandardScheme extends StandardScheme<getPropsConfig_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPropsConfig_args struct) throws TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
            break;
          }
          switch (schemeField.id) {
            case 1: // IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                  struct.ids = new ArrayList<Integer>(_list0.size);
                  int _elem1;
                  for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                  {
                    _elem1 = iprot.readI32();
                    struct.ids.add(_elem1);
                  }
                  iprot.readListEnd();
                }
                struct.setIdsIsSet(true);
              } else {
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPropsConfig_args struct) throws TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.ids != null) {
          oprot.writeFieldBegin(IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.ids.size()));
            for (int _iter3 : struct.ids)
            {
              oprot.writeI32(_iter3);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPropsConfig_argsTupleSchemeFactory implements SchemeFactory {
      public getPropsConfig_argsTupleScheme getScheme() {
        return new getPropsConfig_argsTupleScheme();
      }
    }

    private static class getPropsConfig_argsTupleScheme extends TupleScheme<getPropsConfig_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPropsConfig_args struct) throws TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetIds()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetIds()) {
          {
            oprot.writeI32(struct.ids.size());
            for (int _iter4 : struct.ids)
            {
              oprot.writeI32(_iter4);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPropsConfig_args struct) throws TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, iprot.readI32());
            struct.ids = new ArrayList<Integer>(_list5.size);
            int _elem6;
            for (int _i7 = 0; _i7 < _list5.size; ++_i7)
            {
              _elem6 = iprot.readI32();
              struct.ids.add(_elem6);
            }
          }
          struct.setIdsIsSet(true);
        }
      }
    }

  }

  public static class getPropsConfig_result implements org.apache.thrift.TBase<getPropsConfig_result, getPropsConfig_result._Fields>, java.io.Serializable, Cloneable, Comparable<getPropsConfig_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPropsConfig_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.MAP, (short)0);
    private static final org.apache.thrift.protocol.TField EX1_FIELD_DESC = new org.apache.thrift.protocol.TField("ex1", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPropsConfig_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPropsConfig_resultTupleSchemeFactory());
    }

    public Map<Integer,TPropsConfig> success; // required
    public TServiceException ex1; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success"),
      EX1((short)1, "ex1");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          case 1: // EX1
            return EX1;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT,
          new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32),
              new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, TPropsConfig.class))));
      tmpMap.put(_Fields.EX1, new org.apache.thrift.meta_data.FieldMetaData("ex1", org.apache.thrift.TFieldRequirementType.DEFAULT,
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPropsConfig_result.class, metaDataMap);
    }

    public getPropsConfig_result() {
    }

    public getPropsConfig_result(
      Map<Integer,TPropsConfig> success,
      TServiceException ex1)
    {
      this();
      this.success = success;
      this.ex1 = ex1;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPropsConfig_result(getPropsConfig_result other) {
      if (other.isSetSuccess()) {
        Map<Integer,TPropsConfig> __this__success = new HashMap<Integer,TPropsConfig>(other.success.size());
        for (Map.Entry<Integer, TPropsConfig> other_element : other.success.entrySet()) {

          Integer other_element_key = other_element.getKey();
          TPropsConfig other_element_value = other_element.getValue();

          Integer __this__success_copy_key = other_element_key;

          TPropsConfig __this__success_copy_value = new TPropsConfig(other_element_value);

          __this__success.put(__this__success_copy_key, __this__success_copy_value);
        }
        this.success = __this__success;
      }
      if (other.isSetEx1()) {
        this.ex1 = new TServiceException(other.ex1);
      }
    }

    public getPropsConfig_result deepCopy() {
      return new getPropsConfig_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
      this.ex1 = null;
    }

    public int getSuccessSize() {
      return (this.success == null) ? 0 : this.success.size();
    }

    public void putToSuccess(int key, TPropsConfig val) {
      if (this.success == null) {
        this.success = new HashMap<Integer,TPropsConfig>();
      }
      this.success.put(key, val);
    }

    public Map<Integer,TPropsConfig> getSuccess() {
      return this.success;
    }

    public getPropsConfig_result setSuccess(Map<Integer,TPropsConfig> success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public TServiceException getEx1() {
      return this.ex1;
    }

    public getPropsConfig_result setEx1(TServiceException ex1) {
      this.ex1 = ex1;
      return this;
    }

    public void unsetEx1() {
      this.ex1 = null;
    }

    /** Returns true if field ex1 is set (has been assigned a value) and false otherwise */
    public boolean isSetEx1() {
      return this.ex1 != null;
    }

    public void setEx1IsSet(boolean value) {
      if (!value) {
        this.ex1 = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Map<Integer,TPropsConfig>)value);
        }
        break;

      case EX1:
        if (value == null) {
          unsetEx1();
        } else {
          setEx1((TServiceException)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      case EX1:
        return getEx1();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      case EX1:
        return isSetEx1();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPropsConfig_result)
        return this.equals((getPropsConfig_result)that);
      return false;
    }

    public boolean equals(getPropsConfig_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      boolean this_present_ex1 = true && this.isSetEx1();
      boolean that_present_ex1 = true && that.isSetEx1();
      if (this_present_ex1 || that_present_ex1) {
        if (!(this_present_ex1 && that_present_ex1))
          return false;
        if (!this.ex1.equals(that.ex1))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      boolean present_ex1 = true && (isSetEx1());
      list.add(present_ex1);
      if (present_ex1)
        list.add(ex1);

      return list.hashCode();
    }

    @Override
    public int compareTo(getPropsConfig_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetEx1()).compareTo(other.isSetEx1());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetEx1()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ex1, other.ex1);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPropsConfig_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("ex1:");
      if (this.ex1 == null) {
        sb.append("null");
      } else {
        sb.append(this.ex1);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPropsConfig_resultStandardSchemeFactory implements SchemeFactory {
      public getPropsConfig_resultStandardScheme getScheme() {
        return new getPropsConfig_resultStandardScheme();
      }
    }

    private static class getPropsConfig_resultStandardScheme extends StandardScheme<getPropsConfig_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPropsConfig_result struct) throws TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
                {
                  org.apache.thrift.protocol.TMap _map8 = iprot.readMapBegin();
                  struct.success = new HashMap<Integer,TPropsConfig>(2*_map8.size);
                  int _key9;
                  TPropsConfig _val10;
                  for (int _i11 = 0; _i11 < _map8.size; ++_i11)
                  {
                    _key9 = iprot.readI32();
                    _val10 = new TPropsConfig();
                    _val10.read(iprot);
                    struct.success.put(_key9, _val10);
                  }
                  iprot.readMapEnd();
                }
                struct.setSuccessIsSet(true);
              } else {
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 1: // EX1
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ex1 = new TServiceException();
                struct.ex1.read(iprot);
                struct.setEx1IsSet(true);
              } else {
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPropsConfig_result struct) throws TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.STRUCT, struct.success.size()));
            for (Map.Entry<Integer, TPropsConfig> _iter12 : struct.success.entrySet())
            {
              oprot.writeI32(_iter12.getKey());
              _iter12.getValue().write(oprot);
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
        if (struct.ex1 != null) {
          oprot.writeFieldBegin(EX1_FIELD_DESC);
          struct.ex1.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPropsConfig_resultTupleSchemeFactory implements SchemeFactory {
      public getPropsConfig_resultTupleScheme getScheme() {
        return new getPropsConfig_resultTupleScheme();
      }
    }

    private static class getPropsConfig_resultTupleScheme extends TupleScheme<getPropsConfig_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPropsConfig_result struct) throws TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        if (struct.isSetEx1()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetSuccess()) {
          {
            oprot.writeI32(struct.success.size());
            for (Map.Entry<Integer, TPropsConfig> _iter13 : struct.success.entrySet())
            {
              oprot.writeI32(_iter13.getKey());
              _iter13.getValue().write(oprot);
            }
          }
        }
        if (struct.isSetEx1()) {
          struct.ex1.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPropsConfig_result struct) throws TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TMap _map14 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
            struct.success = new HashMap<Integer,TPropsConfig>(2*_map14.size);
            int _key15;
            TPropsConfig _val16;
            for (int _i17 = 0; _i17 < _map14.size; ++_i17)
            {
              _key15 = iprot.readI32();
              _val16 = new TPropsConfig();
              _val16.read(iprot);
              struct.success.put(_key15, _val16);
            }
          }
          struct.setSuccessIsSet(true);
        }
        if (incoming.get(1)) {
          struct.ex1 = new TServiceException();
          struct.ex1.read(iprot);
          struct.setEx1IsSet(true);
        }
      }
    }

  }

}
