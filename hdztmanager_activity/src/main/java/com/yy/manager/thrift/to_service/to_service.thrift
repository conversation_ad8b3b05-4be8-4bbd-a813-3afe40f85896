namespace java com.yy.manager.thrift.to_service


struct TSysParam {
  1: string mcode;
  2: string scode;
  3: string value;
  4: i32 status;
  5: string extend;
}

service TPingService {
  i64 ping(1: i64 seq);
  void ping2();
}

service TCommonService extends TPingService {
    /**
    * 查询营收任意配置
    * mcode = "ACTIVITY_BROADCAST_PROPS_CONFIG"
    * scode: 2交友 36宝贝
    * status: 1有效 0无效
    * value: json字符串
    * [{"actId":2023085001,"showActIdLevel":3,"startTime":"2023-08-14 17:00:00","endTime":"2023-08-23 00:00:00","propIds":[20527,20528]}]
    **/
    TSysParam getSingleSysParam(1: string mcode, 2: string scode);
}