package com.yy.manager.thrift.client;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.manager.exception.SuperException;
import com.yy.manager.thrift.support.WebdbSaHelper;
import com.yy.manager.thrift.webdbservice.*;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 频道类信息服务接口
 *
 * <AUTHOR>
 * @date 2021年06月01日 上午10:33:27
 **/
@Component
@Slf4j
public class WebdbSinfoClient {

    @Reference(protocol = "nythrift", owner = "__", url = "${thrift.service-agent.url}")
    private webdb_sinfo_service.Iface proxy = null;

    public webdb_sinfo_service.Iface getProxy() {
        return proxy;
    }

    /* webdb 子频道信息列*/
    private final static List<String> SUB_CHANNEL_INFO_COLUMNS = Lists.newArrayList(
            /* 顶级频道id*/
            "tid",
            //频道id
            "sid",
            //父频道id
            "pid",
            //频道名字
            "name",
            //子频道人数限制
            "maxman",
            //频道密码
            "passwd",
            //频道创建时间
            "create_time",
            //麦序模式
            "style",
            //麦序时间
            "microtime",
            //是否限制文字聊天速度
            "is_limit_txt",
            //限制文字聊天速度每句间隔秒数
            "txt_limittime",
            //排序序号
            "sort",
            //收费频道类型
            "charge",
            //频道模板
            "template_id",
            //子频道是否有设置密码
            "is_passwd_set",
            //子频道是否有限制游客进入
            "is_guest_access_limit");

    /* webdb 顶级频道信息列*/
    private final static List<String> CHANNEL_INFO_COLUMNS = Lists.newArrayList("sid",
            "asid", "name",
            "ownerid",
            "type",
            "typestr",
            "logo_index",
            "logo_url",
            "credit",
            "template_id",
            "jiedai_sid"
    );

    /**
     * <p>批量获取子频道信息</p>
     *
     * @param sidSsids 是顶级频道_子频道
     * @return 参数传入 顶级频道默认子频道的时候， 顶级频道_顶级频道 , 查询不到数据返回空
     */
    //@Cached(timeToLiveMillis = CacheTimeout.yy_channel_info)
    public Map<String, Map<String, String>> batchGetSubChannelInfo(List<String> sidSsids) {
        long startTime = System.currentTimeMillis();

        SaRequestSubsess req = new SaRequestSubsess();
        req.setAuthMsg(WebdbSaHelper.getAuthorizeMsg());
        req.setAppkey(Const.APP_ID + "");
        req.setColumns(SUB_CHANNEL_INFO_COLUMNS);

        Map<String, Map<String, String>> subChannelMap = Maps.newHashMap();
        try {
            List<StringList> tidSids = sidSsids.stream()
                    .filter(sidSsid -> sidSsid.contains("_"))
                    .map(sidSsid -> Lists.newArrayList(sidSsid.split("_")[0], sidSsid.split("_")[1]))
                    .map(StringList::new)
                    .collect(Collectors.toList());

            req.setTidSids(tidSids);
            SaResponseSet result = proxy.sa_get_subsess_info(req);
            int resCode0 = 0;
            int resCode3 = 3;
            int rescode = result.getRescode();
            if (rescode == resCode0) {
                List<Map<String, String>> saResponseMaps = getSaResponseMaps(result);
                subChannelMap = saResponseMaps.stream().collect(Collectors.toMap(data -> data.get("tid") + "_" + data.get("sid"), Function.identity()));
            } else if (rescode == resCode3) {
                log.warn("batchGetSubChannelInfo empty data,size:{},code:{}", sidSsids.size(), rescode);
            } else {
                log.error("batchGetSubChannelInfo error,sid size:{},error:{}", sidSsids.size(), rescode);
            }

        } catch (Exception e) {
            log.error("batchGetSubChannelInfo exception@sidSsids:{} {}", sidSsids, System.currentTimeMillis() - startTime, e);
        }

        return subChannelMap;
    }

    public Map<Long, Map<String, String>> batchGetTopChannelInfo(List<Long> sids) {
        return batchGetTopChannelInfo(sids, 0);
    }

    public Map<String, String> getChannelInfoByAsId(Long asId) {
        Map<Long, Map<String, String>> channelInfos = batchGetTopChannelInfo(Collections.singletonList(asId), 1);
        return channelInfos.getOrDefault(asId, Maps.newHashMap());
    }

    /**
     * <p>批量获取顶级信息</p>
     */
    //@Cached(timeToLiveMillis = CacheTimeout.yy_top_channel_info)
    public Map<Long, Map<String, String>> batchGetTopChannelInfo(List<Long> sids, int type) {
        if (org.springframework.util.CollectionUtils.isEmpty(sids)) {
            return Maps.newHashMap();
        }

        // 接口要求：顶级频道id列表(列表最大长度为500)
        int maxSize = 500;
        if (sids.size() > maxSize) {
            throw new SuperException("sid列表最大长度为500", SuperException.E_PARAM_ILLEGAL);
        }

        List<String> strSids = sids.stream().map(i -> String.valueOf(i)).collect(Collectors.toList());
        long startTime = System.currentTimeMillis();
        SaRequestSession req = new SaRequestSession();
        req.setAuthMsg(WebdbSaHelper.getAuthorizeMsg());
        req.setAppkey(Const.APP_ID + "");
        req.setSids(strSids);
        req.setType(type);
        req.setColumns(CHANNEL_INFO_COLUMNS);

        Map<Long, Map<String, String>> topChannelMap = Maps.newHashMap();
        try {
            SaResponseSet result = proxy.sa_get_session_info(req);
            int rescode = result.getRescode();
            int resCode0 = 0;
            int resCode3 = 3;
            if (rescode == resCode0) {
                List<Map<String, String>> saResponseMaps = getSaResponseMaps(result);
                if (type == NumberUtils.INTEGER_ZERO) {
                    topChannelMap = saResponseMaps.stream().collect(Collectors.toMap(data -> Convert.toLong(data.get("sid")), Function.identity()));
                } else if (type == NumberUtils.INTEGER_ONE) {
                    topChannelMap = saResponseMaps.stream().collect(Collectors.toMap(data -> Convert.toLong(data.get("asid")), Function.identity()));
                }
            } else if (rescode == resCode3) {
                log.warn("batchGetTopChannelInfo empty data,size:{}, rescode:{}", sids.size(), rescode);
            } else {
                log.error("batchGetTopChannelInfo error,sid size:{}, rescode:{}", sids.size(), rescode);
            }
        } catch (Exception e) {
            log.error("batchGetTopChannelInfo exception@sidSsids:{} {}", sids, System.currentTimeMillis() - startTime, e);
        }

        return topChannelMap;
    }

    //@Cached(timeToLiveMillis = CacheTimeout.yy_top_channel_info)
    public Map<String, String> getChannelInfo(long sid) {
        Map<Long, Map<String, String>> map = batchGetTopChannelInfo(Lists.newArrayList(sid));
        return map.containsKey(sid) ? map.get(sid) : Maps.newHashMap();
    }

    /**
     * 获取子频道加载的模板ID
     */
    public String getSessionTemplate(Long sid, Long ssid) {
        try {
            // 相等时认为是查顶级子频道（使用 sa_get_subsess_info 是查不到顶级子频道信息的，这个不合理但没办法！）
            if (sid.equals(ssid)) {
                Map<String, String> map = getChannelInfo(sid);
                return StringUtils.trim(map.get("template_id"));
            } else {
                String sidSSid = sid + "_" + ssid;
                Map<String, Map<String, String>> map = this.batchGetSubChannelInfo(Lists.newArrayList(sidSSid));
                return map.containsKey(sidSSid) ? StringUtils.trim(map.get(sidSSid).get("template_id")) : "";
            }
        } catch (Exception e) {
            log.error("getSessionTemplate error,sid:{},ssid:{},e:{}", sid, ssid, e.getMessage(), e);
            return "";
        }
    }


    //是否是ow
    //@Cached(timeToLiveMillis = 10000)
    public long getOwUid(Long sid) {
        Map<String, String> channelInfo = this.getChannelInfo(sid);
        if (channelInfo == null) {
            return 0L;
        }
        String owUid = channelInfo.get(Const.WEBDB_CHANNEL_COLUMN_NAME.OWUID);
        return Convert.toLong(owUid, 0);
    }

    public boolean isOw(Long sid, Long uid) {
        return uid.equals(getOwUid(sid));
    }

    // 校验子频道合法性
    // @Cached(timeToLiveMillis = 10000)
    public boolean validateSubSession(String topSid, String ssid) {
        try {
            //都是顶级频道
            if (topSid.equals(ssid)) {
                return true;
            }
            //读取父频道

            String subsid = topSid + "_" + ssid;
            Map<String, Map<String, String>> result = batchGetSubChannelInfo(Lists.newArrayList(subsid));

            //数据不存在
            if (org.springframework.util.CollectionUtils.isEmpty(result)) {
                return false;
            }

            //1级子频道
            Map<String, String> map = result.get(subsid);
            String parentSid = StringUtils.trim(map.get("pid"));
            if (topSid.equals(parentSid)) {
                return true;
            }

            //2级子频道 ？？？
            String tid = StringUtils.trim(map.get("tid"));
            if (topSid.equals(tid)) {
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("validateSubSession error,topsid:{},ssid:{},e:{}", topSid, ssid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * SaResponseSet 转成 List<Map>
     */
    private List<Map<String, String>> getSaResponseMaps(SaResponseSet saResponseSet) {
        Map<String, Integer> keyIndex = saResponseSet.getKeyIndex();
        List<StringList> dataSet = saResponseSet.getDataSet();
        if (MapUtils.isEmpty(keyIndex) || CollectionUtils.isEmpty(dataSet)) {
            return Lists.newArrayList();
        }
        List<Map<String, String>> saResponseMaps = Lists.newArrayList();
        for (StringList data : dataSet) {
            Map<String, String> dataMap = Maps.newHashMap();
            List<String> strList = data.getStrList();
            for (Map.Entry<String, Integer> entry : keyIndex.entrySet()) {
                String key = entry.getKey();
                String value = strList.get(entry.getValue());
                if ("logo_url".equals(key) && StringUtils.isBlank(value)) {
                    value = Const.IMAGE.DEFAULT_CHANNEL_LOGO;
                }
                dataMap.put(key, value);
            }
            saResponseMaps.add(dataMap);
        }
        return saResponseMaps;
    }
}
