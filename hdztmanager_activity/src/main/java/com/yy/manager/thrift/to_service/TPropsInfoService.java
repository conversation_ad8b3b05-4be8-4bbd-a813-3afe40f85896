/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.to_service;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-08-16")
public class TPropsInfoService {

  public interface Iface {

    public List<TPropsMeta> getPorpsMetaByApps(TAppId apps) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void getPorpsMetaByApps(TAppId apps, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public List<TPropsMeta> getPorpsMetaByApps(TAppId apps) throws org.apache.thrift.TException
    {
      send_getPorpsMetaByApps(apps);
      return recv_getPorpsMetaByApps();
    }

    public void send_getPorpsMetaByApps(TAppId apps) throws org.apache.thrift.TException
    {
      getPorpsMetaByApps_args args = new getPorpsMetaByApps_args();
      args.setApps(apps);
      sendBase("getPorpsMetaByApps", args);
    }

    public List<TPropsMeta> recv_getPorpsMetaByApps() throws org.apache.thrift.TException
    {
      getPorpsMetaByApps_result result = new getPorpsMetaByApps_result();
      receiveBase(result, "getPorpsMetaByApps");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getPorpsMetaByApps failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void getPorpsMetaByApps(TAppId apps, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getPorpsMetaByApps_call method_call = new getPorpsMetaByApps_call(apps, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getPorpsMetaByApps_call extends org.apache.thrift.async.TAsyncMethodCall {
      private TAppId apps;
      public getPorpsMetaByApps_call(TAppId apps, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.apps = apps;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getPorpsMetaByApps", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getPorpsMetaByApps_args args = new getPorpsMetaByApps_args();
        args.setApps(apps);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public List<TPropsMeta> getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getPorpsMetaByApps();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("getPorpsMetaByApps", new getPorpsMetaByApps());
      return processMap;
    }

    public static class getPorpsMetaByApps<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getPorpsMetaByApps_args> {
      public getPorpsMetaByApps() {
        super("getPorpsMetaByApps");
      }

      public getPorpsMetaByApps_args getEmptyArgsInstance() {
        return new getPorpsMetaByApps_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getPorpsMetaByApps_result getResult(I iface, getPorpsMetaByApps_args args) throws org.apache.thrift.TException {
        getPorpsMetaByApps_result result = new getPorpsMetaByApps_result();
        result.success = iface.getPorpsMetaByApps(args.apps);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("getPorpsMetaByApps", new getPorpsMetaByApps());
      return processMap;
    }

    public static class getPorpsMetaByApps<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getPorpsMetaByApps_args, List<TPropsMeta>> {
      public getPorpsMetaByApps() {
        super("getPorpsMetaByApps");
      }

      public getPorpsMetaByApps_args getEmptyArgsInstance() {
        return new getPorpsMetaByApps_args();
      }

      public AsyncMethodCallback<List<TPropsMeta>> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<List<TPropsMeta>>() { 
          public void onComplete(List<TPropsMeta> o) {
            getPorpsMetaByApps_result result = new getPorpsMetaByApps_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getPorpsMetaByApps_result result = new getPorpsMetaByApps_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getPorpsMetaByApps_args args, org.apache.thrift.async.AsyncMethodCallback<List<TPropsMeta>> resultHandler) throws TException {
        iface.getPorpsMetaByApps(args.apps,resultHandler);
      }
    }

  }

  public static class getPorpsMetaByApps_args implements org.apache.thrift.TBase<getPorpsMetaByApps_args, getPorpsMetaByApps_args._Fields>, java.io.Serializable, Cloneable, Comparable<getPorpsMetaByApps_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPorpsMetaByApps_args");

    private static final org.apache.thrift.protocol.TField APPS_FIELD_DESC = new org.apache.thrift.protocol.TField("apps", org.apache.thrift.protocol.TType.I32, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPorpsMetaByApps_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPorpsMetaByApps_argsTupleSchemeFactory());
    }

    public TAppId apps; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      APPS((short)1, "apps");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // APPS
            return APPS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.APPS, new org.apache.thrift.meta_data.FieldMetaData("apps", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.ENUM          , "TAppId")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPorpsMetaByApps_args.class, metaDataMap);
    }

    public getPorpsMetaByApps_args() {
    }

    public getPorpsMetaByApps_args(
      TAppId apps)
    {
      this();
      this.apps = apps;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPorpsMetaByApps_args(getPorpsMetaByApps_args other) {
      if (other.isSetApps()) {
        this.apps = other.apps;
      }
    }

    public getPorpsMetaByApps_args deepCopy() {
      return new getPorpsMetaByApps_args(this);
    }

    @Override
    public void clear() {
      this.apps = null;
    }

    public TAppId getApps() {
      return this.apps;
    }

    public getPorpsMetaByApps_args setApps(TAppId apps) {
      this.apps = apps;
      return this;
    }

    public void unsetApps() {
      this.apps = null;
    }

    /** Returns true if field apps is set (has been assigned a value) and false otherwise */
    public boolean isSetApps() {
      return this.apps != null;
    }

    public void setAppsIsSet(boolean value) {
      if (!value) {
        this.apps = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case APPS:
        if (value == null) {
          unsetApps();
        } else {
          setApps((TAppId)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case APPS:
        return getApps();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case APPS:
        return isSetApps();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPorpsMetaByApps_args)
        return this.equals((getPorpsMetaByApps_args)that);
      return false;
    }

    public boolean equals(getPorpsMetaByApps_args that) {
      if (that == null)
        return false;

      boolean this_present_apps = true && this.isSetApps();
      boolean that_present_apps = true && that.isSetApps();
      if (this_present_apps || that_present_apps) {
        if (!(this_present_apps && that_present_apps))
          return false;
        if (!this.apps.equals(that.apps))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_apps = true && (isSetApps());
      list.add(present_apps);
      if (present_apps)
        list.add(apps.getValue());

      return list.hashCode();
    }

    @Override
    public int compareTo(getPorpsMetaByApps_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetApps()).compareTo(other.isSetApps());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetApps()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.apps, other.apps);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPorpsMetaByApps_args(");
      boolean first = true;

      sb.append("apps:");
      if (this.apps == null) {
        sb.append("null");
      } else {
        sb.append(this.apps);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPorpsMetaByApps_argsStandardSchemeFactory implements SchemeFactory {
      public getPorpsMetaByApps_argsStandardScheme getScheme() {
        return new getPorpsMetaByApps_argsStandardScheme();
      }
    }

    private static class getPorpsMetaByApps_argsStandardScheme extends StandardScheme<getPorpsMetaByApps_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPorpsMetaByApps_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // APPS
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.apps = com.yy.manager.thrift.to_service.TAppId.findByValue(iprot.readI32());
                struct.setAppsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPorpsMetaByApps_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.apps != null) {
          oprot.writeFieldBegin(APPS_FIELD_DESC);
          oprot.writeI32(struct.apps.getValue());
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPorpsMetaByApps_argsTupleSchemeFactory implements SchemeFactory {
      public getPorpsMetaByApps_argsTupleScheme getScheme() {
        return new getPorpsMetaByApps_argsTupleScheme();
      }
    }

    private static class getPorpsMetaByApps_argsTupleScheme extends TupleScheme<getPorpsMetaByApps_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPorpsMetaByApps_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetApps()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetApps()) {
          oprot.writeI32(struct.apps.getValue());
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPorpsMetaByApps_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.apps = com.yy.manager.thrift.to_service.TAppId.findByValue(iprot.readI32());
          struct.setAppsIsSet(true);
        }
      }
    }

  }

  public static class getPorpsMetaByApps_result implements org.apache.thrift.TBase<getPorpsMetaByApps_result, getPorpsMetaByApps_result._Fields>, java.io.Serializable, Cloneable, Comparable<getPorpsMetaByApps_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPorpsMetaByApps_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.LIST, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPorpsMetaByApps_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPorpsMetaByApps_resultTupleSchemeFactory());
    }

    public List<TPropsMeta> success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT              , "TPropsMeta"))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPorpsMetaByApps_result.class, metaDataMap);
    }

    public getPorpsMetaByApps_result() {
    }

    public getPorpsMetaByApps_result(
      List<TPropsMeta> success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPorpsMetaByApps_result(getPorpsMetaByApps_result other) {
      if (other.isSetSuccess()) {
        List<TPropsMeta> __this__success = new ArrayList<TPropsMeta>(other.success.size());
        for (TPropsMeta other_element : other.success) {
          __this__success.add(other_element);
        }
        this.success = __this__success;
      }
    }

    public getPorpsMetaByApps_result deepCopy() {
      return new getPorpsMetaByApps_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public int getSuccessSize() {
      return (this.success == null) ? 0 : this.success.size();
    }

    public java.util.Iterator<TPropsMeta> getSuccessIterator() {
      return (this.success == null) ? null : this.success.iterator();
    }

    public void addToSuccess(TPropsMeta elem) {
      if (this.success == null) {
        this.success = new ArrayList<TPropsMeta>();
      }
      this.success.add(elem);
    }

    public List<TPropsMeta> getSuccess() {
      return this.success;
    }

    public getPorpsMetaByApps_result setSuccess(List<TPropsMeta> success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((List<TPropsMeta>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPorpsMetaByApps_result)
        return this.equals((getPorpsMetaByApps_result)that);
      return false;
    }

    public boolean equals(getPorpsMetaByApps_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getPorpsMetaByApps_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPorpsMetaByApps_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPorpsMetaByApps_resultStandardSchemeFactory implements SchemeFactory {
      public getPorpsMetaByApps_resultStandardScheme getScheme() {
        return new getPorpsMetaByApps_resultStandardScheme();
      }
    }

    private static class getPorpsMetaByApps_resultStandardScheme extends StandardScheme<getPorpsMetaByApps_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPorpsMetaByApps_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                  struct.success = new ArrayList<TPropsMeta>(_list0.size);
                  TPropsMeta _elem1;
                  for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                  {
                    _elem1 = new TPropsMeta();
                    _elem1.read(iprot);
                    struct.success.add(_elem1);
                  }
                  iprot.readListEnd();
                }
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPorpsMetaByApps_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.success.size()));
            for (TPropsMeta _iter3 : struct.success)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPorpsMetaByApps_resultTupleSchemeFactory implements SchemeFactory {
      public getPorpsMetaByApps_resultTupleScheme getScheme() {
        return new getPorpsMetaByApps_resultTupleScheme();
      }
    }

    private static class getPorpsMetaByApps_resultTupleScheme extends TupleScheme<getPorpsMetaByApps_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPorpsMetaByApps_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          {
            oprot.writeI32(struct.success.size());
            for (TPropsMeta _iter4 : struct.success)
            {
              _iter4.write(oprot);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPorpsMetaByApps_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
            struct.success = new ArrayList<TPropsMeta>(_list5.size);
            TPropsMeta _elem6;
            for (int _i7 = 0; _i7 < _list5.size; ++_i7)
            {
              _elem6 = new TPropsMeta();
              _elem6.read(iprot);
              struct.success.add(_elem6);
            }
          }
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
