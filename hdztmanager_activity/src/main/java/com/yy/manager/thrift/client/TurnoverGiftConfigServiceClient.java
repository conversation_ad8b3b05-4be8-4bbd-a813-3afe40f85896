package com.yy.manager.thrift.client;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.manager.thrift.giftconfig.TPropsConfig;
import com.yy.manager.thrift.giftconfig.TPropsServiceV2;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * desc:营收只读接口
 *
 * @createBy w
 * @create 2019-12-19 19:44
 **/
@Component
public class TurnoverGiftConfigServiceClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "nythrift", registry = "yrpc-reg", owner = "${thrift_turnover_read_client_s2s_name}")
    protected TPropsServiceV2.Iface proxy = null;

    public TPropsServiceV2.Iface getProxy() {
        return proxy;
    }

    public Map<Integer, TPropsConfig> queryPropsConfigs(List<Integer> ids) {
        try {
            Map<Integer, TPropsConfig> propsConfig = getProxy().getPropsConfig(ids);
            log.info("queryPropsConfigs,ret:{}", JSON.toJSONString(propsConfig));
            return propsConfig;
        } catch (Exception e) {
            log.error("TurnoverGiftConfigServiceClient.queryPropsConfigs error,,e:{}", e.getMessage(), e);
        }
        return Maps.newHashMap();

    }

}
