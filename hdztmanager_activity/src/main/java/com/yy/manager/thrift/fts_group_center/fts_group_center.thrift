 # 交友 刘志斌 提供/2021-10-15

namespace java com.yy.manager.thrift.fts_group_center

// s2s name
// 测试：fts_group_center_test
// 生产：fts_group_center

struct CommonRet
{
    1:i64 code; // 0-success
    2:string message;
}

enum Source {
    Unknown = 0;
    SourceJY = 500;
    SourcePK = 600;
    SourceBaby = 400;
    SourceZW  = 800;
    SourcePW  = 900
}

enum QueryType {
    TypeCompere = 0; // 主持，默认
    TypeGuild   = 1; // 公会
    TypeChannel = 2; // 厅
    TypeTing    = 3; // 厅天团
    TypeGroup   = 4; // 陪玩业务-团（陪玩的一种特殊角色）
}

enum Visibility {
    Normal = 0; // 正式分组
    Gray = 1; // 灰度分组
}

struct GroupResult
{
    1:string group; // 分组结果
}

/*
当 r_id == 1005 的时候，表示查询的是通用分组，此时通用分组结果定义如下:
0 - 无分组
1 - 帽子
2 - 超主
3 - 普通主持
 */
struct QueryGroupReq
{
    1:i64 r_id; // 规则ID
    2:list<string> members; // 请求的主持 厅-sid_ssid
    3:QueryType query_type; // 请求类型
    4:Source source; // 业务源
    5:Visibility visible; // 0-普通数据源 1-灰度数据源
    15:map<string, string> expand; // map{queryAll: 1}
}

struct BatchQueryGroupReq
{
    1:list<QueryGroupReq> request_list;
}

struct BatchQueryGroupResp
{
    1:CommonRet header;
    2:list<QueryGroupResp> response_list;
}

struct QueryGroupResp
{
    1:CommonRet header;
    2:map<string, GroupResult> group_map;
    15:map<string, string> expand;
}

struct ActivityInfo
{
    1:string name;      // 活动名称
    2:i64 startTime;    // 活动开始时间，单位：秒
    3:i64 endTime;      // 活动结束时间，单位：秒
}

struct BatchGetActivityInfoResp
{
    1:CommonRet header;
    2:map<string, ActivityInfo> group_map;
}

// CacheMode 缓存模式
enum CacheMode {
  CACHE_REJECT_MODE = 1, // 无缓存，每次都实时计算分组结果
  CACHE_FIRST_MODE = 2,  // 缓存优先，缓存不存在则实时计算
  CACHE_ONLY_MODE = 3,   // 仅读取缓存，以缓存结果为准
}

// Role 分组对象定义
struct Role {
  1: string id                    // 分组对象ID，外部查询分组的时候，传入的 queryType
  2: string name                  // 分组对象名称
  3: list<string> businessIds     // 对应业务 用于展开分组角色
  4: i32 needConfirmFlow          // 是否需要走确认分组流程，一个活动只能有一个流程需要走这个流程
  5: string ruleProgram           // 规则源代码，如果为空，那么表示不会进行规则计算
  6: string defaultResult         // 默认分组结果
  7: list<GroupItem> groupItems   // 有哪些分组ID，name
  8: i32 preActCacheMode          // 活动前缓存模式
  9: i32 inActCacheMode           // 活动中缓存模式
  10: i32 afterActCacheMode       // 活动后缓存模式
  11: i64 recvGiftTriggerTime     // 用户收到礼物的时候，如果收礼的时间在当前设置时间之前，那么会触发分组规则计算
}

// GroupItem 分组定义
struct GroupItem {
  1: string id                    // 分组唯一ID
  2: string name                  // 分组名称
}

// Activity 活动定义
struct Activity {
  1: string id                    // 活动唯一ID
  2: string name                  // 活动名称
  3: string desc                  // 活动说明
  4: list<string> businessIds     // 涉及业务 JY:交友 ZW:追玩
  5: list<Role> roleList          // 角色列表
  6: i64 startTime                // 活动开始时间，单位：秒
  7: i64 endTime                  // 活动结束时间，单位：秒
  8: list<string> bypassActIds    // 旁路活动ID列表，请求该活动分组的时候，将会旁路一份请求到指定的活动中（适用于同一个活动，需要不同规则分组数据分析的情况）
  9: i64 updateTime               // 最后更新时间，单位：秒
  10: string lastOperator         // 最后操作者，通行证
}

// 复制活动分组配置
struct CopyActivityReq
{
    1:i64 uid;          // 操作人UID
    2:string fromActId; // 来源活动分组ID
    3:string toActId;   // 新的活动分组ID
    4:string name;      // 新的活动分组名称
    5:string desc;      // 新的活动分组描述
    6:i64 startTime;    // 新的活动开始时间，单位：秒
    7:i64 endTime;      // 新的活动结束时间，单位：秒
}

// 活动分组基础信息
struct ActivityBasicInfo
{
    1:string actId;     // 活动分组ID
    2:string name;      // 活动名称
    3:string desc;      // 活动描述
    4:i64 startTime;    // 活动开始时间，单位：秒
    5:i64 endTime;      // 活动结束时间，单位：秒
}

// 获取活动分组信息响应
struct GetActivityInfoResp
{
    1:CommonRet header;
    2:Activity info;
}

// GroupWhiteConfig 分组白名单配置
struct GroupWhiteConfig {
  1: string member;                // 分组的成员
  2: string groupId;               // 分组结果ID，对应 GroupItem.id 的值
}

// 获取分组白名单请求
struct GetGroupWhiteConfigReq {
    1:string actId;         // 活动分组ID
    2:string businessId;    // 业务ID JY:交友 ZW:追玩
    3:string roleId;        // 分组角色ID 通常是 COMPERE:主持分组 GUILD:工会分组 CHANNEL:厅分组 TING:厅天团 PWTUAN:陪玩团
    4:Visibility visible;   // 0-普通数据源 1-灰度数据源
}

struct GetGroupWhiteConfigResp {
    1:CommonRet header;
    2:list<GroupWhiteConfig> config;
}

struct UpdateGroupWhiteConfigReq {
    1:i64 uid;               // 操作人UID
    2:string actId;          // 活动分组ID
    3:string businessId;     // 业务ID JY:交友 ZW:追玩
    4:string roleId;         // 分组角色ID 通常是 COMPERE:主持分组 GUILD:工会分组 CHANNEL:厅分组 TING:厅天团 PWTUAN:陪玩团
    5:Visibility visible;    // 0-普通数据源 1-灰度数据源
    6:list<GroupWhiteConfig> config; // 白名单列表，覆盖式更新
}

service FtsGroupService {
    // 测试链接
    void ping();

    // 查询分组
    QueryGroupResp QueryGroup(1:QueryGroupReq req);

    // 批量查询分组
    BatchQueryGroupResp BatchQueryGroup(1:BatchQueryGroupReq req);

    // ------------------------- 管理类接口 -------------------------

    // 查询活动的信息
    BatchGetActivityInfoResp BatchGetActivityInfo(1:list<string> actIds);

    // 复制活动分组配置
    CommonRet CopyActivity(1:CopyActivityReq req);

    // 获取活动分组配置信息
    GetActivityInfoResp GetActivityInfo(1:string actId);

    // 更新活动分组基础信息
    CommonRet UpdateActivityBasicInfo(1:i64 uid, 2:ActivityBasicInfo info);

    // 添加活动分组配置
    CommonRet AddActivity(1:i64 opUID, 2:Activity activity);

    // 更新活动分组配置 信息
    CommonRet UpdateActivity(1:i64 opUID, 2:Activity activity);

    // 删除活动分组配置（对应的分组结果也将删除）
    CommonRet DeleteActivity(1:i64 opUID, 2:string actID);

    // 获取分组白名单
    GetGroupWhiteConfigResp GetGroupWhiteConfig(1:GetGroupWhiteConfigReq req);

    // 更新分组白名单配置
    CommonRet UpdateGroupWhiteConfig(1:UpdateGroupWhiteConfigReq req);
}

