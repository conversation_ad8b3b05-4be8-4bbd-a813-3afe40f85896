/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class UserInfoResp implements org.apache.thrift.TBase<UserInfoResp, UserInfoResp._Fields>, java.io.Serializable, Cloneable, Comparable<UserInfoResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("UserInfoResp");

  private static final org.apache.thrift.protocol.TField RET_FIELD_DESC = new org.apache.thrift.protocol.TField("ret", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField RET_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("ret_map", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new UserInfoRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new UserInfoRespTupleSchemeFactory());
  }

  public CommonRet ret; // required
  public Map<Long,UserInfo> ret_map; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RET((short)1, "ret"),
    RET_MAP((short)2, "ret_map");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RET
          return RET;
        case 2: // RET_MAP
          return RET_MAP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RET, new org.apache.thrift.meta_data.FieldMetaData("ret", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CommonRet.class)));
    tmpMap.put(_Fields.RET_MAP, new org.apache.thrift.meta_data.FieldMetaData("ret_map", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64),
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UserInfo.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(UserInfoResp.class, metaDataMap);
  }

  public UserInfoResp() {
  }

  public UserInfoResp(
    CommonRet ret,
    Map<Long,UserInfo> ret_map)
  {
    this();
    this.ret = ret;
    this.ret_map = ret_map;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public UserInfoResp(UserInfoResp other) {
    if (other.isSetRet()) {
      this.ret = new CommonRet(other.ret);
    }
    if (other.isSetRet_map()) {
      Map<Long,UserInfo> __this__ret_map = new HashMap<Long,UserInfo>(other.ret_map.size());
      for (Map.Entry<Long, UserInfo> other_element : other.ret_map.entrySet()) {

        Long other_element_key = other_element.getKey();
        UserInfo other_element_value = other_element.getValue();

        Long __this__ret_map_copy_key = other_element_key;

        UserInfo __this__ret_map_copy_value = new UserInfo(other_element_value);

        __this__ret_map.put(__this__ret_map_copy_key, __this__ret_map_copy_value);
      }
      this.ret_map = __this__ret_map;
    }
  }

  public UserInfoResp deepCopy() {
    return new UserInfoResp(this);
  }

  @Override
  public void clear() {
    this.ret = null;
    this.ret_map = null;
  }

  public CommonRet getRet() {
    return this.ret;
  }

  public UserInfoResp setRet(CommonRet ret) {
    this.ret = ret;
    return this;
  }

  public void unsetRet() {
    this.ret = null;
  }

  /** Returns true if field ret is set (has been assigned a value) and false otherwise */
  public boolean isSetRet() {
    return this.ret != null;
  }

  public void setRetIsSet(boolean value) {
    if (!value) {
      this.ret = null;
    }
  }

  public int getRet_mapSize() {
    return (this.ret_map == null) ? 0 : this.ret_map.size();
  }

  public void putToRet_map(long key, UserInfo val) {
    if (this.ret_map == null) {
      this.ret_map = new HashMap<Long,UserInfo>();
    }
    this.ret_map.put(key, val);
  }

  public Map<Long,UserInfo> getRet_map() {
    return this.ret_map;
  }

  public UserInfoResp setRet_map(Map<Long,UserInfo> ret_map) {
    this.ret_map = ret_map;
    return this;
  }

  public void unsetRet_map() {
    this.ret_map = null;
  }

  /** Returns true if field ret_map is set (has been assigned a value) and false otherwise */
  public boolean isSetRet_map() {
    return this.ret_map != null;
  }

  public void setRet_mapIsSet(boolean value) {
    if (!value) {
      this.ret_map = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RET:
      if (value == null) {
        unsetRet();
      } else {
        setRet((CommonRet)value);
      }
      break;

    case RET_MAP:
      if (value == null) {
        unsetRet_map();
      } else {
        setRet_map((Map<Long,UserInfo>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RET:
      return getRet();

    case RET_MAP:
      return getRet_map();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RET:
      return isSetRet();
    case RET_MAP:
      return isSetRet_map();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof UserInfoResp)
      return this.equals((UserInfoResp)that);
    return false;
  }

  public boolean equals(UserInfoResp that) {
    if (that == null)
      return false;

    boolean this_present_ret = true && this.isSetRet();
    boolean that_present_ret = true && that.isSetRet();
    if (this_present_ret || that_present_ret) {
      if (!(this_present_ret && that_present_ret))
        return false;
      if (!this.ret.equals(that.ret))
        return false;
    }

    boolean this_present_ret_map = true && this.isSetRet_map();
    boolean that_present_ret_map = true && that.isSetRet_map();
    if (this_present_ret_map || that_present_ret_map) {
      if (!(this_present_ret_map && that_present_ret_map))
        return false;
      if (!this.ret_map.equals(that.ret_map))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ret = true && (isSetRet());
    list.add(present_ret);
    if (present_ret)
      list.add(ret);

    boolean present_ret_map = true && (isSetRet_map());
    list.add(present_ret_map);
    if (present_ret_map)
      list.add(ret_map);

    return list.hashCode();
  }

  @Override
  public int compareTo(UserInfoResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRet()).compareTo(other.isSetRet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret, other.ret);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRet_map()).compareTo(other.isSetRet_map());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet_map()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret_map, other.ret_map);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("UserInfoResp(");
    boolean first = true;

    sb.append("ret:");
    if (this.ret == null) {
      sb.append("null");
    } else {
      sb.append(this.ret);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ret_map:");
    if (this.ret_map == null) {
      sb.append("null");
    } else {
      sb.append(this.ret_map);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (ret != null) {
      ret.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class UserInfoRespStandardSchemeFactory implements SchemeFactory {
    public UserInfoRespStandardScheme getScheme() {
      return new UserInfoRespStandardScheme();
    }
  }

  private static class UserInfoRespStandardScheme extends StandardScheme<UserInfoResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, UserInfoResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // RET
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.ret = new CommonRet();
              struct.ret.read(iprot);
              struct.setRetIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RET_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map8 = iprot.readMapBegin();
                struct.ret_map = new HashMap<Long,UserInfo>(2*_map8.size);
                long _key9;
                UserInfo _val10;
                for (int _i11 = 0; _i11 < _map8.size; ++_i11)
                {
                  _key9 = iprot.readI64();
                  _val10 = new UserInfo();
                  _val10.read(iprot);
                  struct.ret_map.put(_key9, _val10);
                }
                iprot.readMapEnd();
              }
              struct.setRet_mapIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, UserInfoResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.ret != null) {
        oprot.writeFieldBegin(RET_FIELD_DESC);
        struct.ret.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.ret_map != null) {
        oprot.writeFieldBegin(RET_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, struct.ret_map.size()));
          for (Map.Entry<Long, UserInfo> _iter12 : struct.ret_map.entrySet())
          {
            oprot.writeI64(_iter12.getKey());
            _iter12.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class UserInfoRespTupleSchemeFactory implements SchemeFactory {
    public UserInfoRespTupleScheme getScheme() {
      return new UserInfoRespTupleScheme();
    }
  }

  private static class UserInfoRespTupleScheme extends TupleScheme<UserInfoResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, UserInfoResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRet()) {
        optionals.set(0);
      }
      if (struct.isSetRet_map()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRet()) {
        struct.ret.write(oprot);
      }
      if (struct.isSetRet_map()) {
        {
          oprot.writeI32(struct.ret_map.size());
          for (Map.Entry<Long, UserInfo> _iter13 : struct.ret_map.entrySet())
          {
            oprot.writeI64(_iter13.getKey());
            _iter13.getValue().write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, UserInfoResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.ret = new CommonRet();
        struct.ret.read(iprot);
        struct.setRetIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map14 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.ret_map = new HashMap<Long,UserInfo>(2*_map14.size);
          long _key15;
          UserInfo _val16;
          for (int _i17 = 0; _i17 < _map14.size; ++_i17)
          {
            _key15 = iprot.readI64();
            _val16 = new UserInfo();
            _val16.read(iprot);
            struct.ret_map.put(_key15, _val16);
          }
        }
        struct.setRet_mapIsSet(true);
      }
    }
  }

}

