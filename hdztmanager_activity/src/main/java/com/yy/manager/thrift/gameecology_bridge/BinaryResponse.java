/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.gameecology_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-07-25")
public class BinaryResponse implements org.apache.thrift.TBase<BinaryResponse, BinaryResponse._Fields>, java.io.Serializable, Cloneable, Comparable<BinaryResponse> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BinaryResponse");

  private static final org.apache.thrift.protocol.TField SEQ_FIELD_DESC = new org.apache.thrift.protocol.TField("seq", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField RESULT_FIELD_DESC = new org.apache.thrift.protocol.TField("result", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField REASON_FIELD_DESC = new org.apache.thrift.protocol.TField("reason", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField RID_FIELD_DESC = new org.apache.thrift.protocol.TField("rid", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField FID_FIELD_DESC = new org.apache.thrift.protocol.TField("fid", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("data", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField TS_FIELD_DESC = new org.apache.thrift.protocol.TField("ts", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField EXTJSON_FIELD_DESC = new org.apache.thrift.protocol.TField("extjson", org.apache.thrift.protocol.TType.STRING, (short)8);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BinaryResponseStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BinaryResponseTupleSchemeFactory());
  }

  public String seq; // required
  public long result; // required
  public String reason; // required
  public int rid; // required
  public int fid; // required
  public ByteBuffer data; // required
  public long ts; // required
  public String extjson; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SEQ((short)1, "seq"),
    RESULT((short)2, "result"),
    REASON((short)3, "reason"),
    RID((short)4, "rid"),
    FID((short)5, "fid"),
    DATA((short)6, "data"),
    TS((short)7, "ts"),
    EXTJSON((short)8, "extjson");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SEQ
          return SEQ;
        case 2: // RESULT
          return RESULT;
        case 3: // REASON
          return REASON;
        case 4: // RID
          return RID;
        case 5: // FID
          return FID;
        case 6: // DATA
          return DATA;
        case 7: // TS
          return TS;
        case 8: // EXTJSON
          return EXTJSON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RESULT_ISSET_ID = 0;
  private static final int __RID_ISSET_ID = 1;
  private static final int __FID_ISSET_ID = 2;
  private static final int __TS_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SEQ, new org.apache.thrift.meta_data.FieldMetaData("seq", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RESULT, new org.apache.thrift.meta_data.FieldMetaData("result", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.REASON, new org.apache.thrift.meta_data.FieldMetaData("reason", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RID, new org.apache.thrift.meta_data.FieldMetaData("rid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FID, new org.apache.thrift.meta_data.FieldMetaData("fid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.DATA, new org.apache.thrift.meta_data.FieldMetaData("data", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING        , true)));
    tmpMap.put(_Fields.TS, new org.apache.thrift.meta_data.FieldMetaData("ts", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXTJSON, new org.apache.thrift.meta_data.FieldMetaData("extjson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BinaryResponse.class, metaDataMap);
  }

  public BinaryResponse() {
  }

  public BinaryResponse(
    String seq,
    long result,
    String reason,
    int rid,
    int fid,
    ByteBuffer data,
    long ts,
    String extjson)
  {
    this();
    this.seq = seq;
    this.result = result;
    setResultIsSet(true);
    this.reason = reason;
    this.rid = rid;
    setRidIsSet(true);
    this.fid = fid;
    setFidIsSet(true);
    this.data = org.apache.thrift.TBaseHelper.copyBinary(data);
    this.ts = ts;
    setTsIsSet(true);
    this.extjson = extjson;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BinaryResponse(BinaryResponse other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetSeq()) {
      this.seq = other.seq;
    }
    this.result = other.result;
    if (other.isSetReason()) {
      this.reason = other.reason;
    }
    this.rid = other.rid;
    this.fid = other.fid;
    if (other.isSetData()) {
      this.data = org.apache.thrift.TBaseHelper.copyBinary(other.data);
    }
    this.ts = other.ts;
    if (other.isSetExtjson()) {
      this.extjson = other.extjson;
    }
  }

  public BinaryResponse deepCopy() {
    return new BinaryResponse(this);
  }

  @Override
  public void clear() {
    this.seq = null;
    setResultIsSet(false);
    this.result = 0;
    this.reason = null;
    setRidIsSet(false);
    this.rid = 0;
    setFidIsSet(false);
    this.fid = 0;
    this.data = null;
    setTsIsSet(false);
    this.ts = 0;
    this.extjson = null;
  }

  public String getSeq() {
    return this.seq;
  }

  public BinaryResponse setSeq(String seq) {
    this.seq = seq;
    return this;
  }

  public void unsetSeq() {
    this.seq = null;
  }

  /** Returns true if field seq is set (has been assigned a value) and false otherwise */
  public boolean isSetSeq() {
    return this.seq != null;
  }

  public void setSeqIsSet(boolean value) {
    if (!value) {
      this.seq = null;
    }
  }

  public long getResult() {
    return this.result;
  }

  public BinaryResponse setResult(long result) {
    this.result = result;
    setResultIsSet(true);
    return this;
  }

  public void unsetResult() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  /** Returns true if field result is set (has been assigned a value) and false otherwise */
  public boolean isSetResult() {
    return EncodingUtils.testBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  public void setResultIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RESULT_ISSET_ID, value);
  }

  public String getReason() {
    return this.reason;
  }

  public BinaryResponse setReason(String reason) {
    this.reason = reason;
    return this;
  }

  public void unsetReason() {
    this.reason = null;
  }

  /** Returns true if field reason is set (has been assigned a value) and false otherwise */
  public boolean isSetReason() {
    return this.reason != null;
  }

  public void setReasonIsSet(boolean value) {
    if (!value) {
      this.reason = null;
    }
  }

  public int getRid() {
    return this.rid;
  }

  public BinaryResponse setRid(int rid) {
    this.rid = rid;
    setRidIsSet(true);
    return this;
  }

  public void unsetRid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RID_ISSET_ID);
  }

  /** Returns true if field rid is set (has been assigned a value) and false otherwise */
  public boolean isSetRid() {
    return EncodingUtils.testBit(__isset_bitfield, __RID_ISSET_ID);
  }

  public void setRidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RID_ISSET_ID, value);
  }

  public int getFid() {
    return this.fid;
  }

  public BinaryResponse setFid(int fid) {
    this.fid = fid;
    setFidIsSet(true);
    return this;
  }

  public void unsetFid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FID_ISSET_ID);
  }

  /** Returns true if field fid is set (has been assigned a value) and false otherwise */
  public boolean isSetFid() {
    return EncodingUtils.testBit(__isset_bitfield, __FID_ISSET_ID);
  }

  public void setFidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FID_ISSET_ID, value);
  }

  public byte[] getData() {
    setData(org.apache.thrift.TBaseHelper.rightSize(data));
    return data == null ? null : data.array();
  }

  public ByteBuffer bufferForData() {
    return org.apache.thrift.TBaseHelper.copyBinary(data);
  }

  public BinaryResponse setData(byte[] data) {
    this.data = data == null ? (ByteBuffer)null : ByteBuffer.wrap(Arrays.copyOf(data, data.length));
    return this;
  }

  public BinaryResponse setData(ByteBuffer data) {
    this.data = org.apache.thrift.TBaseHelper.copyBinary(data);
    return this;
  }

  public void unsetData() {
    this.data = null;
  }

  /** Returns true if field data is set (has been assigned a value) and false otherwise */
  public boolean isSetData() {
    return this.data != null;
  }

  public void setDataIsSet(boolean value) {
    if (!value) {
      this.data = null;
    }
  }

  public long getTs() {
    return this.ts;
  }

  public BinaryResponse setTs(long ts) {
    this.ts = ts;
    setTsIsSet(true);
    return this;
  }

  public void unsetTs() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TS_ISSET_ID);
  }

  /** Returns true if field ts is set (has been assigned a value) and false otherwise */
  public boolean isSetTs() {
    return EncodingUtils.testBit(__isset_bitfield, __TS_ISSET_ID);
  }

  public void setTsIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TS_ISSET_ID, value);
  }

  public String getExtjson() {
    return this.extjson;
  }

  public BinaryResponse setExtjson(String extjson) {
    this.extjson = extjson;
    return this;
  }

  public void unsetExtjson() {
    this.extjson = null;
  }

  /** Returns true if field extjson is set (has been assigned a value) and false otherwise */
  public boolean isSetExtjson() {
    return this.extjson != null;
  }

  public void setExtjsonIsSet(boolean value) {
    if (!value) {
      this.extjson = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SEQ:
      if (value == null) {
        unsetSeq();
      } else {
        setSeq((String)value);
      }
      break;

    case RESULT:
      if (value == null) {
        unsetResult();
      } else {
        setResult((Long)value);
      }
      break;

    case REASON:
      if (value == null) {
        unsetReason();
      } else {
        setReason((String)value);
      }
      break;

    case RID:
      if (value == null) {
        unsetRid();
      } else {
        setRid((Integer)value);
      }
      break;

    case FID:
      if (value == null) {
        unsetFid();
      } else {
        setFid((Integer)value);
      }
      break;

    case DATA:
      if (value == null) {
        unsetData();
      } else {
        setData((ByteBuffer)value);
      }
      break;

    case TS:
      if (value == null) {
        unsetTs();
      } else {
        setTs((Long)value);
      }
      break;

    case EXTJSON:
      if (value == null) {
        unsetExtjson();
      } else {
        setExtjson((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SEQ:
      return getSeq();

    case RESULT:
      return getResult();

    case REASON:
      return getReason();

    case RID:
      return getRid();

    case FID:
      return getFid();

    case DATA:
      return getData();

    case TS:
      return getTs();

    case EXTJSON:
      return getExtjson();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SEQ:
      return isSetSeq();
    case RESULT:
      return isSetResult();
    case REASON:
      return isSetReason();
    case RID:
      return isSetRid();
    case FID:
      return isSetFid();
    case DATA:
      return isSetData();
    case TS:
      return isSetTs();
    case EXTJSON:
      return isSetExtjson();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BinaryResponse)
      return this.equals((BinaryResponse)that);
    return false;
  }

  public boolean equals(BinaryResponse that) {
    if (that == null)
      return false;

    boolean this_present_seq = true && this.isSetSeq();
    boolean that_present_seq = true && that.isSetSeq();
    if (this_present_seq || that_present_seq) {
      if (!(this_present_seq && that_present_seq))
        return false;
      if (!this.seq.equals(that.seq))
        return false;
    }

    boolean this_present_result = true;
    boolean that_present_result = true;
    if (this_present_result || that_present_result) {
      if (!(this_present_result && that_present_result))
        return false;
      if (this.result != that.result)
        return false;
    }

    boolean this_present_reason = true && this.isSetReason();
    boolean that_present_reason = true && that.isSetReason();
    if (this_present_reason || that_present_reason) {
      if (!(this_present_reason && that_present_reason))
        return false;
      if (!this.reason.equals(that.reason))
        return false;
    }

    boolean this_present_rid = true;
    boolean that_present_rid = true;
    if (this_present_rid || that_present_rid) {
      if (!(this_present_rid && that_present_rid))
        return false;
      if (this.rid != that.rid)
        return false;
    }

    boolean this_present_fid = true;
    boolean that_present_fid = true;
    if (this_present_fid || that_present_fid) {
      if (!(this_present_fid && that_present_fid))
        return false;
      if (this.fid != that.fid)
        return false;
    }

    boolean this_present_data = true && this.isSetData();
    boolean that_present_data = true && that.isSetData();
    if (this_present_data || that_present_data) {
      if (!(this_present_data && that_present_data))
        return false;
      if (!this.data.equals(that.data))
        return false;
    }

    boolean this_present_ts = true;
    boolean that_present_ts = true;
    if (this_present_ts || that_present_ts) {
      if (!(this_present_ts && that_present_ts))
        return false;
      if (this.ts != that.ts)
        return false;
    }

    boolean this_present_extjson = true && this.isSetExtjson();
    boolean that_present_extjson = true && that.isSetExtjson();
    if (this_present_extjson || that_present_extjson) {
      if (!(this_present_extjson && that_present_extjson))
        return false;
      if (!this.extjson.equals(that.extjson))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_seq = true && (isSetSeq());
    list.add(present_seq);
    if (present_seq)
      list.add(seq);

    boolean present_result = true;
    list.add(present_result);
    if (present_result)
      list.add(result);

    boolean present_reason = true && (isSetReason());
    list.add(present_reason);
    if (present_reason)
      list.add(reason);

    boolean present_rid = true;
    list.add(present_rid);
    if (present_rid)
      list.add(rid);

    boolean present_fid = true;
    list.add(present_fid);
    if (present_fid)
      list.add(fid);

    boolean present_data = true && (isSetData());
    list.add(present_data);
    if (present_data)
      list.add(data);

    boolean present_ts = true;
    list.add(present_ts);
    if (present_ts)
      list.add(ts);

    boolean present_extjson = true && (isSetExtjson());
    list.add(present_extjson);
    if (present_extjson)
      list.add(extjson);

    return list.hashCode();
  }

  @Override
  public int compareTo(BinaryResponse other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSeq()).compareTo(other.isSetSeq());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeq()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seq, other.seq);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetResult()).compareTo(other.isSetResult());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResult()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.result, other.result);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReason()).compareTo(other.isSetReason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reason, other.reason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRid()).compareTo(other.isSetRid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rid, other.rid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFid()).compareTo(other.isSetFid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fid, other.fid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetData()).compareTo(other.isSetData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.data, other.data);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTs()).compareTo(other.isSetTs());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTs()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ts, other.ts);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtjson()).compareTo(other.isSetExtjson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtjson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extjson, other.extjson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BinaryResponse(");
    boolean first = true;

    sb.append("seq:");
    if (this.seq == null) {
      sb.append("null");
    } else {
      sb.append(this.seq);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("result:");
    sb.append(this.result);
    first = false;
    if (!first) sb.append(", ");
    sb.append("reason:");
    if (this.reason == null) {
      sb.append("null");
    } else {
      sb.append(this.reason);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rid:");
    sb.append(this.rid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("fid:");
    sb.append(this.fid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("data:");
    if (this.data == null) {
      sb.append("null");
    } else {
      org.apache.thrift.TBaseHelper.toString(this.data, sb);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ts:");
    sb.append(this.ts);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extjson:");
    if (this.extjson == null) {
      sb.append("null");
    } else {
      sb.append(this.extjson);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BinaryResponseStandardSchemeFactory implements SchemeFactory {
    public BinaryResponseStandardScheme getScheme() {
      return new BinaryResponseStandardScheme();
    }
  }

  private static class BinaryResponseStandardScheme extends StandardScheme<BinaryResponse> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BinaryResponse struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SEQ
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.seq = iprot.readString();
              struct.setSeqIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RESULT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.result = iprot.readI64();
              struct.setResultIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REASON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.reason = iprot.readString();
              struct.setReasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // RID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rid = iprot.readI32();
              struct.setRidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // FID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.fid = iprot.readI32();
              struct.setFidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.data = iprot.readBinary();
              struct.setDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // TS
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ts = iprot.readI64();
              struct.setTsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // EXTJSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extjson = iprot.readString();
              struct.setExtjsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BinaryResponse struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.seq != null) {
        oprot.writeFieldBegin(SEQ_FIELD_DESC);
        oprot.writeString(struct.seq);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RESULT_FIELD_DESC);
      oprot.writeI64(struct.result);
      oprot.writeFieldEnd();
      if (struct.reason != null) {
        oprot.writeFieldBegin(REASON_FIELD_DESC);
        oprot.writeString(struct.reason);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RID_FIELD_DESC);
      oprot.writeI32(struct.rid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FID_FIELD_DESC);
      oprot.writeI32(struct.fid);
      oprot.writeFieldEnd();
      if (struct.data != null) {
        oprot.writeFieldBegin(DATA_FIELD_DESC);
        oprot.writeBinary(struct.data);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TS_FIELD_DESC);
      oprot.writeI64(struct.ts);
      oprot.writeFieldEnd();
      if (struct.extjson != null) {
        oprot.writeFieldBegin(EXTJSON_FIELD_DESC);
        oprot.writeString(struct.extjson);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BinaryResponseTupleSchemeFactory implements SchemeFactory {
    public BinaryResponseTupleScheme getScheme() {
      return new BinaryResponseTupleScheme();
    }
  }

  private static class BinaryResponseTupleScheme extends TupleScheme<BinaryResponse> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BinaryResponse struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSeq()) {
        optionals.set(0);
      }
      if (struct.isSetResult()) {
        optionals.set(1);
      }
      if (struct.isSetReason()) {
        optionals.set(2);
      }
      if (struct.isSetRid()) {
        optionals.set(3);
      }
      if (struct.isSetFid()) {
        optionals.set(4);
      }
      if (struct.isSetData()) {
        optionals.set(5);
      }
      if (struct.isSetTs()) {
        optionals.set(6);
      }
      if (struct.isSetExtjson()) {
        optionals.set(7);
      }
      oprot.writeBitSet(optionals, 8);
      if (struct.isSetSeq()) {
        oprot.writeString(struct.seq);
      }
      if (struct.isSetResult()) {
        oprot.writeI64(struct.result);
      }
      if (struct.isSetReason()) {
        oprot.writeString(struct.reason);
      }
      if (struct.isSetRid()) {
        oprot.writeI32(struct.rid);
      }
      if (struct.isSetFid()) {
        oprot.writeI32(struct.fid);
      }
      if (struct.isSetData()) {
        oprot.writeBinary(struct.data);
      }
      if (struct.isSetTs()) {
        oprot.writeI64(struct.ts);
      }
      if (struct.isSetExtjson()) {
        oprot.writeString(struct.extjson);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BinaryResponse struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(8);
      if (incoming.get(0)) {
        struct.seq = iprot.readString();
        struct.setSeqIsSet(true);
      }
      if (incoming.get(1)) {
        struct.result = iprot.readI64();
        struct.setResultIsSet(true);
      }
      if (incoming.get(2)) {
        struct.reason = iprot.readString();
        struct.setReasonIsSet(true);
      }
      if (incoming.get(3)) {
        struct.rid = iprot.readI32();
        struct.setRidIsSet(true);
      }
      if (incoming.get(4)) {
        struct.fid = iprot.readI32();
        struct.setFidIsSet(true);
      }
      if (incoming.get(5)) {
        struct.data = iprot.readBinary();
        struct.setDataIsSet(true);
      }
      if (incoming.get(6)) {
        struct.ts = iprot.readI64();
        struct.setTsIsSet(true);
      }
      if (incoming.get(7)) {
        struct.extjson = iprot.readString();
        struct.setExtjsonIsSet(true);
      }
    }
  }

}

