/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.saibao;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2025-04-03")
public class RoomGameResultVO implements org.apache.thrift.TBase<RoomGameResultVO, RoomGameResultVO._Fields>, java.io.Serializable, Cloneable, Comparable<RoomGameResultVO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RoomGameResultVO");

  private static final org.apache.thrift.protocol.TField ROOM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roomId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField BATTLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("battleId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField STATE_FIELD_DESC = new org.apache.thrift.protocol.TField("state", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField TEAM_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("teamMap", org.apache.thrift.protocol.TType.MAP, (short)4);
  private static final org.apache.thrift.protocol.TField BATTLE_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("battleStatus", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField ROOM_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("roomStatus", org.apache.thrift.protocol.TType.I32, (short)11);
  private static final org.apache.thrift.protocol.TField RESULT_FLAG_FIELD_DESC = new org.apache.thrift.protocol.TField("resultFlag", org.apache.thrift.protocol.TType.I32, (short)12);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RoomGameResultVOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RoomGameResultVOTupleSchemeFactory());
  }

  public String roomId; // required
  public String battleId; // required
  /**
   * 0:未开始 1:进行中 2:结束
   * 
   */
  public int state; // required
  public Map<Integer,GameTeamVO> teamMap; // required
  public int battleStatus; // required
  public int roomStatus; // required
  public int resultFlag; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROOM_ID((short)1, "roomId"),
    BATTLE_ID((short)2, "battleId"),
    /**
     * 0:未开始 1:进行中 2:结束
     * 
     */
    STATE((short)3, "state"),
    TEAM_MAP((short)4, "teamMap"),
    BATTLE_STATUS((short)10, "battleStatus"),
    ROOM_STATUS((short)11, "roomStatus"),
    RESULT_FLAG((short)12, "resultFlag");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROOM_ID
          return ROOM_ID;
        case 2: // BATTLE_ID
          return BATTLE_ID;
        case 3: // STATE
          return STATE;
        case 4: // TEAM_MAP
          return TEAM_MAP;
        case 10: // BATTLE_STATUS
          return BATTLE_STATUS;
        case 11: // ROOM_STATUS
          return ROOM_STATUS;
        case 12: // RESULT_FLAG
          return RESULT_FLAG;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __STATE_ISSET_ID = 0;
  private static final int __BATTLESTATUS_ISSET_ID = 1;
  private static final int __ROOMSTATUS_ISSET_ID = 2;
  private static final int __RESULTFLAG_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROOM_ID, new org.apache.thrift.meta_data.FieldMetaData("roomId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BATTLE_ID, new org.apache.thrift.meta_data.FieldMetaData("battleId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATE, new org.apache.thrift.meta_data.FieldMetaData("state", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TEAM_MAP, new org.apache.thrift.meta_data.FieldMetaData("teamMap", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "GameTeamVO"))));
    tmpMap.put(_Fields.BATTLE_STATUS, new org.apache.thrift.meta_data.FieldMetaData("battleStatus", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ROOM_STATUS, new org.apache.thrift.meta_data.FieldMetaData("roomStatus", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.RESULT_FLAG, new org.apache.thrift.meta_data.FieldMetaData("resultFlag", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RoomGameResultVO.class, metaDataMap);
  }

  public RoomGameResultVO() {
  }

  public RoomGameResultVO(
    String roomId,
    String battleId,
    int state,
    Map<Integer,GameTeamVO> teamMap,
    int battleStatus,
    int roomStatus,
    int resultFlag)
  {
    this();
    this.roomId = roomId;
    this.battleId = battleId;
    this.state = state;
    setStateIsSet(true);
    this.teamMap = teamMap;
    this.battleStatus = battleStatus;
    setBattleStatusIsSet(true);
    this.roomStatus = roomStatus;
    setRoomStatusIsSet(true);
    this.resultFlag = resultFlag;
    setResultFlagIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RoomGameResultVO(RoomGameResultVO other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetRoomId()) {
      this.roomId = other.roomId;
    }
    if (other.isSetBattleId()) {
      this.battleId = other.battleId;
    }
    this.state = other.state;
    if (other.isSetTeamMap()) {
      Map<Integer,GameTeamVO> __this__teamMap = new HashMap<Integer,GameTeamVO>(other.teamMap.size());
      for (Map.Entry<Integer, GameTeamVO> other_element : other.teamMap.entrySet()) {

        Integer other_element_key = other_element.getKey();
        GameTeamVO other_element_value = other_element.getValue();

        Integer __this__teamMap_copy_key = other_element_key;

        GameTeamVO __this__teamMap_copy_value = other_element_value;

        __this__teamMap.put(__this__teamMap_copy_key, __this__teamMap_copy_value);
      }
      this.teamMap = __this__teamMap;
    }
    this.battleStatus = other.battleStatus;
    this.roomStatus = other.roomStatus;
    this.resultFlag = other.resultFlag;
  }

  public RoomGameResultVO deepCopy() {
    return new RoomGameResultVO(this);
  }

  @Override
  public void clear() {
    this.roomId = null;
    this.battleId = null;
    setStateIsSet(false);
    this.state = 0;
    this.teamMap = null;
    setBattleStatusIsSet(false);
    this.battleStatus = 0;
    setRoomStatusIsSet(false);
    this.roomStatus = 0;
    setResultFlagIsSet(false);
    this.resultFlag = 0;
  }

  public String getRoomId() {
    return this.roomId;
  }

  public RoomGameResultVO setRoomId(String roomId) {
    this.roomId = roomId;
    return this;
  }

  public void unsetRoomId() {
    this.roomId = null;
  }

  /** Returns true if field roomId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomId() {
    return this.roomId != null;
  }

  public void setRoomIdIsSet(boolean value) {
    if (!value) {
      this.roomId = null;
    }
  }

  public String getBattleId() {
    return this.battleId;
  }

  public RoomGameResultVO setBattleId(String battleId) {
    this.battleId = battleId;
    return this;
  }

  public void unsetBattleId() {
    this.battleId = null;
  }

  /** Returns true if field battleId is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleId() {
    return this.battleId != null;
  }

  public void setBattleIdIsSet(boolean value) {
    if (!value) {
      this.battleId = null;
    }
  }

  /**
   * 0:未开始 1:进行中 2:结束
   * 
   */
  public int getState() {
    return this.state;
  }

  /**
   * 0:未开始 1:进行中 2:结束
   * 
   */
  public RoomGameResultVO setState(int state) {
    this.state = state;
    setStateIsSet(true);
    return this;
  }

  public void unsetState() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATE_ISSET_ID);
  }

  /** Returns true if field state is set (has been assigned a value) and false otherwise */
  public boolean isSetState() {
    return EncodingUtils.testBit(__isset_bitfield, __STATE_ISSET_ID);
  }

  public void setStateIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATE_ISSET_ID, value);
  }

  public int getTeamMapSize() {
    return (this.teamMap == null) ? 0 : this.teamMap.size();
  }

  public void putToTeamMap(int key, GameTeamVO val) {
    if (this.teamMap == null) {
      this.teamMap = new HashMap<Integer,GameTeamVO>();
    }
    this.teamMap.put(key, val);
  }

  public Map<Integer,GameTeamVO> getTeamMap() {
    return this.teamMap;
  }

  public RoomGameResultVO setTeamMap(Map<Integer,GameTeamVO> teamMap) {
    this.teamMap = teamMap;
    return this;
  }

  public void unsetTeamMap() {
    this.teamMap = null;
  }

  /** Returns true if field teamMap is set (has been assigned a value) and false otherwise */
  public boolean isSetTeamMap() {
    return this.teamMap != null;
  }

  public void setTeamMapIsSet(boolean value) {
    if (!value) {
      this.teamMap = null;
    }
  }

  public int getBattleStatus() {
    return this.battleStatus;
  }

  public RoomGameResultVO setBattleStatus(int battleStatus) {
    this.battleStatus = battleStatus;
    setBattleStatusIsSet(true);
    return this;
  }

  public void unsetBattleStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BATTLESTATUS_ISSET_ID);
  }

  /** Returns true if field battleStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __BATTLESTATUS_ISSET_ID);
  }

  public void setBattleStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BATTLESTATUS_ISSET_ID, value);
  }

  public int getRoomStatus() {
    return this.roomStatus;
  }

  public RoomGameResultVO setRoomStatus(int roomStatus) {
    this.roomStatus = roomStatus;
    setRoomStatusIsSet(true);
    return this;
  }

  public void unsetRoomStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROOMSTATUS_ISSET_ID);
  }

  /** Returns true if field roomStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __ROOMSTATUS_ISSET_ID);
  }

  public void setRoomStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROOMSTATUS_ISSET_ID, value);
  }

  public int getResultFlag() {
    return this.resultFlag;
  }

  public RoomGameResultVO setResultFlag(int resultFlag) {
    this.resultFlag = resultFlag;
    setResultFlagIsSet(true);
    return this;
  }

  public void unsetResultFlag() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RESULTFLAG_ISSET_ID);
  }

  /** Returns true if field resultFlag is set (has been assigned a value) and false otherwise */
  public boolean isSetResultFlag() {
    return EncodingUtils.testBit(__isset_bitfield, __RESULTFLAG_ISSET_ID);
  }

  public void setResultFlagIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RESULTFLAG_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ROOM_ID:
      if (value == null) {
        unsetRoomId();
      } else {
        setRoomId((String)value);
      }
      break;

    case BATTLE_ID:
      if (value == null) {
        unsetBattleId();
      } else {
        setBattleId((String)value);
      }
      break;

    case STATE:
      if (value == null) {
        unsetState();
      } else {
        setState((Integer)value);
      }
      break;

    case TEAM_MAP:
      if (value == null) {
        unsetTeamMap();
      } else {
        setTeamMap((Map<Integer,GameTeamVO>)value);
      }
      break;

    case BATTLE_STATUS:
      if (value == null) {
        unsetBattleStatus();
      } else {
        setBattleStatus((Integer)value);
      }
      break;

    case ROOM_STATUS:
      if (value == null) {
        unsetRoomStatus();
      } else {
        setRoomStatus((Integer)value);
      }
      break;

    case RESULT_FLAG:
      if (value == null) {
        unsetResultFlag();
      } else {
        setResultFlag((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ROOM_ID:
      return getRoomId();

    case BATTLE_ID:
      return getBattleId();

    case STATE:
      return getState();

    case TEAM_MAP:
      return getTeamMap();

    case BATTLE_STATUS:
      return getBattleStatus();

    case ROOM_STATUS:
      return getRoomStatus();

    case RESULT_FLAG:
      return getResultFlag();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ROOM_ID:
      return isSetRoomId();
    case BATTLE_ID:
      return isSetBattleId();
    case STATE:
      return isSetState();
    case TEAM_MAP:
      return isSetTeamMap();
    case BATTLE_STATUS:
      return isSetBattleStatus();
    case ROOM_STATUS:
      return isSetRoomStatus();
    case RESULT_FLAG:
      return isSetResultFlag();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RoomGameResultVO)
      return this.equals((RoomGameResultVO)that);
    return false;
  }

  public boolean equals(RoomGameResultVO that) {
    if (that == null)
      return false;

    boolean this_present_roomId = true && this.isSetRoomId();
    boolean that_present_roomId = true && that.isSetRoomId();
    if (this_present_roomId || that_present_roomId) {
      if (!(this_present_roomId && that_present_roomId))
        return false;
      if (!this.roomId.equals(that.roomId))
        return false;
    }

    boolean this_present_battleId = true && this.isSetBattleId();
    boolean that_present_battleId = true && that.isSetBattleId();
    if (this_present_battleId || that_present_battleId) {
      if (!(this_present_battleId && that_present_battleId))
        return false;
      if (!this.battleId.equals(that.battleId))
        return false;
    }

    boolean this_present_state = true;
    boolean that_present_state = true;
    if (this_present_state || that_present_state) {
      if (!(this_present_state && that_present_state))
        return false;
      if (this.state != that.state)
        return false;
    }

    boolean this_present_teamMap = true && this.isSetTeamMap();
    boolean that_present_teamMap = true && that.isSetTeamMap();
    if (this_present_teamMap || that_present_teamMap) {
      if (!(this_present_teamMap && that_present_teamMap))
        return false;
      if (!this.teamMap.equals(that.teamMap))
        return false;
    }

    boolean this_present_battleStatus = true;
    boolean that_present_battleStatus = true;
    if (this_present_battleStatus || that_present_battleStatus) {
      if (!(this_present_battleStatus && that_present_battleStatus))
        return false;
      if (this.battleStatus != that.battleStatus)
        return false;
    }

    boolean this_present_roomStatus = true;
    boolean that_present_roomStatus = true;
    if (this_present_roomStatus || that_present_roomStatus) {
      if (!(this_present_roomStatus && that_present_roomStatus))
        return false;
      if (this.roomStatus != that.roomStatus)
        return false;
    }

    boolean this_present_resultFlag = true;
    boolean that_present_resultFlag = true;
    if (this_present_resultFlag || that_present_resultFlag) {
      if (!(this_present_resultFlag && that_present_resultFlag))
        return false;
      if (this.resultFlag != that.resultFlag)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_roomId = true && (isSetRoomId());
    list.add(present_roomId);
    if (present_roomId)
      list.add(roomId);

    boolean present_battleId = true && (isSetBattleId());
    list.add(present_battleId);
    if (present_battleId)
      list.add(battleId);

    boolean present_state = true;
    list.add(present_state);
    if (present_state)
      list.add(state);

    boolean present_teamMap = true && (isSetTeamMap());
    list.add(present_teamMap);
    if (present_teamMap)
      list.add(teamMap);

    boolean present_battleStatus = true;
    list.add(present_battleStatus);
    if (present_battleStatus)
      list.add(battleStatus);

    boolean present_roomStatus = true;
    list.add(present_roomStatus);
    if (present_roomStatus)
      list.add(roomStatus);

    boolean present_resultFlag = true;
    list.add(present_resultFlag);
    if (present_resultFlag)
      list.add(resultFlag);

    return list.hashCode();
  }

  @Override
  public int compareTo(RoomGameResultVO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRoomId()).compareTo(other.isSetRoomId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomId, other.roomId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBattleId()).compareTo(other.isSetBattleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleId, other.battleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetState()).compareTo(other.isSetState());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetState()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.state, other.state);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTeamMap()).compareTo(other.isSetTeamMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTeamMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.teamMap, other.teamMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBattleStatus()).compareTo(other.isSetBattleStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleStatus, other.battleStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoomStatus()).compareTo(other.isSetRoomStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomStatus, other.roomStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetResultFlag()).compareTo(other.isSetResultFlag());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResultFlag()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.resultFlag, other.resultFlag);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RoomGameResultVO(");
    boolean first = true;

    sb.append("roomId:");
    if (this.roomId == null) {
      sb.append("null");
    } else {
      sb.append(this.roomId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("battleId:");
    if (this.battleId == null) {
      sb.append("null");
    } else {
      sb.append(this.battleId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("state:");
    sb.append(this.state);
    first = false;
    if (!first) sb.append(", ");
    sb.append("teamMap:");
    if (this.teamMap == null) {
      sb.append("null");
    } else {
      sb.append(this.teamMap);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("battleStatus:");
    sb.append(this.battleStatus);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roomStatus:");
    sb.append(this.roomStatus);
    first = false;
    if (!first) sb.append(", ");
    sb.append("resultFlag:");
    sb.append(this.resultFlag);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RoomGameResultVOStandardSchemeFactory implements SchemeFactory {
    public RoomGameResultVOStandardScheme getScheme() {
      return new RoomGameResultVOStandardScheme();
    }
  }

  private static class RoomGameResultVOStandardScheme extends StandardScheme<RoomGameResultVO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RoomGameResultVO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROOM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.roomId = iprot.readString();
              struct.setRoomIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BATTLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.battleId = iprot.readString();
              struct.setBattleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // STATE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.state = iprot.readI32();
              struct.setStateIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TEAM_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map24 = iprot.readMapBegin();
                struct.teamMap = new HashMap<Integer,GameTeamVO>(2*_map24.size);
                int _key25;
                GameTeamVO _val26;
                for (int _i27 = 0; _i27 < _map24.size; ++_i27)
                {
                  _key25 = iprot.readI32();
                  _val26 = new GameTeamVO();
                  _val26.read(iprot);
                  struct.teamMap.put(_key25, _val26);
                }
                iprot.readMapEnd();
              }
              struct.setTeamMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // BATTLE_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.battleStatus = iprot.readI32();
              struct.setBattleStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // ROOM_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.roomStatus = iprot.readI32();
              struct.setRoomStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // RESULT_FLAG
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.resultFlag = iprot.readI32();
              struct.setResultFlagIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RoomGameResultVO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.roomId != null) {
        oprot.writeFieldBegin(ROOM_ID_FIELD_DESC);
        oprot.writeString(struct.roomId);
        oprot.writeFieldEnd();
      }
      if (struct.battleId != null) {
        oprot.writeFieldBegin(BATTLE_ID_FIELD_DESC);
        oprot.writeString(struct.battleId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(STATE_FIELD_DESC);
      oprot.writeI32(struct.state);
      oprot.writeFieldEnd();
      if (struct.teamMap != null) {
        oprot.writeFieldBegin(TEAM_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.STRUCT, struct.teamMap.size()));
          for (Map.Entry<Integer, GameTeamVO> _iter28 : struct.teamMap.entrySet())
          {
            oprot.writeI32(_iter28.getKey());
            _iter28.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BATTLE_STATUS_FIELD_DESC);
      oprot.writeI32(struct.battleStatus);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROOM_STATUS_FIELD_DESC);
      oprot.writeI32(struct.roomStatus);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RESULT_FLAG_FIELD_DESC);
      oprot.writeI32(struct.resultFlag);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RoomGameResultVOTupleSchemeFactory implements SchemeFactory {
    public RoomGameResultVOTupleScheme getScheme() {
      return new RoomGameResultVOTupleScheme();
    }
  }

  private static class RoomGameResultVOTupleScheme extends TupleScheme<RoomGameResultVO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RoomGameResultVO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRoomId()) {
        optionals.set(0);
      }
      if (struct.isSetBattleId()) {
        optionals.set(1);
      }
      if (struct.isSetState()) {
        optionals.set(2);
      }
      if (struct.isSetTeamMap()) {
        optionals.set(3);
      }
      if (struct.isSetBattleStatus()) {
        optionals.set(4);
      }
      if (struct.isSetRoomStatus()) {
        optionals.set(5);
      }
      if (struct.isSetResultFlag()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetRoomId()) {
        oprot.writeString(struct.roomId);
      }
      if (struct.isSetBattleId()) {
        oprot.writeString(struct.battleId);
      }
      if (struct.isSetState()) {
        oprot.writeI32(struct.state);
      }
      if (struct.isSetTeamMap()) {
        {
          oprot.writeI32(struct.teamMap.size());
          for (Map.Entry<Integer, GameTeamVO> _iter29 : struct.teamMap.entrySet())
          {
            oprot.writeI32(_iter29.getKey());
            _iter29.getValue().write(oprot);
          }
        }
      }
      if (struct.isSetBattleStatus()) {
        oprot.writeI32(struct.battleStatus);
      }
      if (struct.isSetRoomStatus()) {
        oprot.writeI32(struct.roomStatus);
      }
      if (struct.isSetResultFlag()) {
        oprot.writeI32(struct.resultFlag);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RoomGameResultVO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.roomId = iprot.readString();
        struct.setRoomIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.battleId = iprot.readString();
        struct.setBattleIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.state = iprot.readI32();
        struct.setStateIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TMap _map30 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.teamMap = new HashMap<Integer,GameTeamVO>(2*_map30.size);
          int _key31;
          GameTeamVO _val32;
          for (int _i33 = 0; _i33 < _map30.size; ++_i33)
          {
            _key31 = iprot.readI32();
            _val32 = new GameTeamVO();
            _val32.read(iprot);
            struct.teamMap.put(_key31, _val32);
          }
        }
        struct.setTeamMapIsSet(true);
      }
      if (incoming.get(4)) {
        struct.battleStatus = iprot.readI32();
        struct.setBattleStatusIsSet(true);
      }
      if (incoming.get(5)) {
        struct.roomStatus = iprot.readI32();
        struct.setRoomStatusIsSet(true);
      }
      if (incoming.get(6)) {
        struct.resultFlag = iprot.readI32();
        struct.setResultFlagIsSet(true);
      }
    }
  }

}

