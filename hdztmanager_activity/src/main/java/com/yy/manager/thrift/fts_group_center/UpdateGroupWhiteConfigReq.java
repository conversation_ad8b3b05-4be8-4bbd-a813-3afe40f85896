/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.fts_group_center;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2025-05-24")
public class UpdateGroupWhiteConfigReq implements org.apache.thrift.TBase<UpdateGroupWhiteConfigReq, UpdateGroupWhiteConfigReq._Fields>, java.io.Serializable, Cloneable, Comparable<UpdateGroupWhiteConfigReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("UpdateGroupWhiteConfigReq");

  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField BUSINESS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("businessId", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleId", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField VISIBLE_FIELD_DESC = new org.apache.thrift.protocol.TField("visible", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField CONFIG_FIELD_DESC = new org.apache.thrift.protocol.TField("config", org.apache.thrift.protocol.TType.LIST, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new UpdateGroupWhiteConfigReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new UpdateGroupWhiteConfigReqTupleSchemeFactory());
  }

  public long uid; // required
  public String actId; // required
  public String businessId; // required
  public String roleId; // required
  /**
   * 
   * @see Visibility
   */
  public Visibility visible; // required
  public List<GroupWhiteConfig> config; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UID((short)1, "uid"),
    ACT_ID((short)2, "actId"),
    BUSINESS_ID((short)3, "businessId"),
    ROLE_ID((short)4, "roleId"),
    /**
     * 
     * @see Visibility
     */
    VISIBLE((short)5, "visible"),
    CONFIG((short)6, "config");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UID
          return UID;
        case 2: // ACT_ID
          return ACT_ID;
        case 3: // BUSINESS_ID
          return BUSINESS_ID;
        case 4: // ROLE_ID
          return ROLE_ID;
        case 5: // VISIBLE
          return VISIBLE;
        case 6: // CONFIG
          return CONFIG;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __UID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_ID, new org.apache.thrift.meta_data.FieldMetaData("businessId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("roleId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.VISIBLE, new org.apache.thrift.meta_data.FieldMetaData("visible", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, Visibility.class)));
    tmpMap.put(_Fields.CONFIG, new org.apache.thrift.meta_data.FieldMetaData("config", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GroupWhiteConfig.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(UpdateGroupWhiteConfigReq.class, metaDataMap);
  }

  public UpdateGroupWhiteConfigReq() {
  }

  public UpdateGroupWhiteConfigReq(
    long uid,
    String actId,
    String businessId,
    String roleId,
    Visibility visible,
    List<GroupWhiteConfig> config)
  {
    this();
    this.uid = uid;
    setUidIsSet(true);
    this.actId = actId;
    this.businessId = businessId;
    this.roleId = roleId;
    this.visible = visible;
    this.config = config;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public UpdateGroupWhiteConfigReq(UpdateGroupWhiteConfigReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.uid = other.uid;
    if (other.isSetActId()) {
      this.actId = other.actId;
    }
    if (other.isSetBusinessId()) {
      this.businessId = other.businessId;
    }
    if (other.isSetRoleId()) {
      this.roleId = other.roleId;
    }
    if (other.isSetVisible()) {
      this.visible = other.visible;
    }
    if (other.isSetConfig()) {
      List<GroupWhiteConfig> __this__config = new ArrayList<GroupWhiteConfig>(other.config.size());
      for (GroupWhiteConfig other_element : other.config) {
        __this__config.add(new GroupWhiteConfig(other_element));
      }
      this.config = __this__config;
    }
  }

  public UpdateGroupWhiteConfigReq deepCopy() {
    return new UpdateGroupWhiteConfigReq(this);
  }

  @Override
  public void clear() {
    setUidIsSet(false);
    this.uid = 0;
    this.actId = null;
    this.businessId = null;
    this.roleId = null;
    this.visible = null;
    this.config = null;
  }

  public long getUid() {
    return this.uid;
  }

  public UpdateGroupWhiteConfigReq setUid(long uid) {
    this.uid = uid;
    setUidIsSet(true);
    return this;
  }

  public void unsetUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UID_ISSET_ID);
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return EncodingUtils.testBit(__isset_bitfield, __UID_ISSET_ID);
  }

  public void setUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UID_ISSET_ID, value);
  }

  public String getActId() {
    return this.actId;
  }

  public UpdateGroupWhiteConfigReq setActId(String actId) {
    this.actId = actId;
    return this;
  }

  public void unsetActId() {
    this.actId = null;
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return this.actId != null;
  }

  public void setActIdIsSet(boolean value) {
    if (!value) {
      this.actId = null;
    }
  }

  public String getBusinessId() {
    return this.businessId;
  }

  public UpdateGroupWhiteConfigReq setBusinessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

  public void unsetBusinessId() {
    this.businessId = null;
  }

  /** Returns true if field businessId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessId() {
    return this.businessId != null;
  }

  public void setBusinessIdIsSet(boolean value) {
    if (!value) {
      this.businessId = null;
    }
  }

  public String getRoleId() {
    return this.roleId;
  }

  public UpdateGroupWhiteConfigReq setRoleId(String roleId) {
    this.roleId = roleId;
    return this;
  }

  public void unsetRoleId() {
    this.roleId = null;
  }

  /** Returns true if field roleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleId() {
    return this.roleId != null;
  }

  public void setRoleIdIsSet(boolean value) {
    if (!value) {
      this.roleId = null;
    }
  }

  /**
   * 
   * @see Visibility
   */
  public Visibility getVisible() {
    return this.visible;
  }

  /**
   * 
   * @see Visibility
   */
  public UpdateGroupWhiteConfigReq setVisible(Visibility visible) {
    this.visible = visible;
    return this;
  }

  public void unsetVisible() {
    this.visible = null;
  }

  /** Returns true if field visible is set (has been assigned a value) and false otherwise */
  public boolean isSetVisible() {
    return this.visible != null;
  }

  public void setVisibleIsSet(boolean value) {
    if (!value) {
      this.visible = null;
    }
  }

  public int getConfigSize() {
    return (this.config == null) ? 0 : this.config.size();
  }

  public java.util.Iterator<GroupWhiteConfig> getConfigIterator() {
    return (this.config == null) ? null : this.config.iterator();
  }

  public void addToConfig(GroupWhiteConfig elem) {
    if (this.config == null) {
      this.config = new ArrayList<GroupWhiteConfig>();
    }
    this.config.add(elem);
  }

  public List<GroupWhiteConfig> getConfig() {
    return this.config;
  }

  public UpdateGroupWhiteConfigReq setConfig(List<GroupWhiteConfig> config) {
    this.config = config;
    return this;
  }

  public void unsetConfig() {
    this.config = null;
  }

  /** Returns true if field config is set (has been assigned a value) and false otherwise */
  public boolean isSetConfig() {
    return this.config != null;
  }

  public void setConfigIsSet(boolean value) {
    if (!value) {
      this.config = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((Long)value);
      }
      break;

    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((String)value);
      }
      break;

    case BUSINESS_ID:
      if (value == null) {
        unsetBusinessId();
      } else {
        setBusinessId((String)value);
      }
      break;

    case ROLE_ID:
      if (value == null) {
        unsetRoleId();
      } else {
        setRoleId((String)value);
      }
      break;

    case VISIBLE:
      if (value == null) {
        unsetVisible();
      } else {
        setVisible((Visibility)value);
      }
      break;

    case CONFIG:
      if (value == null) {
        unsetConfig();
      } else {
        setConfig((List<GroupWhiteConfig>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case UID:
      return getUid();

    case ACT_ID:
      return getActId();

    case BUSINESS_ID:
      return getBusinessId();

    case ROLE_ID:
      return getRoleId();

    case VISIBLE:
      return getVisible();

    case CONFIG:
      return getConfig();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case UID:
      return isSetUid();
    case ACT_ID:
      return isSetActId();
    case BUSINESS_ID:
      return isSetBusinessId();
    case ROLE_ID:
      return isSetRoleId();
    case VISIBLE:
      return isSetVisible();
    case CONFIG:
      return isSetConfig();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof UpdateGroupWhiteConfigReq)
      return this.equals((UpdateGroupWhiteConfigReq)that);
    return false;
  }

  public boolean equals(UpdateGroupWhiteConfigReq that) {
    if (that == null)
      return false;

    boolean this_present_uid = true;
    boolean that_present_uid = true;
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (this.uid != that.uid)
        return false;
    }

    boolean this_present_actId = true && this.isSetActId();
    boolean that_present_actId = true && that.isSetActId();
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (!this.actId.equals(that.actId))
        return false;
    }

    boolean this_present_businessId = true && this.isSetBusinessId();
    boolean that_present_businessId = true && that.isSetBusinessId();
    if (this_present_businessId || that_present_businessId) {
      if (!(this_present_businessId && that_present_businessId))
        return false;
      if (!this.businessId.equals(that.businessId))
        return false;
    }

    boolean this_present_roleId = true && this.isSetRoleId();
    boolean that_present_roleId = true && that.isSetRoleId();
    if (this_present_roleId || that_present_roleId) {
      if (!(this_present_roleId && that_present_roleId))
        return false;
      if (!this.roleId.equals(that.roleId))
        return false;
    }

    boolean this_present_visible = true && this.isSetVisible();
    boolean that_present_visible = true && that.isSetVisible();
    if (this_present_visible || that_present_visible) {
      if (!(this_present_visible && that_present_visible))
        return false;
      if (!this.visible.equals(that.visible))
        return false;
    }

    boolean this_present_config = true && this.isSetConfig();
    boolean that_present_config = true && that.isSetConfig();
    if (this_present_config || that_present_config) {
      if (!(this_present_config && that_present_config))
        return false;
      if (!this.config.equals(that.config))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_uid = true;
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    boolean present_actId = true && (isSetActId());
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_businessId = true && (isSetBusinessId());
    list.add(present_businessId);
    if (present_businessId)
      list.add(businessId);

    boolean present_roleId = true && (isSetRoleId());
    list.add(present_roleId);
    if (present_roleId)
      list.add(roleId);

    boolean present_visible = true && (isSetVisible());
    list.add(present_visible);
    if (present_visible)
      list.add(visible.getValue());

    boolean present_config = true && (isSetConfig());
    list.add(present_config);
    if (present_config)
      list.add(config);

    return list.hashCode();
  }

  @Override
  public int compareTo(UpdateGroupWhiteConfigReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessId()).compareTo(other.isSetBusinessId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessId, other.businessId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleId()).compareTo(other.isSetRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleId, other.roleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetVisible()).compareTo(other.isSetVisible());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVisible()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.visible, other.visible);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetConfig()).compareTo(other.isSetConfig());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConfig()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.config, other.config);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("UpdateGroupWhiteConfigReq(");
    boolean first = true;

    sb.append("uid:");
    sb.append(this.uid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actId:");
    if (this.actId == null) {
      sb.append("null");
    } else {
      sb.append(this.actId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessId:");
    if (this.businessId == null) {
      sb.append("null");
    } else {
      sb.append(this.businessId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleId:");
    if (this.roleId == null) {
      sb.append("null");
    } else {
      sb.append(this.roleId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("visible:");
    if (this.visible == null) {
      sb.append("null");
    } else {
      sb.append(this.visible);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("config:");
    if (this.config == null) {
      sb.append("null");
    } else {
      sb.append(this.config);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class UpdateGroupWhiteConfigReqStandardSchemeFactory implements SchemeFactory {
    public UpdateGroupWhiteConfigReqStandardScheme getScheme() {
      return new UpdateGroupWhiteConfigReqStandardScheme();
    }
  }

  private static class UpdateGroupWhiteConfigReqStandardScheme extends StandardScheme<UpdateGroupWhiteConfigReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, UpdateGroupWhiteConfigReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.uid = iprot.readI64();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.actId = iprot.readString();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BUSINESS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessId = iprot.readString();
              struct.setBusinessIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.roleId = iprot.readString();
              struct.setRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // VISIBLE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.visible = com.yy.manager.thrift.fts_group_center.Visibility.findByValue(iprot.readI32());
              struct.setVisibleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // CONFIG
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list112 = iprot.readListBegin();
                struct.config = new ArrayList<GroupWhiteConfig>(_list112.size);
                GroupWhiteConfig _elem113;
                for (int _i114 = 0; _i114 < _list112.size; ++_i114)
                {
                  _elem113 = new GroupWhiteConfig();
                  _elem113.read(iprot);
                  struct.config.add(_elem113);
                }
                iprot.readListEnd();
              }
              struct.setConfigIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, UpdateGroupWhiteConfigReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(UID_FIELD_DESC);
      oprot.writeI64(struct.uid);
      oprot.writeFieldEnd();
      if (struct.actId != null) {
        oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
        oprot.writeString(struct.actId);
        oprot.writeFieldEnd();
      }
      if (struct.businessId != null) {
        oprot.writeFieldBegin(BUSINESS_ID_FIELD_DESC);
        oprot.writeString(struct.businessId);
        oprot.writeFieldEnd();
      }
      if (struct.roleId != null) {
        oprot.writeFieldBegin(ROLE_ID_FIELD_DESC);
        oprot.writeString(struct.roleId);
        oprot.writeFieldEnd();
      }
      if (struct.visible != null) {
        oprot.writeFieldBegin(VISIBLE_FIELD_DESC);
        oprot.writeI32(struct.visible.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.config != null) {
        oprot.writeFieldBegin(CONFIG_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.config.size()));
          for (GroupWhiteConfig _iter115 : struct.config)
          {
            _iter115.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class UpdateGroupWhiteConfigReqTupleSchemeFactory implements SchemeFactory {
    public UpdateGroupWhiteConfigReqTupleScheme getScheme() {
      return new UpdateGroupWhiteConfigReqTupleScheme();
    }
  }

  private static class UpdateGroupWhiteConfigReqTupleScheme extends TupleScheme<UpdateGroupWhiteConfigReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, UpdateGroupWhiteConfigReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetUid()) {
        optionals.set(0);
      }
      if (struct.isSetActId()) {
        optionals.set(1);
      }
      if (struct.isSetBusinessId()) {
        optionals.set(2);
      }
      if (struct.isSetRoleId()) {
        optionals.set(3);
      }
      if (struct.isSetVisible()) {
        optionals.set(4);
      }
      if (struct.isSetConfig()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetUid()) {
        oprot.writeI64(struct.uid);
      }
      if (struct.isSetActId()) {
        oprot.writeString(struct.actId);
      }
      if (struct.isSetBusinessId()) {
        oprot.writeString(struct.businessId);
      }
      if (struct.isSetRoleId()) {
        oprot.writeString(struct.roleId);
      }
      if (struct.isSetVisible()) {
        oprot.writeI32(struct.visible.getValue());
      }
      if (struct.isSetConfig()) {
        {
          oprot.writeI32(struct.config.size());
          for (GroupWhiteConfig _iter116 : struct.config)
          {
            _iter116.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, UpdateGroupWhiteConfigReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.uid = iprot.readI64();
        struct.setUidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.actId = iprot.readString();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.businessId = iprot.readString();
        struct.setBusinessIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.roleId = iprot.readString();
        struct.setRoleIdIsSet(true);
      }
      if (incoming.get(4)) {
        struct.visible = com.yy.manager.thrift.fts_group_center.Visibility.findByValue(iprot.readI32());
        struct.setVisibleIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TList _list117 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.config = new ArrayList<GroupWhiteConfig>(_list117.size);
          GroupWhiteConfig _elem118;
          for (int _i119 = 0; _i119 < _list117.size; ++_i119)
          {
            _elem118 = new GroupWhiteConfig();
            _elem118.read(iprot);
            struct.config.add(_elem118);
          }
        }
        struct.setConfigIsSet(true);
      }
    }
  }

}

