/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class SubscribeReq implements org.apache.thrift.TBase<SubscribeReq, SubscribeReq._Fields>, java.io.Serializable, Cloneable, Comparable<SubscribeReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SubscribeReq");

  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField COMPERE_UIDS_FIELD_DESC = new org.apache.thrift.protocol.TField("compere_uids", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SubscribeReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SubscribeReqTupleSchemeFactory());
  }

  public long uid; // required
  public List<Long> compere_uids; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UID((short)1, "uid"),
    COMPERE_UIDS((short)2, "compere_uids");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UID
          return UID;
        case 2: // COMPERE_UIDS
          return COMPERE_UIDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __UID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COMPERE_UIDS, new org.apache.thrift.meta_data.FieldMetaData("compere_uids", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SubscribeReq.class, metaDataMap);
  }

  public SubscribeReq() {
    this.uid = 1L;

    this.compere_uids = new ArrayList<Long>();

  }

  public SubscribeReq(
    long uid,
    List<Long> compere_uids)
  {
    this();
    this.uid = uid;
    setUidIsSet(true);
    this.compere_uids = compere_uids;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SubscribeReq(SubscribeReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.uid = other.uid;
    if (other.isSetCompere_uids()) {
      List<Long> __this__compere_uids = new ArrayList<Long>(other.compere_uids);
      this.compere_uids = __this__compere_uids;
    }
  }

  public SubscribeReq deepCopy() {
    return new SubscribeReq(this);
  }

  @Override
  public void clear() {
    this.uid = 1L;

    this.compere_uids = new ArrayList<Long>();

  }

  public long getUid() {
    return this.uid;
  }

  public SubscribeReq setUid(long uid) {
    this.uid = uid;
    setUidIsSet(true);
    return this;
  }

  public void unsetUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UID_ISSET_ID);
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return EncodingUtils.testBit(__isset_bitfield, __UID_ISSET_ID);
  }

  public void setUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UID_ISSET_ID, value);
  }

  public int getCompere_uidsSize() {
    return (this.compere_uids == null) ? 0 : this.compere_uids.size();
  }

  public java.util.Iterator<Long> getCompere_uidsIterator() {
    return (this.compere_uids == null) ? null : this.compere_uids.iterator();
  }

  public void addToCompere_uids(long elem) {
    if (this.compere_uids == null) {
      this.compere_uids = new ArrayList<Long>();
    }
    this.compere_uids.add(elem);
  }

  public List<Long> getCompere_uids() {
    return this.compere_uids;
  }

  public SubscribeReq setCompere_uids(List<Long> compere_uids) {
    this.compere_uids = compere_uids;
    return this;
  }

  public void unsetCompere_uids() {
    this.compere_uids = null;
  }

  /** Returns true if field compere_uids is set (has been assigned a value) and false otherwise */
  public boolean isSetCompere_uids() {
    return this.compere_uids != null;
  }

  public void setCompere_uidsIsSet(boolean value) {
    if (!value) {
      this.compere_uids = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((Long)value);
      }
      break;

    case COMPERE_UIDS:
      if (value == null) {
        unsetCompere_uids();
      } else {
        setCompere_uids((List<Long>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case UID:
      return getUid();

    case COMPERE_UIDS:
      return getCompere_uids();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case UID:
      return isSetUid();
    case COMPERE_UIDS:
      return isSetCompere_uids();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SubscribeReq)
      return this.equals((SubscribeReq)that);
    return false;
  }

  public boolean equals(SubscribeReq that) {
    if (that == null)
      return false;

    boolean this_present_uid = true;
    boolean that_present_uid = true;
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (this.uid != that.uid)
        return false;
    }

    boolean this_present_compere_uids = true && this.isSetCompere_uids();
    boolean that_present_compere_uids = true && that.isSetCompere_uids();
    if (this_present_compere_uids || that_present_compere_uids) {
      if (!(this_present_compere_uids && that_present_compere_uids))
        return false;
      if (!this.compere_uids.equals(that.compere_uids))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_uid = true;
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    boolean present_compere_uids = true && (isSetCompere_uids());
    list.add(present_compere_uids);
    if (present_compere_uids)
      list.add(compere_uids);

    return list.hashCode();
  }

  @Override
  public int compareTo(SubscribeReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCompere_uids()).compareTo(other.isSetCompere_uids());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCompere_uids()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.compere_uids, other.compere_uids);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SubscribeReq(");
    boolean first = true;

    sb.append("uid:");
    sb.append(this.uid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("compere_uids:");
    if (this.compere_uids == null) {
      sb.append("null");
    } else {
      sb.append(this.compere_uids);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SubscribeReqStandardSchemeFactory implements SchemeFactory {
    public SubscribeReqStandardScheme getScheme() {
      return new SubscribeReqStandardScheme();
    }
  }

  private static class SubscribeReqStandardScheme extends StandardScheme<SubscribeReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SubscribeReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.uid = iprot.readI64();
              struct.setUidIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // COMPERE_UIDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list96 = iprot.readListBegin();
                struct.compere_uids = new ArrayList<Long>(_list96.size);
                long _elem97;
                for (int _i98 = 0; _i98 < _list96.size; ++_i98)
                {
                  _elem97 = iprot.readI64();
                  struct.compere_uids.add(_elem97);
                }
                iprot.readListEnd();
              }
              struct.setCompere_uidsIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SubscribeReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(UID_FIELD_DESC);
      oprot.writeI64(struct.uid);
      oprot.writeFieldEnd();
      if (struct.compere_uids != null) {
        oprot.writeFieldBegin(COMPERE_UIDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.compere_uids.size()));
          for (long _iter99 : struct.compere_uids)
          {
            oprot.writeI64(_iter99);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SubscribeReqTupleSchemeFactory implements SchemeFactory {
    public SubscribeReqTupleScheme getScheme() {
      return new SubscribeReqTupleScheme();
    }
  }

  private static class SubscribeReqTupleScheme extends TupleScheme<SubscribeReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SubscribeReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetUid()) {
        optionals.set(0);
      }
      if (struct.isSetCompere_uids()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetUid()) {
        oprot.writeI64(struct.uid);
      }
      if (struct.isSetCompere_uids()) {
        {
          oprot.writeI32(struct.compere_uids.size());
          for (long _iter100 : struct.compere_uids)
          {
            oprot.writeI64(_iter100);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SubscribeReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.uid = iprot.readI64();
        struct.setUidIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list101 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.compere_uids = new ArrayList<Long>(_list101.size);
          long _elem102;
          for (int _i103 = 0; _i103 < _list101.size; ++_i103)
          {
            _elem102 = iprot.readI64();
            struct.compere_uids.add(_elem102);
          }
        }
        struct.setCompere_uidsIsSet(true);
      }
    }
  }

}

