/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 通过顶级频道id和父频道id查询所有子频道信息
 * @param     appkey          客户端的标识
 * @param     tid             顶级频道id
 * @param     pid             父频道id
 * @param     recursive       是否需要递归查询
 * @param     columns         需要查询的字段集合，可以查询如下字段
 *                              - "sid": 频道id
 *                              - "pid": 父频道id
 *                              - "tid": 顶级频道id
 *                              - "name": 频道名字
 *                              - "passwd": 频道密码
 *                              - "create_time": 频道创建时间
 *                              - "style", 麦序模式
 *                              - "microtime", 麦序时间
 *                              - "is_limit_txt", is_limit_txt
 *                              - "txt_limittime", txt_limittime
 *                              - "sort", 排序序号
 *                              - "charge", 收费频道类型
 *                              - "template_id", 频道模板
 * @SaResponseSet               子频道信息结果集，如果没有查到结果则dataSet为空
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class SaRequestRecursess implements org.apache.thrift.TBase<SaRequestRecursess, SaRequestRecursess._Fields>, java.io.Serializable, Cloneable, Comparable<SaRequestRecursess> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaRequestRecursess");

  private static final org.apache.thrift.protocol.TField AUTH_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authMsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPKEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appkey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField TID_FIELD_DESC = new org.apache.thrift.protocol.TField("tid", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField PID_FIELD_DESC = new org.apache.thrift.protocol.TField("pid", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField RECURSIVE_FIELD_DESC = new org.apache.thrift.protocol.TField("recursive", org.apache.thrift.protocol.TType.BOOL, (short)5);
  private static final org.apache.thrift.protocol.TField COLUMNS_FIELD_DESC = new org.apache.thrift.protocol.TField("columns", org.apache.thrift.protocol.TType.LIST, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaRequestRecursessStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaRequestRecursessTupleSchemeFactory());
  }

  public AuthorizeMsg authMsg; // required
  public String appkey; // required
  public String tid; // required
  public String pid; // required
  public boolean recursive; // required
  public List<String> columns; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_MSG((short)1, "authMsg"),
    APPKEY((short)2, "appkey"),
    TID((short)3, "tid"),
    PID((short)4, "pid"),
    RECURSIVE((short)5, "recursive"),
    COLUMNS((short)6, "columns");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_MSG
          return AUTH_MSG;
        case 2: // APPKEY
          return APPKEY;
        case 3: // TID
          return TID;
        case 4: // PID
          return PID;
        case 5: // RECURSIVE
          return RECURSIVE;
        case 6: // COLUMNS
          return COLUMNS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RECURSIVE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_MSG, new org.apache.thrift.meta_data.FieldMetaData("authMsg", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPKEY, new org.apache.thrift.meta_data.FieldMetaData("appkey", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TID, new org.apache.thrift.meta_data.FieldMetaData("tid", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PID, new org.apache.thrift.meta_data.FieldMetaData("pid", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RECURSIVE, new org.apache.thrift.meta_data.FieldMetaData("recursive", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.COLUMNS, new org.apache.thrift.meta_data.FieldMetaData("columns", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaRequestRecursess.class, metaDataMap);
  }

  public SaRequestRecursess() {
  }

  public SaRequestRecursess(
    AuthorizeMsg authMsg,
    String appkey,
    String tid,
    String pid,
    boolean recursive,
    List<String> columns)
  {
    this();
    this.authMsg = authMsg;
    this.appkey = appkey;
    this.tid = tid;
    this.pid = pid;
    this.recursive = recursive;
    setRecursiveIsSet(true);
    this.columns = columns;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaRequestRecursess(SaRequestRecursess other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAuthMsg()) {
      this.authMsg = new AuthorizeMsg(other.authMsg);
    }
    if (other.isSetAppkey()) {
      this.appkey = other.appkey;
    }
    if (other.isSetTid()) {
      this.tid = other.tid;
    }
    if (other.isSetPid()) {
      this.pid = other.pid;
    }
    this.recursive = other.recursive;
    if (other.isSetColumns()) {
      List<String> __this__columns = new ArrayList<String>(other.columns);
      this.columns = __this__columns;
    }
  }

  public SaRequestRecursess deepCopy() {
    return new SaRequestRecursess(this);
  }

  @Override
  public void clear() {
    this.authMsg = null;
    this.appkey = null;
    this.tid = null;
    this.pid = null;
    setRecursiveIsSet(false);
    this.recursive = false;
    this.columns = null;
  }

  public AuthorizeMsg getAuthMsg() {
    return this.authMsg;
  }

  public SaRequestRecursess setAuthMsg(AuthorizeMsg authMsg) {
    this.authMsg = authMsg;
    return this;
  }

  public void unsetAuthMsg() {
    this.authMsg = null;
  }

  /** Returns true if field authMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthMsg() {
    return this.authMsg != null;
  }

  public void setAuthMsgIsSet(boolean value) {
    if (!value) {
      this.authMsg = null;
    }
  }

  public String getAppkey() {
    return this.appkey;
  }

  public SaRequestRecursess setAppkey(String appkey) {
    this.appkey = appkey;
    return this;
  }

  public void unsetAppkey() {
    this.appkey = null;
  }

  /** Returns true if field appkey is set (has been assigned a value) and false otherwise */
  public boolean isSetAppkey() {
    return this.appkey != null;
  }

  public void setAppkeyIsSet(boolean value) {
    if (!value) {
      this.appkey = null;
    }
  }

  public String getTid() {
    return this.tid;
  }

  public SaRequestRecursess setTid(String tid) {
    this.tid = tid;
    return this;
  }

  public void unsetTid() {
    this.tid = null;
  }

  /** Returns true if field tid is set (has been assigned a value) and false otherwise */
  public boolean isSetTid() {
    return this.tid != null;
  }

  public void setTidIsSet(boolean value) {
    if (!value) {
      this.tid = null;
    }
  }

  public String getPid() {
    return this.pid;
  }

  public SaRequestRecursess setPid(String pid) {
    this.pid = pid;
    return this;
  }

  public void unsetPid() {
    this.pid = null;
  }

  /** Returns true if field pid is set (has been assigned a value) and false otherwise */
  public boolean isSetPid() {
    return this.pid != null;
  }

  public void setPidIsSet(boolean value) {
    if (!value) {
      this.pid = null;
    }
  }

  public boolean isRecursive() {
    return this.recursive;
  }

  public SaRequestRecursess setRecursive(boolean recursive) {
    this.recursive = recursive;
    setRecursiveIsSet(true);
    return this;
  }

  public void unsetRecursive() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RECURSIVE_ISSET_ID);
  }

  /** Returns true if field recursive is set (has been assigned a value) and false otherwise */
  public boolean isSetRecursive() {
    return EncodingUtils.testBit(__isset_bitfield, __RECURSIVE_ISSET_ID);
  }

  public void setRecursiveIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RECURSIVE_ISSET_ID, value);
  }

  public int getColumnsSize() {
    return (this.columns == null) ? 0 : this.columns.size();
  }

  public java.util.Iterator<String> getColumnsIterator() {
    return (this.columns == null) ? null : this.columns.iterator();
  }

  public void addToColumns(String elem) {
    if (this.columns == null) {
      this.columns = new ArrayList<String>();
    }
    this.columns.add(elem);
  }

  public List<String> getColumns() {
    return this.columns;
  }

  public SaRequestRecursess setColumns(List<String> columns) {
    this.columns = columns;
    return this;
  }

  public void unsetColumns() {
    this.columns = null;
  }

  /** Returns true if field columns is set (has been assigned a value) and false otherwise */
  public boolean isSetColumns() {
    return this.columns != null;
  }

  public void setColumnsIsSet(boolean value) {
    if (!value) {
      this.columns = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_MSG:
      if (value == null) {
        unsetAuthMsg();
      } else {
        setAuthMsg((AuthorizeMsg)value);
      }
      break;

    case APPKEY:
      if (value == null) {
        unsetAppkey();
      } else {
        setAppkey((String)value);
      }
      break;

    case TID:
      if (value == null) {
        unsetTid();
      } else {
        setTid((String)value);
      }
      break;

    case PID:
      if (value == null) {
        unsetPid();
      } else {
        setPid((String)value);
      }
      break;

    case RECURSIVE:
      if (value == null) {
        unsetRecursive();
      } else {
        setRecursive((Boolean)value);
      }
      break;

    case COLUMNS:
      if (value == null) {
        unsetColumns();
      } else {
        setColumns((List<String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_MSG:
      return getAuthMsg();

    case APPKEY:
      return getAppkey();

    case TID:
      return getTid();

    case PID:
      return getPid();

    case RECURSIVE:
      return isRecursive();

    case COLUMNS:
      return getColumns();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_MSG:
      return isSetAuthMsg();
    case APPKEY:
      return isSetAppkey();
    case TID:
      return isSetTid();
    case PID:
      return isSetPid();
    case RECURSIVE:
      return isSetRecursive();
    case COLUMNS:
      return isSetColumns();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaRequestRecursess)
      return this.equals((SaRequestRecursess)that);
    return false;
  }

  public boolean equals(SaRequestRecursess that) {
    if (that == null)
      return false;

    boolean this_present_authMsg = true && this.isSetAuthMsg();
    boolean that_present_authMsg = true && that.isSetAuthMsg();
    if (this_present_authMsg || that_present_authMsg) {
      if (!(this_present_authMsg && that_present_authMsg))
        return false;
      if (!this.authMsg.equals(that.authMsg))
        return false;
    }

    boolean this_present_appkey = true && this.isSetAppkey();
    boolean that_present_appkey = true && that.isSetAppkey();
    if (this_present_appkey || that_present_appkey) {
      if (!(this_present_appkey && that_present_appkey))
        return false;
      if (!this.appkey.equals(that.appkey))
        return false;
    }

    boolean this_present_tid = true && this.isSetTid();
    boolean that_present_tid = true && that.isSetTid();
    if (this_present_tid || that_present_tid) {
      if (!(this_present_tid && that_present_tid))
        return false;
      if (!this.tid.equals(that.tid))
        return false;
    }

    boolean this_present_pid = true && this.isSetPid();
    boolean that_present_pid = true && that.isSetPid();
    if (this_present_pid || that_present_pid) {
      if (!(this_present_pid && that_present_pid))
        return false;
      if (!this.pid.equals(that.pid))
        return false;
    }

    boolean this_present_recursive = true;
    boolean that_present_recursive = true;
    if (this_present_recursive || that_present_recursive) {
      if (!(this_present_recursive && that_present_recursive))
        return false;
      if (this.recursive != that.recursive)
        return false;
    }

    boolean this_present_columns = true && this.isSetColumns();
    boolean that_present_columns = true && that.isSetColumns();
    if (this_present_columns || that_present_columns) {
      if (!(this_present_columns && that_present_columns))
        return false;
      if (!this.columns.equals(that.columns))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authMsg = true && (isSetAuthMsg());
    list.add(present_authMsg);
    if (present_authMsg)
      list.add(authMsg);

    boolean present_appkey = true && (isSetAppkey());
    list.add(present_appkey);
    if (present_appkey)
      list.add(appkey);

    boolean present_tid = true && (isSetTid());
    list.add(present_tid);
    if (present_tid)
      list.add(tid);

    boolean present_pid = true && (isSetPid());
    list.add(present_pid);
    if (present_pid)
      list.add(pid);

    boolean present_recursive = true;
    list.add(present_recursive);
    if (present_recursive)
      list.add(recursive);

    boolean present_columns = true && (isSetColumns());
    list.add(present_columns);
    if (present_columns)
      list.add(columns);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaRequestRecursess other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthMsg()).compareTo(other.isSetAuthMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authMsg, other.authMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppkey()).compareTo(other.isSetAppkey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppkey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appkey, other.appkey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTid()).compareTo(other.isSetTid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tid, other.tid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPid()).compareTo(other.isSetPid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pid, other.pid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRecursive()).compareTo(other.isSetRecursive());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRecursive()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.recursive, other.recursive);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetColumns()).compareTo(other.isSetColumns());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetColumns()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.columns, other.columns);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaRequestRecursess(");
    boolean first = true;

    sb.append("authMsg:");
    if (this.authMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authMsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appkey:");
    if (this.appkey == null) {
      sb.append("null");
    } else {
      sb.append(this.appkey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("tid:");
    if (this.tid == null) {
      sb.append("null");
    } else {
      sb.append(this.tid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pid:");
    if (this.pid == null) {
      sb.append("null");
    } else {
      sb.append(this.pid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("recursive:");
    sb.append(this.recursive);
    first = false;
    if (!first) sb.append(", ");
    sb.append("columns:");
    if (this.columns == null) {
      sb.append("null");
    } else {
      sb.append(this.columns);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
    if (authMsg != null) {
      authMsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaRequestRecursessStandardSchemeFactory implements SchemeFactory {
    public SaRequestRecursessStandardScheme getScheme() {
      return new SaRequestRecursessStandardScheme();
    }
  }

  private static class SaRequestRecursessStandardScheme extends StandardScheme<SaRequestRecursess> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaRequestRecursess struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authMsg = new AuthorizeMsg();
              struct.authMsg.read(iprot);
              struct.setAuthMsgIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPKEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appkey = iprot.readString();
              struct.setAppkeyIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.tid = iprot.readString();
              struct.setTidIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.pid = iprot.readString();
              struct.setPidIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // RECURSIVE
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.recursive = iprot.readBool();
              struct.setRecursiveIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // COLUMNS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list104 = iprot.readListBegin();
                struct.columns = new ArrayList<String>(_list104.size);
                String _elem105;
                for (int _i106 = 0; _i106 < _list104.size; ++_i106)
                {
                  _elem105 = iprot.readString();
                  struct.columns.add(_elem105);
                }
                iprot.readListEnd();
              }
              struct.setColumnsIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaRequestRecursess struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authMsg != null) {
        oprot.writeFieldBegin(AUTH_MSG_FIELD_DESC);
        struct.authMsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appkey != null) {
        oprot.writeFieldBegin(APPKEY_FIELD_DESC);
        oprot.writeString(struct.appkey);
        oprot.writeFieldEnd();
      }
      if (struct.tid != null) {
        oprot.writeFieldBegin(TID_FIELD_DESC);
        oprot.writeString(struct.tid);
        oprot.writeFieldEnd();
      }
      if (struct.pid != null) {
        oprot.writeFieldBegin(PID_FIELD_DESC);
        oprot.writeString(struct.pid);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RECURSIVE_FIELD_DESC);
      oprot.writeBool(struct.recursive);
      oprot.writeFieldEnd();
      if (struct.columns != null) {
        oprot.writeFieldBegin(COLUMNS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.columns.size()));
          for (String _iter107 : struct.columns)
          {
            oprot.writeString(_iter107);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaRequestRecursessTupleSchemeFactory implements SchemeFactory {
    public SaRequestRecursessTupleScheme getScheme() {
      return new SaRequestRecursessTupleScheme();
    }
  }

  private static class SaRequestRecursessTupleScheme extends TupleScheme<SaRequestRecursess> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaRequestRecursess struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthMsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppkey()) {
        optionals.set(1);
      }
      if (struct.isSetTid()) {
        optionals.set(2);
      }
      if (struct.isSetPid()) {
        optionals.set(3);
      }
      if (struct.isSetRecursive()) {
        optionals.set(4);
      }
      if (struct.isSetColumns()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetAuthMsg()) {
        struct.authMsg.write(oprot);
      }
      if (struct.isSetAppkey()) {
        oprot.writeString(struct.appkey);
      }
      if (struct.isSetTid()) {
        oprot.writeString(struct.tid);
      }
      if (struct.isSetPid()) {
        oprot.writeString(struct.pid);
      }
      if (struct.isSetRecursive()) {
        oprot.writeBool(struct.recursive);
      }
      if (struct.isSetColumns()) {
        {
          oprot.writeI32(struct.columns.size());
          for (String _iter108 : struct.columns)
          {
            oprot.writeString(_iter108);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaRequestRecursess struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.authMsg = new AuthorizeMsg();
        struct.authMsg.read(iprot);
        struct.setAuthMsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appkey = iprot.readString();
        struct.setAppkeyIsSet(true);
      }
      if (incoming.get(2)) {
        struct.tid = iprot.readString();
        struct.setTidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.pid = iprot.readString();
        struct.setPidIsSet(true);
      }
      if (incoming.get(4)) {
        struct.recursive = iprot.readBool();
        struct.setRecursiveIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TList _list109 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.columns = new ArrayList<String>(_list109.size);
          String _elem110;
          for (int _i111 = 0; _i111 < _list109.size; ++_i111)
          {
            _elem110 = iprot.readString();
            struct.columns.add(_elem110);
          }
        }
        struct.setColumnsIsSet(true);
      }
    }
  }

}

