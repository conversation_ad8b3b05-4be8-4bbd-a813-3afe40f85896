/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.hdzt.ranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-11-17")
public class PkInfo implements org.apache.thrift.TBase<PkInfo, PkInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PkInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PkInfo");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField DEST_RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("destRankId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SRC_RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("srcRankId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField PK_AWARD_FIELD_DESC = new org.apache.thrift.protocol.TField("pkAward", org.apache.thrift.protocol.TType.LIST, (short)5);
  private static final org.apache.thrift.protocol.TField RANK_AWARD_FIELD_DESC = new org.apache.thrift.protocol.TField("rankAward", org.apache.thrift.protocol.TType.LIST, (short)6);
  private static final org.apache.thrift.protocol.TField DATE_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("dateStr", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField PK_GROUP_ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("pkGroupItems", org.apache.thrift.protocol.TType.LIST, (short)8);
  private static final org.apache.thrift.protocol.TField REMARK_FIELD_DESC = new org.apache.thrift.protocol.TField("remark", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField SETTLE_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("settleStatus", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new PkInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new PkInfoTupleSchemeFactory());
  }

  public long actId; // required
  public long destRankId; // required
  public long phaseId; // required
  public long srcRankId; // required
  public List<Integer> pkAward; // required
  public List<Integer> rankAward; // required
  public String dateStr; // required
  public List<PkGroupItem> pkGroupItems; // required
  public String remark; // required
  public long settleStatus; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    DEST_RANK_ID((short)2, "destRankId"),
    PHASE_ID((short)3, "phaseId"),
    SRC_RANK_ID((short)4, "srcRankId"),
    PK_AWARD((short)5, "pkAward"),
    RANK_AWARD((short)6, "rankAward"),
    DATE_STR((short)7, "dateStr"),
    PK_GROUP_ITEMS((short)8, "pkGroupItems"),
    REMARK((short)9, "remark"),
    SETTLE_STATUS((short)10, "settleStatus"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // DEST_RANK_ID
          return DEST_RANK_ID;
        case 3: // PHASE_ID
          return PHASE_ID;
        case 4: // SRC_RANK_ID
          return SRC_RANK_ID;
        case 5: // PK_AWARD
          return PK_AWARD;
        case 6: // RANK_AWARD
          return RANK_AWARD;
        case 7: // DATE_STR
          return DATE_STR;
        case 8: // PK_GROUP_ITEMS
          return PK_GROUP_ITEMS;
        case 9: // REMARK
          return REMARK;
        case 10: // SETTLE_STATUS
          return SETTLE_STATUS;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __DESTRANKID_ISSET_ID = 1;
  private static final int __PHASEID_ISSET_ID = 2;
  private static final int __SRCRANKID_ISSET_ID = 3;
  private static final int __SETTLESTATUS_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DEST_RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("destRankId", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SRC_RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("srcRankId", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PK_AWARD, new org.apache.thrift.meta_data.FieldMetaData("pkAward", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    tmpMap.put(_Fields.RANK_AWARD, new org.apache.thrift.meta_data.FieldMetaData("rankAward", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    tmpMap.put(_Fields.DATE_STR, new org.apache.thrift.meta_data.FieldMetaData("dateStr", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PK_GROUP_ITEMS, new org.apache.thrift.meta_data.FieldMetaData("pkGroupItems", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PkGroupItem.class))));
    tmpMap.put(_Fields.REMARK, new org.apache.thrift.meta_data.FieldMetaData("remark", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SETTLE_STATUS, new org.apache.thrift.meta_data.FieldMetaData("settleStatus", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING),
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PkInfo.class, metaDataMap);
  }

  public PkInfo() {
  }

  public PkInfo(
    long actId,
    long destRankId,
    long phaseId,
    long srcRankId,
    List<Integer> pkAward,
    List<Integer> rankAward,
    String dateStr,
    List<PkGroupItem> pkGroupItems,
    String remark,
    long settleStatus,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.destRankId = destRankId;
    setDestRankIdIsSet(true);
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.srcRankId = srcRankId;
    setSrcRankIdIsSet(true);
    this.pkAward = pkAward;
    this.rankAward = rankAward;
    this.dateStr = dateStr;
    this.pkGroupItems = pkGroupItems;
    this.remark = remark;
    this.settleStatus = settleStatus;
    setSettleStatusIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PkInfo(PkInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.destRankId = other.destRankId;
    this.phaseId = other.phaseId;
    this.srcRankId = other.srcRankId;
    if (other.isSetPkAward()) {
      List<Integer> __this__pkAward = new ArrayList<Integer>(other.pkAward);
      this.pkAward = __this__pkAward;
    }
    if (other.isSetRankAward()) {
      List<Integer> __this__rankAward = new ArrayList<Integer>(other.rankAward);
      this.rankAward = __this__rankAward;
    }
    if (other.isSetDateStr()) {
      this.dateStr = other.dateStr;
    }
    if (other.isSetPkGroupItems()) {
      List<PkGroupItem> __this__pkGroupItems = new ArrayList<PkGroupItem>(other.pkGroupItems.size());
      for (PkGroupItem other_element : other.pkGroupItems) {
        __this__pkGroupItems.add(new PkGroupItem(other_element));
      }
      this.pkGroupItems = __this__pkGroupItems;
    }
    if (other.isSetRemark()) {
      this.remark = other.remark;
    }
    this.settleStatus = other.settleStatus;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public PkInfo deepCopy() {
    return new PkInfo(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setDestRankIdIsSet(false);
    this.destRankId = 0;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    setSrcRankIdIsSet(false);
    this.srcRankId = 0;
    this.pkAward = null;
    this.rankAward = null;
    this.dateStr = null;
    this.pkGroupItems = null;
    this.remark = null;
    setSettleStatusIsSet(false);
    this.settleStatus = 0;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public PkInfo setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getDestRankId() {
    return this.destRankId;
  }

  public PkInfo setDestRankId(long destRankId) {
    this.destRankId = destRankId;
    setDestRankIdIsSet(true);
    return this;
  }

  public void unsetDestRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __DESTRANKID_ISSET_ID);
  }

  /** Returns true if field destRankId is set (has been assigned a value) and false otherwise */
  public boolean isSetDestRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __DESTRANKID_ISSET_ID);
  }

  public void setDestRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __DESTRANKID_ISSET_ID, value);
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public PkInfo setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public long getSrcRankId() {
    return this.srcRankId;
  }

  public PkInfo setSrcRankId(long srcRankId) {
    this.srcRankId = srcRankId;
    setSrcRankIdIsSet(true);
    return this;
  }

  public void unsetSrcRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SRCRANKID_ISSET_ID);
  }

  /** Returns true if field srcRankId is set (has been assigned a value) and false otherwise */
  public boolean isSetSrcRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __SRCRANKID_ISSET_ID);
  }

  public void setSrcRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SRCRANKID_ISSET_ID, value);
  }

  public int getPkAwardSize() {
    return (this.pkAward == null) ? 0 : this.pkAward.size();
  }

  public java.util.Iterator<Integer> getPkAwardIterator() {
    return (this.pkAward == null) ? null : this.pkAward.iterator();
  }

  public void addToPkAward(int elem) {
    if (this.pkAward == null) {
      this.pkAward = new ArrayList<Integer>();
    }
    this.pkAward.add(elem);
  }

  public List<Integer> getPkAward() {
    return this.pkAward;
  }

  public PkInfo setPkAward(List<Integer> pkAward) {
    this.pkAward = pkAward;
    return this;
  }

  public void unsetPkAward() {
    this.pkAward = null;
  }

  /** Returns true if field pkAward is set (has been assigned a value) and false otherwise */
  public boolean isSetPkAward() {
    return this.pkAward != null;
  }

  public void setPkAwardIsSet(boolean value) {
    if (!value) {
      this.pkAward = null;
    }
  }

  public int getRankAwardSize() {
    return (this.rankAward == null) ? 0 : this.rankAward.size();
  }

  public java.util.Iterator<Integer> getRankAwardIterator() {
    return (this.rankAward == null) ? null : this.rankAward.iterator();
  }

  public void addToRankAward(int elem) {
    if (this.rankAward == null) {
      this.rankAward = new ArrayList<Integer>();
    }
    this.rankAward.add(elem);
  }

  public List<Integer> getRankAward() {
    return this.rankAward;
  }

  public PkInfo setRankAward(List<Integer> rankAward) {
    this.rankAward = rankAward;
    return this;
  }

  public void unsetRankAward() {
    this.rankAward = null;
  }

  /** Returns true if field rankAward is set (has been assigned a value) and false otherwise */
  public boolean isSetRankAward() {
    return this.rankAward != null;
  }

  public void setRankAwardIsSet(boolean value) {
    if (!value) {
      this.rankAward = null;
    }
  }

  public String getDateStr() {
    return this.dateStr;
  }

  public PkInfo setDateStr(String dateStr) {
    this.dateStr = dateStr;
    return this;
  }

  public void unsetDateStr() {
    this.dateStr = null;
  }

  /** Returns true if field dateStr is set (has been assigned a value) and false otherwise */
  public boolean isSetDateStr() {
    return this.dateStr != null;
  }

  public void setDateStrIsSet(boolean value) {
    if (!value) {
      this.dateStr = null;
    }
  }

  public int getPkGroupItemsSize() {
    return (this.pkGroupItems == null) ? 0 : this.pkGroupItems.size();
  }

  public java.util.Iterator<PkGroupItem> getPkGroupItemsIterator() {
    return (this.pkGroupItems == null) ? null : this.pkGroupItems.iterator();
  }

  public void addToPkGroupItems(PkGroupItem elem) {
    if (this.pkGroupItems == null) {
      this.pkGroupItems = new ArrayList<PkGroupItem>();
    }
    this.pkGroupItems.add(elem);
  }

  public List<PkGroupItem> getPkGroupItems() {
    return this.pkGroupItems;
  }

  public PkInfo setPkGroupItems(List<PkGroupItem> pkGroupItems) {
    this.pkGroupItems = pkGroupItems;
    return this;
  }

  public void unsetPkGroupItems() {
    this.pkGroupItems = null;
  }

  /** Returns true if field pkGroupItems is set (has been assigned a value) and false otherwise */
  public boolean isSetPkGroupItems() {
    return this.pkGroupItems != null;
  }

  public void setPkGroupItemsIsSet(boolean value) {
    if (!value) {
      this.pkGroupItems = null;
    }
  }

  public String getRemark() {
    return this.remark;
  }

  public PkInfo setRemark(String remark) {
    this.remark = remark;
    return this;
  }

  public void unsetRemark() {
    this.remark = null;
  }

  /** Returns true if field remark is set (has been assigned a value) and false otherwise */
  public boolean isSetRemark() {
    return this.remark != null;
  }

  public void setRemarkIsSet(boolean value) {
    if (!value) {
      this.remark = null;
    }
  }

  public long getSettleStatus() {
    return this.settleStatus;
  }

  public PkInfo setSettleStatus(long settleStatus) {
    this.settleStatus = settleStatus;
    setSettleStatusIsSet(true);
    return this;
  }

  public void unsetSettleStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SETTLESTATUS_ISSET_ID);
  }

  /** Returns true if field settleStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetSettleStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __SETTLESTATUS_ISSET_ID);
  }

  public void setSettleStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SETTLESTATUS_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public PkInfo setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case DEST_RANK_ID:
      if (value == null) {
        unsetDestRankId();
      } else {
        setDestRankId((Long)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case SRC_RANK_ID:
      if (value == null) {
        unsetSrcRankId();
      } else {
        setSrcRankId((Long)value);
      }
      break;

    case PK_AWARD:
      if (value == null) {
        unsetPkAward();
      } else {
        setPkAward((List<Integer>)value);
      }
      break;

    case RANK_AWARD:
      if (value == null) {
        unsetRankAward();
      } else {
        setRankAward((List<Integer>)value);
      }
      break;

    case DATE_STR:
      if (value == null) {
        unsetDateStr();
      } else {
        setDateStr((String)value);
      }
      break;

    case PK_GROUP_ITEMS:
      if (value == null) {
        unsetPkGroupItems();
      } else {
        setPkGroupItems((List<PkGroupItem>)value);
      }
      break;

    case REMARK:
      if (value == null) {
        unsetRemark();
      } else {
        setRemark((String)value);
      }
      break;

    case SETTLE_STATUS:
      if (value == null) {
        unsetSettleStatus();
      } else {
        setSettleStatus((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case DEST_RANK_ID:
      return getDestRankId();

    case PHASE_ID:
      return getPhaseId();

    case SRC_RANK_ID:
      return getSrcRankId();

    case PK_AWARD:
      return getPkAward();

    case RANK_AWARD:
      return getRankAward();

    case DATE_STR:
      return getDateStr();

    case PK_GROUP_ITEMS:
      return getPkGroupItems();

    case REMARK:
      return getRemark();

    case SETTLE_STATUS:
      return getSettleStatus();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case DEST_RANK_ID:
      return isSetDestRankId();
    case PHASE_ID:
      return isSetPhaseId();
    case SRC_RANK_ID:
      return isSetSrcRankId();
    case PK_AWARD:
      return isSetPkAward();
    case RANK_AWARD:
      return isSetRankAward();
    case DATE_STR:
      return isSetDateStr();
    case PK_GROUP_ITEMS:
      return isSetPkGroupItems();
    case REMARK:
      return isSetRemark();
    case SETTLE_STATUS:
      return isSetSettleStatus();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof PkInfo)
      return this.equals((PkInfo)that);
    return false;
  }

  public boolean equals(PkInfo that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_destRankId = true;
    boolean that_present_destRankId = true;
    if (this_present_destRankId || that_present_destRankId) {
      if (!(this_present_destRankId && that_present_destRankId))
        return false;
      if (this.destRankId != that.destRankId)
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_srcRankId = true;
    boolean that_present_srcRankId = true;
    if (this_present_srcRankId || that_present_srcRankId) {
      if (!(this_present_srcRankId && that_present_srcRankId))
        return false;
      if (this.srcRankId != that.srcRankId)
        return false;
    }

    boolean this_present_pkAward = true && this.isSetPkAward();
    boolean that_present_pkAward = true && that.isSetPkAward();
    if (this_present_pkAward || that_present_pkAward) {
      if (!(this_present_pkAward && that_present_pkAward))
        return false;
      if (!this.pkAward.equals(that.pkAward))
        return false;
    }

    boolean this_present_rankAward = true && this.isSetRankAward();
    boolean that_present_rankAward = true && that.isSetRankAward();
    if (this_present_rankAward || that_present_rankAward) {
      if (!(this_present_rankAward && that_present_rankAward))
        return false;
      if (!this.rankAward.equals(that.rankAward))
        return false;
    }

    boolean this_present_dateStr = true && this.isSetDateStr();
    boolean that_present_dateStr = true && that.isSetDateStr();
    if (this_present_dateStr || that_present_dateStr) {
      if (!(this_present_dateStr && that_present_dateStr))
        return false;
      if (!this.dateStr.equals(that.dateStr))
        return false;
    }

    boolean this_present_pkGroupItems = true && this.isSetPkGroupItems();
    boolean that_present_pkGroupItems = true && that.isSetPkGroupItems();
    if (this_present_pkGroupItems || that_present_pkGroupItems) {
      if (!(this_present_pkGroupItems && that_present_pkGroupItems))
        return false;
      if (!this.pkGroupItems.equals(that.pkGroupItems))
        return false;
    }

    boolean this_present_remark = true && this.isSetRemark();
    boolean that_present_remark = true && that.isSetRemark();
    if (this_present_remark || that_present_remark) {
      if (!(this_present_remark && that_present_remark))
        return false;
      if (!this.remark.equals(that.remark))
        return false;
    }

    boolean this_present_settleStatus = true;
    boolean that_present_settleStatus = true;
    if (this_present_settleStatus || that_present_settleStatus) {
      if (!(this_present_settleStatus && that_present_settleStatus))
        return false;
      if (this.settleStatus != that.settleStatus)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_destRankId = true;
    list.add(present_destRankId);
    if (present_destRankId)
      list.add(destRankId);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_srcRankId = true;
    list.add(present_srcRankId);
    if (present_srcRankId)
      list.add(srcRankId);

    boolean present_pkAward = true && (isSetPkAward());
    list.add(present_pkAward);
    if (present_pkAward)
      list.add(pkAward);

    boolean present_rankAward = true && (isSetRankAward());
    list.add(present_rankAward);
    if (present_rankAward)
      list.add(rankAward);

    boolean present_dateStr = true && (isSetDateStr());
    list.add(present_dateStr);
    if (present_dateStr)
      list.add(dateStr);

    boolean present_pkGroupItems = true && (isSetPkGroupItems());
    list.add(present_pkGroupItems);
    if (present_pkGroupItems)
      list.add(pkGroupItems);

    boolean present_remark = true && (isSetRemark());
    list.add(present_remark);
    if (present_remark)
      list.add(remark);

    boolean present_settleStatus = true;
    list.add(present_settleStatus);
    if (present_settleStatus)
      list.add(settleStatus);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(PkInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDestRankId()).compareTo(other.isSetDestRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDestRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.destRankId, other.destRankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSrcRankId()).compareTo(other.isSetSrcRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSrcRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.srcRankId, other.srcRankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPkAward()).compareTo(other.isSetPkAward());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPkAward()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pkAward, other.pkAward);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankAward()).compareTo(other.isSetRankAward());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankAward()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankAward, other.rankAward);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDateStr()).compareTo(other.isSetDateStr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDateStr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dateStr, other.dateStr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPkGroupItems()).compareTo(other.isSetPkGroupItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPkGroupItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pkGroupItems, other.pkGroupItems);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRemark()).compareTo(other.isSetRemark());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemark()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.remark, other.remark);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSettleStatus()).compareTo(other.isSetSettleStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSettleStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.settleStatus, other.settleStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PkInfo(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("destRankId:");
    sb.append(this.destRankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("srcRankId:");
    sb.append(this.srcRankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pkAward:");
    if (this.pkAward == null) {
      sb.append("null");
    } else {
      sb.append(this.pkAward);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankAward:");
    if (this.rankAward == null) {
      sb.append("null");
    } else {
      sb.append(this.rankAward);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("dateStr:");
    if (this.dateStr == null) {
      sb.append("null");
    } else {
      sb.append(this.dateStr);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pkGroupItems:");
    if (this.pkGroupItems == null) {
      sb.append("null");
    } else {
      sb.append(this.pkGroupItems);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("remark:");
    if (this.remark == null) {
      sb.append("null");
    } else {
      sb.append(this.remark);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("settleStatus:");
    sb.append(this.settleStatus);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PkInfoStandardSchemeFactory implements SchemeFactory {
    public PkInfoStandardScheme getScheme() {
      return new PkInfoStandardScheme();
    }
  }

  private static class PkInfoStandardScheme extends StandardScheme<PkInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PkInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // DEST_RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.destRankId = iprot.readI64();
              struct.setDestRankIdIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SRC_RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.srcRankId = iprot.readI64();
              struct.setSrcRankIdIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // PK_AWARD
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list606 = iprot.readListBegin();
                struct.pkAward = new ArrayList<Integer>(_list606.size);
                int _elem607;
                for (int _i608 = 0; _i608 < _list606.size; ++_i608)
                {
                  _elem607 = iprot.readI32();
                  struct.pkAward.add(_elem607);
                }
                iprot.readListEnd();
              }
              struct.setPkAwardIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // RANK_AWARD
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list609 = iprot.readListBegin();
                struct.rankAward = new ArrayList<Integer>(_list609.size);
                int _elem610;
                for (int _i611 = 0; _i611 < _list609.size; ++_i611)
                {
                  _elem610 = iprot.readI32();
                  struct.rankAward.add(_elem610);
                }
                iprot.readListEnd();
              }
              struct.setRankAwardIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // DATE_STR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.dateStr = iprot.readString();
              struct.setDateStrIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // PK_GROUP_ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list612 = iprot.readListBegin();
                struct.pkGroupItems = new ArrayList<PkGroupItem>(_list612.size);
                PkGroupItem _elem613;
                for (int _i614 = 0; _i614 < _list612.size; ++_i614)
                {
                  _elem613 = new PkGroupItem();
                  _elem613.read(iprot);
                  struct.pkGroupItems.add(_elem613);
                }
                iprot.readListEnd();
              }
              struct.setPkGroupItemsIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // REMARK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.remark = iprot.readString();
              struct.setRemarkIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // SETTLE_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.settleStatus = iprot.readI64();
              struct.setSettleStatusIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map615 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map615.size);
                String _key616;
                String _val617;
                for (int _i618 = 0; _i618 < _map615.size; ++_i618)
                {
                  _key616 = iprot.readString();
                  _val617 = iprot.readString();
                  struct.extData.put(_key616, _val617);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PkInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(DEST_RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.destRankId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SRC_RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.srcRankId);
      oprot.writeFieldEnd();
      if (struct.pkAward != null) {
        oprot.writeFieldBegin(PK_AWARD_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.pkAward.size()));
          for (int _iter619 : struct.pkAward)
          {
            oprot.writeI32(_iter619);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.rankAward != null) {
        oprot.writeFieldBegin(RANK_AWARD_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.rankAward.size()));
          for (int _iter620 : struct.rankAward)
          {
            oprot.writeI32(_iter620);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.dateStr != null) {
        oprot.writeFieldBegin(DATE_STR_FIELD_DESC);
        oprot.writeString(struct.dateStr);
        oprot.writeFieldEnd();
      }
      if (struct.pkGroupItems != null) {
        oprot.writeFieldBegin(PK_GROUP_ITEMS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.pkGroupItems.size()));
          for (PkGroupItem _iter621 : struct.pkGroupItems)
          {
            _iter621.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.remark != null) {
        oprot.writeFieldBegin(REMARK_FIELD_DESC);
        oprot.writeString(struct.remark);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SETTLE_STATUS_FIELD_DESC);
      oprot.writeI64(struct.settleStatus);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter622 : struct.extData.entrySet())
          {
            oprot.writeString(_iter622.getKey());
            oprot.writeString(_iter622.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PkInfoTupleSchemeFactory implements SchemeFactory {
    public PkInfoTupleScheme getScheme() {
      return new PkInfoTupleScheme();
    }
  }

  private static class PkInfoTupleScheme extends TupleScheme<PkInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PkInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetDestRankId()) {
        optionals.set(1);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(2);
      }
      if (struct.isSetSrcRankId()) {
        optionals.set(3);
      }
      if (struct.isSetPkAward()) {
        optionals.set(4);
      }
      if (struct.isSetRankAward()) {
        optionals.set(5);
      }
      if (struct.isSetDateStr()) {
        optionals.set(6);
      }
      if (struct.isSetPkGroupItems()) {
        optionals.set(7);
      }
      if (struct.isSetRemark()) {
        optionals.set(8);
      }
      if (struct.isSetSettleStatus()) {
        optionals.set(9);
      }
      if (struct.isSetExtData()) {
        optionals.set(10);
      }
      oprot.writeBitSet(optionals, 11);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetDestRankId()) {
        oprot.writeI64(struct.destRankId);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetSrcRankId()) {
        oprot.writeI64(struct.srcRankId);
      }
      if (struct.isSetPkAward()) {
        {
          oprot.writeI32(struct.pkAward.size());
          for (int _iter623 : struct.pkAward)
          {
            oprot.writeI32(_iter623);
          }
        }
      }
      if (struct.isSetRankAward()) {
        {
          oprot.writeI32(struct.rankAward.size());
          for (int _iter624 : struct.rankAward)
          {
            oprot.writeI32(_iter624);
          }
        }
      }
      if (struct.isSetDateStr()) {
        oprot.writeString(struct.dateStr);
      }
      if (struct.isSetPkGroupItems()) {
        {
          oprot.writeI32(struct.pkGroupItems.size());
          for (PkGroupItem _iter625 : struct.pkGroupItems)
          {
            _iter625.write(oprot);
          }
        }
      }
      if (struct.isSetRemark()) {
        oprot.writeString(struct.remark);
      }
      if (struct.isSetSettleStatus()) {
        oprot.writeI64(struct.settleStatus);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter626 : struct.extData.entrySet())
          {
            oprot.writeString(_iter626.getKey());
            oprot.writeString(_iter626.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PkInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(11);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.destRankId = iprot.readI64();
        struct.setDestRankIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.srcRankId = iprot.readI64();
        struct.setSrcRankIdIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TList _list627 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, iprot.readI32());
          struct.pkAward = new ArrayList<Integer>(_list627.size);
          int _elem628;
          for (int _i629 = 0; _i629 < _list627.size; ++_i629)
          {
            _elem628 = iprot.readI32();
            struct.pkAward.add(_elem628);
          }
        }
        struct.setPkAwardIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TList _list630 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, iprot.readI32());
          struct.rankAward = new ArrayList<Integer>(_list630.size);
          int _elem631;
          for (int _i632 = 0; _i632 < _list630.size; ++_i632)
          {
            _elem631 = iprot.readI32();
            struct.rankAward.add(_elem631);
          }
        }
        struct.setRankAwardIsSet(true);
      }
      if (incoming.get(6)) {
        struct.dateStr = iprot.readString();
        struct.setDateStrIsSet(true);
      }
      if (incoming.get(7)) {
        {
          org.apache.thrift.protocol.TList _list633 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.pkGroupItems = new ArrayList<PkGroupItem>(_list633.size);
          PkGroupItem _elem634;
          for (int _i635 = 0; _i635 < _list633.size; ++_i635)
          {
            _elem634 = new PkGroupItem();
            _elem634.read(iprot);
            struct.pkGroupItems.add(_elem634);
          }
        }
        struct.setPkGroupItemsIsSet(true);
      }
      if (incoming.get(8)) {
        struct.remark = iprot.readString();
        struct.setRemarkIsSet(true);
      }
      if (incoming.get(9)) {
        struct.settleStatus = iprot.readI64();
        struct.setSettleStatusIsSet(true);
      }
      if (incoming.get(10)) {
        {
          org.apache.thrift.protocol.TMap _map636 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map636.size);
          String _key637;
          String _val638;
          for (int _i639 = 0; _i639 < _map636.size; ++_i639)
          {
            _key637 = iprot.readString();
            _val638 = iprot.readString();
            struct.extData.put(_key637, _val638);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

