/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.manager.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 解禁用户修改个人资料(昵称/签名/个性签名)的限制
 * @param   appkey        客户端标识
 * @param   uids          解禁的用户uid列表(列表最大长度为50)
 * @param   reasonCode    封禁原因: 0 - 违规; 1 - 公会uid
 *
 *
 * @SaResponse			  返回值rescode，设置成功返回0
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-10-29")
public class SaRequestUnlmtUptUser implements org.apache.thrift.TBase<SaRequestUnlmtUptUser, SaRequestUnlmtUptUser._Fields>, java.io.Serializable, Cloneable, Comparable<SaRequestUnlmtUptUser> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaRequestUnlmtUptUser");

  private static final org.apache.thrift.protocol.TField AUTH_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authMsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPKEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appkey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField UIDS_FIELD_DESC = new org.apache.thrift.protocol.TField("uids", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField REASON_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("reasonCode", org.apache.thrift.protocol.TType.I16, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaRequestUnlmtUptUserStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaRequestUnlmtUptUserTupleSchemeFactory());
  }

  public AuthorizeMsg authMsg; // required
  public String appkey; // required
  public List<String> uids; // required
  public short reasonCode; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_MSG((short)1, "authMsg"),
    APPKEY((short)2, "appkey"),
    UIDS((short)3, "uids"),
    REASON_CODE((short)4, "reasonCode");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_MSG
          return AUTH_MSG;
        case 2: // APPKEY
          return APPKEY;
        case 3: // UIDS
          return UIDS;
        case 4: // REASON_CODE
          return REASON_CODE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __REASONCODE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_MSG, new org.apache.thrift.meta_data.FieldMetaData("authMsg", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPKEY, new org.apache.thrift.meta_data.FieldMetaData("appkey", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.UIDS, new org.apache.thrift.meta_data.FieldMetaData("uids", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.REASON_CODE, new org.apache.thrift.meta_data.FieldMetaData("reasonCode", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I16)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaRequestUnlmtUptUser.class, metaDataMap);
  }

  public SaRequestUnlmtUptUser() {
  }

  public SaRequestUnlmtUptUser(
    AuthorizeMsg authMsg,
    String appkey,
    List<String> uids,
    short reasonCode)
  {
    this();
    this.authMsg = authMsg;
    this.appkey = appkey;
    this.uids = uids;
    this.reasonCode = reasonCode;
    setReasonCodeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaRequestUnlmtUptUser(SaRequestUnlmtUptUser other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAuthMsg()) {
      this.authMsg = new AuthorizeMsg(other.authMsg);
    }
    if (other.isSetAppkey()) {
      this.appkey = other.appkey;
    }
    if (other.isSetUids()) {
      List<String> __this__uids = new ArrayList<String>(other.uids);
      this.uids = __this__uids;
    }
    this.reasonCode = other.reasonCode;
  }

  public SaRequestUnlmtUptUser deepCopy() {
    return new SaRequestUnlmtUptUser(this);
  }

  @Override
  public void clear() {
    this.authMsg = null;
    this.appkey = null;
    this.uids = null;
    setReasonCodeIsSet(false);
    this.reasonCode = 0;
  }

  public AuthorizeMsg getAuthMsg() {
    return this.authMsg;
  }

  public SaRequestUnlmtUptUser setAuthMsg(AuthorizeMsg authMsg) {
    this.authMsg = authMsg;
    return this;
  }

  public void unsetAuthMsg() {
    this.authMsg = null;
  }

  /** Returns true if field authMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthMsg() {
    return this.authMsg != null;
  }

  public void setAuthMsgIsSet(boolean value) {
    if (!value) {
      this.authMsg = null;
    }
  }

  public String getAppkey() {
    return this.appkey;
  }

  public SaRequestUnlmtUptUser setAppkey(String appkey) {
    this.appkey = appkey;
    return this;
  }

  public void unsetAppkey() {
    this.appkey = null;
  }

  /** Returns true if field appkey is set (has been assigned a value) and false otherwise */
  public boolean isSetAppkey() {
    return this.appkey != null;
  }

  public void setAppkeyIsSet(boolean value) {
    if (!value) {
      this.appkey = null;
    }
  }

  public int getUidsSize() {
    return (this.uids == null) ? 0 : this.uids.size();
  }

  public java.util.Iterator<String> getUidsIterator() {
    return (this.uids == null) ? null : this.uids.iterator();
  }

  public void addToUids(String elem) {
    if (this.uids == null) {
      this.uids = new ArrayList<String>();
    }
    this.uids.add(elem);
  }

  public List<String> getUids() {
    return this.uids;
  }

  public SaRequestUnlmtUptUser setUids(List<String> uids) {
    this.uids = uids;
    return this;
  }

  public void unsetUids() {
    this.uids = null;
  }

  /** Returns true if field uids is set (has been assigned a value) and false otherwise */
  public boolean isSetUids() {
    return this.uids != null;
  }

  public void setUidsIsSet(boolean value) {
    if (!value) {
      this.uids = null;
    }
  }

  public short getReasonCode() {
    return this.reasonCode;
  }

  public SaRequestUnlmtUptUser setReasonCode(short reasonCode) {
    this.reasonCode = reasonCode;
    setReasonCodeIsSet(true);
    return this;
  }

  public void unsetReasonCode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REASONCODE_ISSET_ID);
  }

  /** Returns true if field reasonCode is set (has been assigned a value) and false otherwise */
  public boolean isSetReasonCode() {
    return EncodingUtils.testBit(__isset_bitfield, __REASONCODE_ISSET_ID);
  }

  public void setReasonCodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REASONCODE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_MSG:
      if (value == null) {
        unsetAuthMsg();
      } else {
        setAuthMsg((AuthorizeMsg)value);
      }
      break;

    case APPKEY:
      if (value == null) {
        unsetAppkey();
      } else {
        setAppkey((String)value);
      }
      break;

    case UIDS:
      if (value == null) {
        unsetUids();
      } else {
        setUids((List<String>)value);
      }
      break;

    case REASON_CODE:
      if (value == null) {
        unsetReasonCode();
      } else {
        setReasonCode((Short)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_MSG:
      return getAuthMsg();

    case APPKEY:
      return getAppkey();

    case UIDS:
      return getUids();

    case REASON_CODE:
      return getReasonCode();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_MSG:
      return isSetAuthMsg();
    case APPKEY:
      return isSetAppkey();
    case UIDS:
      return isSetUids();
    case REASON_CODE:
      return isSetReasonCode();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaRequestUnlmtUptUser)
      return this.equals((SaRequestUnlmtUptUser)that);
    return false;
  }

  public boolean equals(SaRequestUnlmtUptUser that) {
    if (that == null)
      return false;

    boolean this_present_authMsg = true && this.isSetAuthMsg();
    boolean that_present_authMsg = true && that.isSetAuthMsg();
    if (this_present_authMsg || that_present_authMsg) {
      if (!(this_present_authMsg && that_present_authMsg))
        return false;
      if (!this.authMsg.equals(that.authMsg))
        return false;
    }

    boolean this_present_appkey = true && this.isSetAppkey();
    boolean that_present_appkey = true && that.isSetAppkey();
    if (this_present_appkey || that_present_appkey) {
      if (!(this_present_appkey && that_present_appkey))
        return false;
      if (!this.appkey.equals(that.appkey))
        return false;
    }

    boolean this_present_uids = true && this.isSetUids();
    boolean that_present_uids = true && that.isSetUids();
    if (this_present_uids || that_present_uids) {
      if (!(this_present_uids && that_present_uids))
        return false;
      if (!this.uids.equals(that.uids))
        return false;
    }

    boolean this_present_reasonCode = true;
    boolean that_present_reasonCode = true;
    if (this_present_reasonCode || that_present_reasonCode) {
      if (!(this_present_reasonCode && that_present_reasonCode))
        return false;
      if (this.reasonCode != that.reasonCode)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authMsg = true && (isSetAuthMsg());
    list.add(present_authMsg);
    if (present_authMsg)
      list.add(authMsg);

    boolean present_appkey = true && (isSetAppkey());
    list.add(present_appkey);
    if (present_appkey)
      list.add(appkey);

    boolean present_uids = true && (isSetUids());
    list.add(present_uids);
    if (present_uids)
      list.add(uids);

    boolean present_reasonCode = true;
    list.add(present_reasonCode);
    if (present_reasonCode)
      list.add(reasonCode);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaRequestUnlmtUptUser other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthMsg()).compareTo(other.isSetAuthMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authMsg, other.authMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppkey()).compareTo(other.isSetAppkey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppkey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appkey, other.appkey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUids()).compareTo(other.isSetUids());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUids()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uids, other.uids);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReasonCode()).compareTo(other.isSetReasonCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReasonCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reasonCode, other.reasonCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaRequestUnlmtUptUser(");
    boolean first = true;

    sb.append("authMsg:");
    if (this.authMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authMsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appkey:");
    if (this.appkey == null) {
      sb.append("null");
    } else {
      sb.append(this.appkey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("uids:");
    if (this.uids == null) {
      sb.append("null");
    } else {
      sb.append(this.uids);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("reasonCode:");
    sb.append(this.reasonCode);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
    if (authMsg != null) {
      authMsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaRequestUnlmtUptUserStandardSchemeFactory implements SchemeFactory {
    public SaRequestUnlmtUptUserStandardScheme getScheme() {
      return new SaRequestUnlmtUptUserStandardScheme();
    }
  }

  private static class SaRequestUnlmtUptUserStandardScheme extends StandardScheme<SaRequestUnlmtUptUser> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaRequestUnlmtUptUser struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authMsg = new AuthorizeMsg();
              struct.authMsg.read(iprot);
              struct.setAuthMsgIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPKEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appkey = iprot.readString();
              struct.setAppkeyIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // UIDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list164 = iprot.readListBegin();
                struct.uids = new ArrayList<String>(_list164.size);
                String _elem165;
                for (int _i166 = 0; _i166 < _list164.size; ++_i166)
                {
                  _elem165 = iprot.readString();
                  struct.uids.add(_elem165);
                }
                iprot.readListEnd();
              }
              struct.setUidsIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // REASON_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I16) {
              struct.reasonCode = iprot.readI16();
              struct.setReasonCodeIsSet(true);
            } else {
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaRequestUnlmtUptUser struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authMsg != null) {
        oprot.writeFieldBegin(AUTH_MSG_FIELD_DESC);
        struct.authMsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appkey != null) {
        oprot.writeFieldBegin(APPKEY_FIELD_DESC);
        oprot.writeString(struct.appkey);
        oprot.writeFieldEnd();
      }
      if (struct.uids != null) {
        oprot.writeFieldBegin(UIDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.uids.size()));
          for (String _iter167 : struct.uids)
          {
            oprot.writeString(_iter167);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(REASON_CODE_FIELD_DESC);
      oprot.writeI16(struct.reasonCode);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaRequestUnlmtUptUserTupleSchemeFactory implements SchemeFactory {
    public SaRequestUnlmtUptUserTupleScheme getScheme() {
      return new SaRequestUnlmtUptUserTupleScheme();
    }
  }

  private static class SaRequestUnlmtUptUserTupleScheme extends TupleScheme<SaRequestUnlmtUptUser> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaRequestUnlmtUptUser struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthMsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppkey()) {
        optionals.set(1);
      }
      if (struct.isSetUids()) {
        optionals.set(2);
      }
      if (struct.isSetReasonCode()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetAuthMsg()) {
        struct.authMsg.write(oprot);
      }
      if (struct.isSetAppkey()) {
        oprot.writeString(struct.appkey);
      }
      if (struct.isSetUids()) {
        {
          oprot.writeI32(struct.uids.size());
          for (String _iter168 : struct.uids)
          {
            oprot.writeString(_iter168);
          }
        }
      }
      if (struct.isSetReasonCode()) {
        oprot.writeI16(struct.reasonCode);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaRequestUnlmtUptUser struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.authMsg = new AuthorizeMsg();
        struct.authMsg.read(iprot);
        struct.setAuthMsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appkey = iprot.readString();
        struct.setAppkeyIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list169 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.uids = new ArrayList<String>(_list169.size);
          String _elem170;
          for (int _i171 = 0; _i171 < _list169.size; ++_i171)
          {
            _elem170 = iprot.readString();
            struct.uids.add(_elem170);
          }
        }
        struct.setUidsIsSet(true);
      }
      if (incoming.get(3)) {
        struct.reasonCode = iprot.readI16();
        struct.setReasonCodeIsSet(true);
      }
    }
  }

}

