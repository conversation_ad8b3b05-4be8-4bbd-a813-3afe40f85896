package com.yy.manager.thrift.client;

import com.yy.java.component.util.JsonUtils;
import com.yy.manager.exception.SuperException;
import com.yy.manager.thrift.fts_pendant.FtsPendantService;
import com.yy.manager.thrift.fts_pendant.PendantConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/26 11:54
 */
@Component
@Slf4j
public class FtsPendantServiceClient {

    @Reference(protocol = "nythrift", registry = "yrpc-reg", owner = "${thrift_fts_pendant_client_s2s_name}")
    private FtsPendantService.Iface proxy;

    /**
     * 获取活动挂件列表
     *
     * @param actId 活动ID
     */
    public List<PendantConfig> getPendantByActId(Long actId) {

        try {
            if (actId == null || actId <= 0) {
                return List.of();
            }
            var rsp = proxy.GetPendantByActID(actId);
            if (rsp != null && rsp.header != null) {
                if (rsp.header.code == 0) {
                    return rsp.getConfig();
                }
                throw new SuperException(rsp.header.getMessage(), (int) rsp.header.getCode());
            }
            return List.of();
        } catch (SuperException e) {
            log.error("getPendantByActId bizFailed actId:{} msg:{}", actId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("getPendantByActId failed actId:{} msg:{}", actId, e.getMessage(), e);
            throw new SuperException(e.getMessage(), 500);
        }
    }

    /**
     * 添加活动挂件
     */
    public void addPendant(Long opUid, PendantConfig config) {
        try {
            opUid = opUid == null ? 0 : opUid;
            log.info("addPendant opUid:{} config:{}", opUid, JsonUtils.serialize(config));
            var rsp = proxy.AddPendant(opUid, config);
            if (rsp != null) {
                if (rsp.code == 0) {
                    return;
                }
                throw new SuperException(rsp.getMessage(), (int) rsp.getCode());
            }
        } catch (SuperException e) {
            log.error("addPendant bizFailed opUid:{} config:{} msg:{}", opUid, JsonUtils.serialize(config), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("addPendant failed opUid:{} config:{} msg:{}", opUid, JsonUtils.serialize(config), e.getMessage(), e);
            throw new SuperException(e.getMessage(), 500);
        }
    }

    /**
     * 更新活动挂件信息
     */
    public void updatePendant(Long opUid, PendantConfig config) {
        try {
            opUid = opUid == null ? 0 : opUid;
            log.info("updatePendant opUid:{} config:{}", opUid, JsonUtils.serialize(config));
            var rsp = proxy.UpdatePendant(opUid, config);
            if (rsp != null) {
                if (rsp.code == 0) {
                    return;
                }
                throw new SuperException(rsp.getMessage(), (int) rsp.getCode());
            }
        } catch (SuperException e) {
            log.error("updatePendant bizFailed opUid:{} config:{} msg:{}", opUid, JsonUtils.serialize(config), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("updatePendant failed opUid:{} config:{} msg:{}", opUid, JsonUtils.serialize(config), e.getMessage(), e);
            throw new SuperException(e.getMessage(), 500);
        }
    }

    /**
     * 删除活动挂件
     *
     * @param opUid 操作人uid
     * @param actId 活动id
     */
    public void deletePendantByActId(Long opUid, Long actId) {
        opUid = opUid == null ? 0 : opUid;
        actId = actId == null ? 0 : actId;
        log.info("deletePendantByActId opUid:{} actId:{}", opUid, actId);
        try {
            if (actId <= 0) {
                return;
            }
            var rsp = proxy.DeletePendantByActID(opUid, actId);
            if (rsp != null) {
                if (rsp.code == 0) {
                    return;
                }
                throw new SuperException(rsp.getMessage(), (int) rsp.getCode());
            }
        } catch (SuperException e) {
            log.error("deletePendantByActId bizFailed opUid:{} actId:{} msg:{}", opUid, actId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("deletePendantByActId failed opUid:{} actId:{} msg:{}", opUid, actId, e.getMessage(), e);
            throw new SuperException(e.getMessage(), 500);
        }
    }
}
