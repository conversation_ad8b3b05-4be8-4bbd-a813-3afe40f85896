package com.yy.manager.deploy.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.common.utils.SysEnvHelper;
import com.yy.manager.datasource.database.DataSourceSelector;
import com.yy.manager.deploy.entity.ActConfigTable;
import com.yy.manager.deploy.mapper.ActConfigTableMapper;
import com.yy.manager.deploy.vo.ComparingRow;
import com.yy.manager.deploy.vo.ComparingTable;
import com.yy.manager.deploy.vo.ModifyStatement;
import com.yy.manager.ecology.entity.HdzjComponent;
import com.yy.manager.ecology.service.IHdzjComponentService;
import com.yy.manager.hdztmanager.mapper.CommonMapper;
import com.yy.manager.template.enums.DB;
import com.yy.manager.template.util.DSUtils;
import com.yy.manager.utils.ActCfgUtils;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.SqlCommandType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Service
public class ConfigDeployService {

    @Resource
    private ActConfigTableMapper actConfigTableMapper;

    @Autowired
    private IHdzjComponentService hdzjComponentService;

    @Resource
    private CommonMapper commonMapper;

    public Response<LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> getDbConfig(int actId, String db, String env) {
        if (StringUtils.isBlank(env) || SysEnvHelper.getEnv().equals(env)) {
            try {
                LinkedHashMap<String, List<LinkedHashMap<String, Object>>> dbConfig = fetchDbConfig(actId, db, null);
                return Response.success(dbConfig);
            } catch (IllegalArgumentException e) {
                return Response.fail(400, e.getMessage());
            }
        }

        return getRemoteDbConfig(actId, db, env);
    }

    public Response<LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> getRemoteDbConfig(int actId, String db, String env) {
        if (SysEnvHelper.getEnv().equals(env)) {
            return Response.fail(400, "invalid env");
        }

        if (!SysEnvHelper.DEPLOY.equals(env) && !SysEnvHelper.TEST.equals(env)) {
            return Response.fail(400, "invalid env");
        }

        String host = SysEnvHelper.TEST.equals(env) ? Const.TEST_HOST : Const.PROD_HOST;
        String url = "https://" + host + "/deploy/config?actId=" + actId + "&db=" + db;
        String resp = HttpUtil.get(url, StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(resp)) {
            return Response.fail(500, "could not get remote dbConfig");
        }

        return JSON.parseObject(resp, new TypeReference<Response<LinkedHashMap<String, List<LinkedHashMap<String, Object>>>>>(){});
    }

    /**
     *
     * @param actId
     * @param db
     * @return table_name -> table_data
     */
    public LinkedHashMap<String, List<LinkedHashMap<String, Object>>> fetchDbConfig(int actId, String db, List<ActConfigTable> configTables) {
        if (StringUtils.isEmpty(db)) {
            throw new IllegalArgumentException("db cannot be empty");
        }

        if (configTables == null) {
            QueryWrapper<ActConfigTable> wrapper = new QueryWrapper<>();
            wrapper.eq("db", db);
            wrapper.orderByAsc("tbl");
            configTables = actConfigTableMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(configTables)) {
                return new LinkedHashMap<>(0);
            }
        }

        LinkedHashMap<String, List<LinkedHashMap<String, Object>>> dbConfig = new LinkedHashMap<>(configTables.size());
        try (DataSourceSelector ignored = DataSourceSelector.doWithActId(db, actId)) {
            for (ActConfigTable configTable : configTables) {
                String condition;
                if (DB.component.name().equals(db)) {
                    condition = getComponentDefineCondition(actId);
                } else {
                    condition = configTable.getSqlCondition();
                    condition = condition.replace("{actId}", String.valueOf(actId));
                }

                if (StringUtils.isNotBlank(configTable.getOrderCondition())) {
                    condition += (" " + configTable.getOrderCondition());
                }

                List<LinkedHashMap<String, Object>> data = commonMapper.commonSelect(configTable.getTbl(), condition, (long) actId);
                dbConfig.put(configTable.getTbl(), data);
            }
        }

        return dbConfig;
    }

    public String getComponentDefineCondition(int actId) {
        List<HdzjComponent> components = hdzjComponentService.list(actId, new QueryWrapper<>().eq("act_id", actId));
        if (CollectionUtils.isEmpty(components)) {
            return "1<>1";
        }

        List<Integer> cmptIds = components.stream().map(HdzjComponent::getCmptId).distinct().toList();

        return  "cmpt_id in (" + StringUtils.join(cmptIds, ',') + ")";
    }

    public Response<List<ComparingTable>> genComparingTables(int actId, String db, String env) {
        Response<LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> resp = getRemoteDbConfig(actId, db, env);
        if (resp == null || !resp.success()) {
            return Response.fail(400, "could not get remote config: " + (resp != null ? resp.getReason() : "response is null"));
        }
        QueryWrapper<ActConfigTable> wrapper = new QueryWrapper<>();
        wrapper.eq("db", db);
        wrapper.orderByAsc("tbl");
        List<ActConfigTable> configTables = actConfigTableMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(configTables)) {
            return Response.success(Collections.emptyList());
        }

        LinkedHashMap<String, List<LinkedHashMap<String, Object>>> remoteConfig = resp.getData();
        LinkedHashMap<String, List<LinkedHashMap<String, Object>>> baseConfig = fetchDbConfig(actId, db, configTables);

        List<ComparingTable> comparingTables = new ArrayList<>(configTables.size());
        for (ActConfigTable configTable : configTables) {
            List<LinkedHashMap<String, Object>> baseTable = baseConfig.getOrDefault(configTable.getTbl(), Collections.emptyList());
            List<LinkedHashMap<String, Object>> remoteTable = remoteConfig.getOrDefault(configTable.getTbl(), Collections.emptyList());
            if (baseTable.isEmpty() && remoteTable.isEmpty()) {
                continue;
            }

            List<ComparingRow> comparingRows = genComparingRows(configTable, baseTable, remoteTable);
            int diffSize = (int) comparingRows.stream().filter(row -> row.getModifyStatement() != null).count();
            ComparingTable table = new ComparingTable();
            table.setKey(configTable.getTbl());
            table.setTbl(configTable.getTbl());
            table.setBaseSize(baseTable.size());
            table.setRemoteSize(remoteTable.size());
            table.setDiffSize(diffSize);
            table.setComparingRows(comparingRows);

            comparingTables.add(table);
        }

        return Response.success(comparingTables);
    }

    public List<ComparingRow> genComparingRows(ActConfigTable configTable, List<LinkedHashMap<String, Object>> baseTable, List<LinkedHashMap<String, Object>> remoteTable) {
        List<String> keyColumns = configTable.getKeyColumns();
        int maxSize = Math.max(baseTable.size(), remoteTable.size());
        if (maxSize == 0) {
            return Collections.emptyList();
        }
        Set<String> intersection = new HashSet<>(maxSize);
        List<ComparingRow> result = new ArrayList<>(maxSize);
        if (CollectionUtils.isNotEmpty(remoteTable)) {
            for (LinkedHashMap<String, Object> remoteRow : remoteTable) {
                LinkedHashMap<String, Object> rowKey = ActCfgUtils.getKey(remoteRow, keyColumns);
                ComparingRow row = new ComparingRow();
                result.add(row);
                row.setKey(configTable.getTbl() + '|' + ActCfgUtils.toStrKey(rowKey));
                row.setTbl(configTable.getTbl());
                row.setRowKey(rowKey);
                row.setRemoteRow(remoteRow);
                LinkedHashMap<String, Object> baseRow = ActCfgUtils.findByCondition(baseTable, rowKey);
                row.setBaseRow(baseRow);
                if (baseRow == null) {
                    Map<String, Object> params = new LinkedHashMap<>(remoteRow);
                    if (configTable.getAutoKey() == 1) {
                        for (String keyColumn : keyColumns) {
                            params.remove(keyColumn);
                        }
                    }
                    ModifyStatement statement = new ModifyStatement(configTable.getTbl(), SqlCommandType.INSERT, params, rowKey);
                    row.setModifyStatement(statement);
                    continue;
                }

                String strKey = ActCfgUtils.toStrKey(rowKey);
                intersection.add(strKey);

                Map<String, Object> diffColumns = ActCfgUtils.getDiffColumns(baseRow, remoteRow, configTable.getIgnoreColumns());
                if (!diffColumns.isEmpty() && configTable.getAutoKey() != 1) {
                    ModifyStatement statement = new ModifyStatement(configTable.getTbl(), SqlCommandType.UPDATE, diffColumns, rowKey);
                    row.setModifyStatement(statement);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(baseTable)) {
            for (LinkedHashMap<String, Object> baseRow : baseTable) {
                LinkedHashMap<String, Object> rowKey = ActCfgUtils.getKey(baseRow, keyColumns);
                String strKey = ActCfgUtils.toStrKey(rowKey);
                if (intersection.contains(strKey)) {
                    continue;
                }

                ComparingRow row = new ComparingRow();
                result.add(row);
                row.setKey(configTable.getTbl() + '|' + ActCfgUtils.toStrKey(rowKey));
                row.setTbl(configTable.getTbl());
                row.setRowKey(rowKey);
                row.setBaseRow(baseRow);
                if (configTable.getAutoKey() != 1) {
                    ModifyStatement statement = new ModifyStatement(configTable.getTbl(), SqlCommandType.DELETE, null, rowKey);
                    row.setModifyStatement(statement);
                }
            }
        }

        return result;
    }

    @DSTransactional
    public Response<Integer> executeStatements(int actId, String db, List<ModifyStatement> statements) {
        int rs = 0;
        try (DataSourceSelector ignore = DataSourceSelector.doWithActId(db, actId)) {
            for (ModifyStatement statement : statements) {
                int effected = switch (statement.getModifyType()) {
                    case INSERT ->  commonMapper.commonInsertByMap(statement.getTbl(), statement.getParams());
                    case UPDATE ->  commonMapper.commonUpdateByMap(statement.getTbl(), statement.getParams(), statement.getConditions());
                    case DELETE ->  commonMapper.commonDeleteByMap(statement.getTbl(), statement.getConditions());
                    default -> throw new RuntimeException("unsupported modifyType");
                };

                if (effected > 1) {
                    throw new RuntimeException("statement execution effected unexpected");
                }

                rs += effected;
            }
        }

        return Response.success(rs);
    }
}
