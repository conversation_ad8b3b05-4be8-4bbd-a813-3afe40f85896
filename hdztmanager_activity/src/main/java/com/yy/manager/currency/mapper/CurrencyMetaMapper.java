package com.yy.manager.currency.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DS("currency")
public interface CurrencyMetaMapper {

    @Select("select cid from currency_meta where hdpt_act_id = #{actId}")
    List<String> selectActivityCidList(@Param("actId") long actId);
}
