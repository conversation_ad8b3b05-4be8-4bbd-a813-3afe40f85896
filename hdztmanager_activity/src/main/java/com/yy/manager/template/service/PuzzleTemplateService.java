package com.yy.manager.template.service;

import cn.hutool.core.lang.id.NanoId;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.common.utils.StringUtils;
import com.yy.manager.acttemplate.entity.TemplateActInfo;
import com.yy.manager.acttemplate.entity.TemplateInfo;
import com.yy.manager.acttemplate.mapper.TemplateActInfoMapper;
import com.yy.manager.acttemplate.mapper.TemplateInfoMapper;
import com.yy.manager.template.entity.TemplateAttrDefine;
import com.yy.manager.template.entity.TemplateAttrValue;
import com.yy.manager.template.mapper.TemplateAttrDefineMapper;
import com.yy.manager.template.mapper.TemplateAttrValueMapper;
import com.yy.manager.template.vo.*;
import com.yy.manager.utils.DateUtil;
import com.yy.manager.utils.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PuzzleTemplateService {

    @Value("${puzzle.secret-key:xx}")
    private String secretKey;

    @Value("${puzzle.publish.callback.url:https://api-test-outer.baizhanlive.com/sdk/activity/template/storage/storage/bizComponent/callback/releaseResult}")
    private String publishCallbackUrl;

    @Resource
    private TemplateInfoMapper templateInfoMapper;

    @Resource
    private TemplateAttrDefineMapper templateAttrDefineMapper;

    @Resource
    private TemplateAttrValueMapper templateAttrValueMapper;

    @Resource
    private TemplateActInfoMapper templateActInfoMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HdptConfigTemplateService hdptConfigTemplateService;

    public Pair<Integer, String> validTemplateAttrConfig(long puzzleActId, String templateId, String actId, List<TemplateAttrVal> attrs) {
        TemplateInfo templateInfo = templateInfoMapper.selectByPrimaryKey(templateId);
        if (templateInfo == null) {
            return Pair.of(400, "模板ID错误，模板不存在");
        }

        TemplateActInfo templateActInfo = templateActInfoMapper.selectByPuzzleActId(puzzleActId);
        if (templateActInfo != null) {
            if (!StringUtils.equals(templateId, templateActInfo.getTemplateId()) || !StringUtils.equals(actId, templateActInfo.getActId())) {
                return Pair.of(400, "模板ID、活动ID对应错误");
            }
        }

        List<TemplateAttrDefine> defines = templateAttrDefineMapper.selectTemplateAttrDefines(templateId);
        if (CollectionUtils.isEmpty(defines)) {
            return null;
        }

        if (CollectionUtils.isEmpty(attrs)) {
            return Pair.of(400, "模板属性缺失");
        }

        Map<String, String> attrMap = attrs.stream().collect(Collectors.toMap(attr -> String.format("%s:%s:%s:%d", attr.getDb(), attr.getTbl(), attr.getCol(), attr.getRowIndex()), TemplateAttrVal::getAttrValue, (k1, k2) -> k1));
        for (TemplateAttrDefine define : defines) {
            if (define.getRequired()) {
                String key = String.format("%s:%s:%s:%d", define.getDb(), define.getTbl(), define.getCol(), define.getRowIndex());
                if (StringUtils.isEmpty(attrMap.get(key))) {
                    return Pair.of(400, "模板属性缺失：" + key);
                }
            }
        }

        return null;
    }

    public PuzzleResponse<PuzzleChangeListVo> prePublishPuzzleConfig(PuzzleConfig puzzleConfig) {
        List<PuzzleComponentConfig> componentList = puzzleConfig.getComponentList();
        if (CollectionUtils.isEmpty(componentList)) {
            return PuzzleResponse.fail(400, "组件配置不可为空");
        }
        String componentData = componentList.get(0).getComponentData();
        if (!StringUtils.startsWith(componentData, StringUtils.OPEN_BRACE)) {
            return PuzzleResponse.fail(400, "组件配置不可为空");
        }
        PuzzleComponentServerConfig componentConfig = JSON.parseObject(componentData, PuzzleComponentServerConfig.class);
        PuzzleComponentServerConfig.PuzzleTemplateConfig templateConfig = componentConfig.getBizComponentServerConfig();
        final String templateId = templateConfig.getTemplateId(), actId = templateConfig.getActId();
        TemplateInfo templateInfo = templateInfoMapper.selectByPrimaryKey(templateId);
        if (templateInfo == null) {
            return PuzzleResponse.fail(400, "模板ID错误，模板不存在");
        }

        PuzzleChangeListVo data = new PuzzleChangeListVo();
        TemplateActInfo templateActInfo = templateActInfoMapper.selectByPuzzleActId(puzzleConfig.getActId());
        List<String> changeList = new ArrayList<>(16);
        if (templateActInfo != null) {
            if (!StringUtils.equals(templateId, templateActInfo.getTemplateId()) || !StringUtils.equals(actId, templateActInfo.getActId())) {
                return PuzzleResponse.fail(400, "模板ID、活动ID对应错误");
            }

            if (templateActInfo.getBeginTime().getTime() != puzzleConfig.getStartTime().getTime()) {
                changeList.add("活动开始时间调整为：" + DateFormatUtils.format(puzzleConfig.getStartTime(), DateUtil.DEFAULT_PATTERN));
            }

            if (templateActInfo.getEndTime().getTime() != puzzleConfig.getEndTime().getTime()) {
                changeList.add("活动结束时间调整为：" + DateFormatUtils.format(puzzleConfig.getEndTime(), DateUtil.DEFAULT_PATTERN));
            }

            if (!StringUtils.equals(templateActInfo.getActName(), templateConfig.getActName())) {
                changeList.add("活动名称修改为：" + templateConfig.getActName());
            }

            // 属性更新
            QueryWrapper<TemplateAttrValue> wrapper = new QueryWrapper<>();
            wrapper.eq("actId", actId);
            wrapper.eq("templateId", templateId);
            List<TemplateAttrValue> attrValues = templateAttrValueMapper.selectList(wrapper);
            Map<String, String> attrMap = attrValues.stream().collect(Collectors.toMap(attrValue -> String.format("%s:%s:%s:%d", attrValue.getDb(), attrValue.getTbl(), attrValue.getCol(), attrValue.getRowIndex()), TemplateAttrValue::getAttrValue));

            for (TemplateAttrVal attrVal : templateConfig.getAttrs()) {
                String key = String.format("%s:%s:%s:%d", attrVal.getDb(), attrVal.getTbl(), attrVal.getCol(), attrVal.getRowIndex());
                if (!StringUtils.equals(attrMap.get(key), attrVal.getAttrValue())) {
                    changeList.add("修改属性值：" + attrMap.get(key) + " => " + attrVal.getAttrValue());
                }
            }

            data.setChangeListMap(Map.of("2001", changeList));
            return PuzzleResponse.success(data);
        }

        data.setChangeListMap(Map.of("2001", Collections.singletonList("首次基于模板："+ templateId +"配置活动：" + actId)));
        return PuzzleResponse.success(data);
    }

    @DSTransactional
    public void savePuzzleConfig(long uid, PuzzlePublishEvent event) {
        PuzzlePublishData publishData = event.getData();
        PuzzleComponentConfig componentConfig = publishData.getComponentList().stream().filter(c -> c.getType() == 2001).findFirst().orElseThrow();
        String componentData = componentConfig.getComponentData();
        PuzzleComponentServerConfig componentServerConfig = JSON.parseObject(componentData, PuzzleComponentServerConfig.class);
        PuzzleComponentServerConfig.PuzzleTemplateConfig templateConfig = componentServerConfig.getBizComponentServerConfig();
        final String templateId = templateConfig.getTemplateId(), actId = templateConfig.getActId();
        TemplateInfo templateInfo = templateInfoMapper.selectByPrimaryKey(templateId);
        if (templateInfo == null) {
            throw new RuntimeException("savePuzzleConfig fail template not exist");
        }

        Response<?> response = hdptConfigTemplateService.doSaveTemplateActivityInfo(uid, templateId, actId, publishData.getActName(), publishData.getStartTime(), publishData.getEndTime(), true, templateConfig.getAttrs());
        if (!response.success()) {
            throw new RuntimeException("savePuzzleConfig fail do save fail");
        }

        // 回调
        PuzzlePublishResult publishResult = new PuzzlePublishResult();
        publishResult.setActId(publishData.getActId());
        publishResult.setAuditVersion(publishData.getAuditVersion());
        publishResult.setComponentIds(List.of(componentConfig.getId()));

        PuzzleRequest request = new PuzzleRequest();
        request.setBizId("interactiveAct");
        request.setTraceId(NanoId.randomNanoId(36));
        request.setTimestamp(System.currentTimeMillis());
        request.setReqData(JSON.toJSONString(publishResult));
        request.setSign(getSign(request));

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        HttpEntity<PuzzleRequest> entity = new HttpEntity<>(request, headers);
        PuzzleResponse<?> resp = restTemplate.postForObject(publishCallbackUrl, entity, PuzzleResponse.class);
        log.info("savePuzzleConfig callback with actId:{} rs:{}", actId, resp);
    }

    public String getSign(PuzzleRequest request) {
        return new HmacUtils(HmacAlgorithms.HMAC_SHA_256, secretKey).hmacHex(buildSignStr(request));
    }

    private static String buildSignStr(PuzzleRequest request) {
        StringJoiner joiner = new StringJoiner(StringUtils.AND);
        if (StringUtils.isNotEmpty(request.getBizId())) {
            joiner.add("bizId=" + request.getBizId());
        }

        if (StringUtils.isNotEmpty(request.getReqData())) {
            joiner.add("reqData=" + request.getReqData());
        }

        joiner.add("timestamp=" + request.getTimestamp());

        if (StringUtils.isNotEmpty(request.getTraceId())) {
            joiner.add("traceId=" + request.getTraceId());
        }

        return joiner.toString();
    }
}
