package com.yy.manager.template.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yy.common.utils.StringUtils;
import com.yy.manager.acttemplate.entity.TemplateInfo;
import com.yy.manager.acttemplate.mapper.TemplateInfoMapper;
import com.yy.manager.template.entity.TemplateAttrDefine;
import com.yy.manager.template.entity.TemplateAttrTemplate;
import com.yy.manager.template.mapper.TemplateAttrDefineMapper;
import com.yy.manager.template.mapper.TemplateAttrTemplateMapper;
import com.yy.manager.template.vo.GiftInfo;
import com.yy.manager.thrift.client.TPropsInfoServiceClient;
import com.yy.manager.thrift.client.TurnoverCommonServiceClient;
import com.yy.manager.thrift.hdzt.ranking.BusiId;
import com.yy.manager.thrift.to_service.TAppId;
import com.yy.manager.thrift.to_service.TPropsMeta;
import com.yy.manager.utils.PageResponse;
import com.yy.manager.utils.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TemplateConfigMgrService {

    @Resource
    private TemplateInfoMapper templateInfoMapper;

    @Resource
    private TemplateAttrDefineMapper templateAttrDefineMapper;

    @Resource
    private TemplateAttrTemplateMapper templateAttrTemplateMapper;


    public PageResponse<TemplateInfo> queryPageTemplateInfo(String templateId, String templateName, String baseActId, Integer state, int pageNo, int pageSize) {
        QueryWrapper<TemplateInfo> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(templateName)) {
            wrapper.eq("id", templateId);
        }

        if (StringUtils.isNotEmpty(templateName)) {
            wrapper.like("name", templateName);
        }

        if (StringUtils.isNotEmpty(baseActId)) {
            wrapper.eq("base_act_id", baseActId);
        }

        if (state != null) {
            wrapper.eq("state", state);
        }

        Page<TemplateInfo> page = new Page<>(pageNo, pageSize);

        Page<TemplateInfo> p = templateInfoMapper.selectPage(page, wrapper);
        PageResponse<TemplateInfo> result = new PageResponse<>();
        result.setTotal(p.getTotal());
        result.setData(p.getRecords());
        return result;
    }

    public Response<List<TemplateAttrTemplate>> queryTemplateAttrTemplates(String templateId) {
        QueryWrapper<TemplateAttrTemplate> wrapper = new QueryWrapper<>();
        wrapper.eq("template_id", templateId);
        List<TemplateAttrTemplate> templates = templateAttrTemplateMapper.selectList(wrapper);
        return Response.success(templates);
    }

    public Response<Integer> addTemplateAttrTemplate(TemplateAttrTemplate template) {
        TemplateInfo templateInfo = templateInfoMapper.selectById(template.getTemplateId());
        if (templateInfo == null) {
            return Response.fail(400, "template not exist!");
        }

        TemplateAttrTemplate exist = templateAttrTemplateMapper.selectByPrimaryKey(template.getTemplateId(), template.getDb(), template.getTbl(), template.getCol(), template.getRowIndex());
        if (exist != null) {
            return Response.fail(400, "attr template already exist!");
        }

        int rs = templateAttrTemplateMapper.insert(template);
        log.info("addTemplateAttrTemplate with template:{}, rs:{}", template, rs);
        return Response.success(rs);
    }

    public Response<Integer> updateTemplateAttrTemplate(TemplateAttrTemplate template) {
        TemplateAttrTemplate exist = templateAttrTemplateMapper.selectByPrimaryKey(template.getTemplateId(), template.getDb(), template.getTbl(), template.getCol(), template.getRowIndex());
        if (exist == null) {
            return Response.fail(400, "attr template not exist!");
        }

        int rs = templateAttrTemplateMapper.updateByPrimaryKeySelective(template);
        log.info("updateTemplateAttrTemplate with template:{}, rs:{}", template, rs);
        return Response.success(rs);
    }

    public Response<Integer> deleteTemplateAttrTemplate(String templateId, String db, String tbl, String col, Integer rowIndex) {
        TemplateAttrTemplate exist = templateAttrTemplateMapper.selectByPrimaryKey(templateId, db, tbl, col, rowIndex);
        if (exist == null) {
            return Response.fail(400, "attr template not exist!");
        }

        int rs = templateAttrTemplateMapper.deleteByPrimaryKey(templateId, db, tbl, col, rowIndex);
        log.info("deleteTemplateAttrTemplate with :{} {} {} {} {}, rs:{}", templateId, db, tbl, col, rowIndex, rs);
        return Response.success(rs);
    }

    public Response<List<TemplateAttrDefine>> queryTemplateAttrDefines(String templateId) {
        QueryWrapper<TemplateAttrDefine> wrapper = new QueryWrapper<>();
        wrapper.eq("template_id", templateId);
        List<TemplateAttrDefine> templates = templateAttrDefineMapper.selectList(wrapper);
        return Response.success(templates);
    }

    public Response<Integer> addTemplateAttrDefine(TemplateAttrDefine template) {
        TemplateInfo templateInfo = templateInfoMapper.selectById(template.getTemplateId());
        if (templateInfo == null) {
            return Response.fail(400, "template not exist!");
        }

        TemplateAttrDefine exist = templateAttrDefineMapper.selectByPrimaryKey(template.getTemplateId(), template.getDb(), template.getTbl(), template.getCol(), template.getRowIndex());
        if (exist != null) {
            return Response.fail(400, "attr template already exist!");
        }

        int rs = templateAttrDefineMapper.insert(template);
        log.info("addTemplateAttrDefine with template:{}, rs:{}", template, rs);
        return Response.success(rs);
    }

    public Response<Integer> updateTemplateAttrDefine(TemplateAttrDefine template) {
        TemplateAttrDefine exist = templateAttrDefineMapper.selectByPrimaryKey(template.getTemplateId(), template.getDb(), template.getTbl(), template.getCol(), template.getRowIndex());
        if (exist == null) {
            return Response.fail(400, "attr template not exist!");
        }

        int rs = templateAttrDefineMapper.updateByPrimaryKeySelective(template);
        log.info("updateTemplateAttrDefine with template:{}, rs:{}", template, rs);
        return Response.success(rs);
    }

    public Response<Integer> deleteTemplateAttrDefine(String templateId, String db, String tbl, String col, Integer rowIndex) {
        TemplateAttrDefine exist = templateAttrDefineMapper.selectByPrimaryKey(templateId, db, tbl, col, rowIndex);
        if (exist == null) {
            return Response.fail(400, "attr template not exist!");
        }

        int rs = templateAttrDefineMapper.deleteByPrimaryKey(templateId, db, tbl, col, rowIndex);
        log.info("deleteTemplateAttrDefine with :{} {} {} {} {}, rs:{}", templateId, db, tbl, col, rowIndex, rs);
        return Response.success(rs);
    }




}
