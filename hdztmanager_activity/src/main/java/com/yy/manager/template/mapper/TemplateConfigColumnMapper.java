package com.yy.manager.template.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.template.entity.TemplateConfigColumn;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("hdpttemplate")
public interface TemplateConfigColumnMapper extends BaseMapper<TemplateConfigColumn> {

    List<TemplateConfigColumn> selectTemplateConfigColumns(@Param("valueSource") int valueSource);

    List<TemplateConfigColumn> selectAllTemplateConfigColumns();
}
