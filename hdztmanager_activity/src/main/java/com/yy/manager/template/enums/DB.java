package com.yy.manager.template.enums;

import com.yy.manager.datasource.database.GameecologyDataSourceContextHolder;
import com.yy.manager.datasource.database.HdztDataSourceContextHolder;

public enum DB {

    hdzk, hdzt, stream, material, currency, component, shop, template;

    public String getDS(long actId) {
        if (this == component) {
            return "gameecology";
        }
        if (this == stream) {
            return "hdztstream";
        }

        if (this == material) {
            return "hdptmaterials";
        }

        if (this == currency) {
            return "currency";
        }

        if (this == shop) {
            return "shop";
        }

        if (this == template) {
            return "hdpttemplate";
        }

        if (this == hdzk) {
            return GameecologyDataSourceContextHolder.getDataSource(actId).getDs();
        }

        return HdztDataSourceContextHolder.getHdztDataSource(actId).getDs();
    }

    public static DB parse(String db) {
        if ("gameecology".equals(db)) {
            return hdzk;
        }

        return DB.valueOf(db);
    }
}
