package com.yy.manager.template.vo;

import lombok.Data;

import java.util.Date;

@Data
public class CopyActivityRequest {

    /** 操作者UID */
    protected long opUid;

    /**
     * 源活动ID
     */
    protected int baseActId;

    /**
     * 新活动ID
     */
    protected int actId;

    /**
     * 新活动名称
     */
    protected String actName;

    /**
     * 活动备注
     */
    protected String remark;

    /**
     * 新活动开始时间
     */
    protected Date beginTime;

    /**
     * 新活动结束时间
     */
    protected Date endTime;

    /**
     * 指定模板templateId，可不传
     */
    protected String templateId;

    /**
     * astro projectId
     */
    private long projectId;
}
