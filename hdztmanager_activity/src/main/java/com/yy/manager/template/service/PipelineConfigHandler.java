package com.yy.manager.template.service;

import com.yy.manager.template.entity.TemplateConfigColumn;
import com.yy.manager.template.handler.*;
import com.yy.manager.template.mapper.TemplateConfigColumnMapper;
import com.yy.manager.template.vo.TransferContext;
import com.yy.manager.template.vo.UpdateStatement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * pipeline: GeneralHandler -> IntervalDateHandler -> ManualAttrHandler -> AutoIdHandler -> ActAutoIdHandler -> GeneratedIdHandler -> ForeignKeyHandler -> TemplateAttrHandler ->
 */
@Component
@Slf4j
public class PipelineConfigHandler {

    @Autowired
    private List<IConfigHandler> handlers;

    @Resource
    private TemplateConfigColumnMapper templateConfigColumnMapper;

    public void addTransfer(TransferContext context, Map<String, LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> dbConfigs) {
        fillTemplateConfigColumn(context);
        for (IConfigHandler handler : handlers) {
            long startTime = System.currentTimeMillis();
            handler.handle(context, dbConfigs, null);
            log.info("addTransfer with handler:{} cost:{}ms", handler.getClass().getSimpleName(), System.currentTimeMillis() - startTime);
        }
    }

    public List<UpdateStatement> updateTransfer(TransferContext context, Map<String, LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> dbConfigs) {
        fillTemplateConfigColumn(context);
        List<UpdateStatement> updateStatements = new ArrayList<>(50);
        for (IConfigHandler handler : handlers) {
            handler.handle(context, dbConfigs, updateStatements);
        }

        return updateStatements;
    }

    private void fillTemplateConfigColumn(TransferContext context) {
        var templateConfigColumns = templateConfigColumnMapper.selectAllTemplateConfigColumns();
        Map<Integer, List<TemplateConfigColumn>> valueSource2templateConfigColumnMap = templateConfigColumns.stream().collect(Collectors.groupingBy(TemplateConfigColumn::getValueSource));
        context.setTemplateConfigColumns(valueSource2templateConfigColumnMap);
    }
}
