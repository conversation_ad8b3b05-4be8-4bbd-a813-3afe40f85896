package com.yy.manager.template.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.template.entity.TemplateAttrTemplate;

@DS("hdpttemplate")
public interface TemplateAttrTemplateMapper extends BaseMapper<TemplateAttrTemplate> {

    TemplateAttrTemplate selectByPrimaryKey(String templateId, String db, String tbl, String col, int rowIndex);

    int deleteByPrimaryKey(String templateId, String db, String tbl, String col, int rowIndex);

    int updateByPrimaryKeySelective(TemplateAttrTemplate template);
}
