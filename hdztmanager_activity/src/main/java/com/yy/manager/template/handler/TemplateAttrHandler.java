package com.yy.manager.template.handler;

import cn.hutool.core.bean.BeanPath;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.common.utils.StringUtils;
import com.yy.manager.acttemplate.entity.TemplateActInfo;
import com.yy.manager.template.entity.TemplateAttrTemplate;
import com.yy.manager.template.entity.TemplateConfigColumn;
import com.yy.manager.template.enums.ValueSource;
import com.yy.manager.template.exception.HandleException;
import com.yy.manager.template.mapper.TemplateAttrTemplateMapper;
import com.yy.manager.template.vo.TransferContext;
import com.yy.manager.template.vo.UpdateStatement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 模板替换
 */
@Slf4j
@Component("templateAttrHandler")
public class TemplateAttrHandler implements IConfigHandler {

    private static final Pattern PATTERN = Pattern.compile("\\$\\$\\{([^}]+)}");

    @Resource
    private TemplateAttrTemplateMapper templateAttrTemplateMapper;

    @Override
    public void handle(TransferContext context, Map<String, LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> dbConfigs, List<UpdateStatement> updateStatements) {
        List<TemplateConfigColumn> templateConfigColumns = context.findTemplateConfigColumns(ValueSource.template);
        if (CollectionUtils.isNotEmpty(templateConfigColumns)) {
            for (TemplateConfigColumn column : templateConfigColumns) {
                try {
                    render(column.getDb(), column.getTbl(), column.getCol(), -1, null, column.getTemplate(), context.getTemplateActInfo(), dbConfigs, updateStatements);
                } catch (Exception e) {
                    throw new HandleException(e, "TemplateAttrHandler$Column", column.getDb(), column.getTbl(), column.getCol(), -1);
                }
            }
        }

        QueryWrapper<TemplateAttrTemplate> wrapper = new QueryWrapper<>();
        wrapper.eq("template_id", context.getTemplateActInfo().getTemplateId());
        List<TemplateAttrTemplate> templates = templateAttrTemplateMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(templates)) {
            return;
        }

        for (TemplateAttrTemplate template : templates) {
            try {
                render(template.getDb(), template.getTbl(), template.getCol(), template.getRowIndex(), template.getTemplateCondition(), template.getTemplate(), context.getTemplateActInfo(), dbConfigs, updateStatements);
            } catch (Exception e) {
                throw new HandleException(e, "TemplateAttrHandler$Template", template.getDb(), template.getTbl(), template.getCol(), template.getRowIndex());

            }
        }
    }


    private void render(String db, String tbl, String col, int rowIndex, String condition, String template, TemplateActInfo templateActInfo, Map<String, LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> dbConfigs, List<UpdateStatement> updateStatements) {
        LinkedHashMap<String, List<LinkedHashMap<String, Object>>> dbConfig = dbConfigs.get(db);
        if (MapUtils.isEmpty(dbConfig)) {
            return;
        }

        List<LinkedHashMap<String, Object>> tableConfig = dbConfig.get(tbl);
        if (CollectionUtils.isEmpty(tableConfig)) {
            return;
        }

        String templateValue = template;
        List<String> keys = ReUtil.findAll(PATTERN, templateValue, 1);
        if (CollectionUtils.isNotEmpty(keys)) {
            Map<String, String> replace = new HashMap<>(keys.size());
            for (String key : keys) {
                String placeholder = "$${" + key + "}";
                if (replace.containsKey(placeholder)) {
                    continue;
                }
                Object value;
                // hdzt.ranking_item_transform[0].busi_item_id 类型的json path表达式
                if (key.contains(StringUtils.DOT)) {
                    BeanPath resolver = new BeanPath(key);
                    value = resolver.get(dbConfigs);
                } else {
                    value = ReflectUtil.getFieldValue(templateActInfo, key);
                }

                replace.put(placeholder, toStr(value));
            }

            for (Map.Entry<String, String> entry : replace.entrySet()) {
                templateValue = templateValue.replace(entry.getKey(), entry.getValue());
            }
        }

        // 优先使用condition
        if (StringUtils.startsWith(condition, StringUtils.OPEN_BRACE)) {
            Map<String, Object> conditionMap = JSON.parseObject(condition, new TypeReference<>(){});
            if (MapUtils.isNotEmpty(conditionMap)) {
                LinkedHashMap<String, Object> rowConfig = findByCondition(tableConfig, conditionMap);
                if (rowConfig != null) {
                    setValue(rowConfig, col, templateValue);
                }

                return;
            }
        }

        if (rowIndex < 0) {
            final String value = templateValue;
            tableConfig.forEach(row -> {
                Object baseValue = row.get(col);
                Object newValue = toObject(value, baseValue);
                if (!Objects.equals(newValue, baseValue)) {
                    setValue(row, col, newValue);
                    if (updateStatements != null) {
                        Map<String, Object> params = Map.of(col, newValue);
                        Map<String, Object> conditions = getConditions(db, tbl, row);
                        updateStatements.add(new UpdateStatement(db, tbl, params, conditions));
                    }
                }
            });
            return;
        }

        if (rowIndex >= tableConfig.size()) {
            throw new IllegalStateException("table[" + tbl + "] row size: " + tableConfig.size() + " less than config rowIndex: " + rowIndex);
        }

        LinkedHashMap<String, Object> row = tableConfig.get(rowIndex);
        Object baseValue = row.get(col);
        Object newValue = toObject(templateValue, baseValue);
        if (!Objects.equals(newValue, baseValue)) {
            setValue(row, col, newValue);
            if (updateStatements != null) {
                Map<String, Object> params = Map.of(col, newValue);
                Map<String, Object> conditions = getConditions(db, tbl, row);
                updateStatements.add(new UpdateStatement(db, tbl, params, conditions));
            }
        }
    }

    @Override
    public int getOrder() {
        return 8;
    }
}
