package com.yy.manager.template.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.template.entity.TemplateAttrValue;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

@DS("hdpttemplate")
public interface TemplateAttrValueMapper extends BaseMapper<TemplateAttrValue> {

    int addBatch(@Param("list") Collection<TemplateAttrValue> values);
}
