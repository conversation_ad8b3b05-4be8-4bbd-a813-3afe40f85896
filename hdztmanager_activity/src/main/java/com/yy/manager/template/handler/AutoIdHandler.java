package com.yy.manager.template.handler;

import com.yy.common.utils.StringUtils;
import com.yy.manager.datasource.database.DataSourceSelector;
import com.yy.manager.hdztmanager.mapper.CommonMapper;
import com.yy.manager.template.entity.TemplateConfigColumn;
import com.yy.manager.template.enums.ValueSource;
import com.yy.manager.template.exception.HandleException;
import com.yy.manager.template.vo.TransferContext;
import com.yy.manager.template.vo.UpdateStatement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 自增ID替换
 */
@Component("autoIdHandler")
public class AutoIdHandler implements IConfigHandler {

    @Resource
    private CommonMapper commonMapper;

    @Override
    public void handle(TransferContext context, Map<String, LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> dbConfigs, List<UpdateStatement> updateStatements) {
        String actIdStr = context.getTemplateActInfo().getActId();
        if (!StringUtils.isNumeric(actIdStr)) {
            throw new IllegalStateException("actId must be number");
        }

        int actId = Integer.parseInt(actIdStr);
        // 同一个活动不处理自增ID
        if (actId == context.getBaseActivity().getActId()) {
            return;
        }

        List<TemplateConfigColumn> templateConfigColumns = context.findTemplateConfigColumns(ValueSource.auto);
        if (CollectionUtils.isEmpty(templateConfigColumns)) {
            return;
        }
        for (TemplateConfigColumn column : templateConfigColumns) {
            var dbConfig = dbConfigs.get(column.getDb());
            if (MapUtils.isEmpty(dbConfig)) {
                continue;
            }

            var tableConfig = dbConfig.get(column.getTbl());
            if (CollectionUtils.isEmpty(tableConfig)) {
                continue;
            }

            try {
                Long maxId;
                try (DataSourceSelector ignored = DataSourceSelector.doWithActId(column.getDb(), actId)) {
                    maxId = commonMapper.selectMaxId(column.getTbl(), column.getCol());
                }

                maxId = maxId == null ? 0 : maxId;
                for (Map<String, Object> rowConfig : tableConfig) {
                    setValue(rowConfig, column.getCol(), String.valueOf(++maxId));
                }
            } catch (Exception e) {
                throw new HandleException(e, getClass().getSimpleName(), column.getDb(), column.getTbl(), column.getCol(), -1);
            }
        }
    }

    @Override
    public int getOrder() {
        return 3;
    }
}
