package com.yy.manager.template.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.template.entity.TemplateAttrDefine;
import com.yy.manager.template.vo.TemplateAttr;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("hdpttemplate")
public interface TemplateAttrDefineMapper extends BaseMapper<TemplateAttrDefine> {

    List<TemplateAttrDefine> selectTemplateAttrDefines(@Param("templateId") String templateId);

    List<TemplateAttr> selectActAttrs(@Param("actId") String actId, @Param("templateId") String templateId);

    TemplateAttrDefine selectByPrimaryKey(String templateId, String db, String tbl, String col, int rowIndex);

    int deleteByPrimaryKey(String templateId, String db, String tbl, String col, int rowIndex);

    int updateByPrimaryKeySelective(TemplateAttrDefine template);
}
