package com.yy.manager.clear.service.redis;

import com.yy.manager.clear.bean.DataSourceEnum;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/1 10:53
 **/
@Service
public class EcologyStreamActRedisDataClear extends AbstractActRedisDataClear {
    @Override
    public RedisTemplate<String, Serializable> doGetRedisTemplate(int groupCode) {
        return redisManager.getRedisEcologyStream();
    }

    @Override
    public DataSourceEnum supportDataSource() {
        return DataSourceEnum.Stream;
    }

    @Override
    public int getGroupCode(String actIdStr) {
        return 0;
    }
}
