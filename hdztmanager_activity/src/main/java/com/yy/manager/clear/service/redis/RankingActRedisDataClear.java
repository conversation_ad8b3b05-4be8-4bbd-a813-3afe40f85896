package com.yy.manager.clear.service.redis;

import com.yy.manager.clear.bean.DataSourceEnum;
import com.yy.manager.clear.bean.RedisDataScopeEnum;
import com.yy.manager.datasource.database.HdztDataSourceContextHolder;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.service.IHdztActivityService;
import com.yy.manager.hdzt.service.IRankingConfigService;
import com.yy.manager.utils.Convert;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/29 17:57
 **/
@Service
public class RankingActRedisDataClear extends AbstractActRedisDataClear {
    @Autowired
    private IHdztActivityService hdztActivityService;
    @Autowired
    private IRankingConfigService rankingConfigService;

    @Override
    public RedisTemplate<String, Serializable> doGetRedisTemplate(int groupCode) {
        return redisManager.getHdztRankingRedisTemplate(groupCode);
    }

    @Override
    public DataSourceEnum supportDataSource() {
        return DataSourceEnum.ZTRanking;
    }

    @Override
    public int getGroupCode(String actIdStr) {
        long actId = Convert.toLong(actIdStr, 0);
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity != null && activity.getActGroup() != null && activity.getActGroup() > 0) {
            return activity.getActGroup();
        }

        return -1;
    }

    /**
     * 获取与groupCode不同的榜单配置
     **/
    public List<Integer> listRankConfigGroupCode(String actId, int groupCode) {
        List<Integer> rankGroupCodes = rankingConfigService.selectRedisGroup(Convert.toLong(actId, 0));
        List<Integer> result = rankGroupCodes.stream()
                .filter(rankGroupCode -> rankGroupCode != null && rankGroupCode != 0 && rankGroupCode != groupCode)
                .collect(Collectors.toList());

        return result;
    }

    @Override
    public void cleanKeys(String actId, int groupCode, String token) {
        List<Integer> rankGroupCodes = listRankConfigGroupCode(actId, groupCode);
        rankGroupCodes.forEach(rankGroupCode -> super.cleanKeys(actId, rankGroupCode, token));
        super.cleanKeys(actId, groupCode, token);
    }

    @Override
    public Set<String> scanKey(String actId, RedisDataScopeEnum dataScope, int groupCode) {
        Set<String> keys = super.scanKey(actId, dataScope, groupCode);
        List<Integer> rankGroupCodes = listRankConfigGroupCode(actId, groupCode);
        if (CollectionUtils.isNotEmpty(rankGroupCodes)) {
            rankGroupCodes.forEach(rankGroupCode -> keys.addAll(super.scanKey(actId, dataScope, rankGroupCode)));
        }

        return keys;
    }

    @Override
    public void cleanKeysDirect(String actId, RedisDataScopeEnum dataScope, int groupCode) {
        super.cleanKeysDirect(actId, dataScope, groupCode);
        List<Integer> rankGroupCodes = listRankConfigGroupCode(actId, groupCode);
        if (CollectionUtils.isNotEmpty(rankGroupCodes)) {
            rankGroupCodes.forEach(rankGroupCode -> super.cleanKeysDirect(actId, dataScope, rankGroupCode));
        }
    }
}
