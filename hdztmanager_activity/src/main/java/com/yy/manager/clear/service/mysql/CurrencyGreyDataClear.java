package com.yy.manager.clear.service.mysql;

import cn.hutool.http.HttpUtil;
import com.yy.common.utils.SysEnvHelper;
import com.yy.manager.clear.bean.MysqlDataScopeEnum;
import com.yy.manager.currency.mapper.CurrencyMetaMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class CurrencyGreyDataClear extends AbstractActMysqlDataClear {

    private static final String CLEAN_URL = SysEnvHelper.isDeploy() ? "https://currency-zy.yy.com/currency/clearGrayData" : "https://currency-zy-test.yy.com/currency/clearGrayData";

    @Resource
    private CurrencyMetaMapper currencyMetaMapper;

    @Override
    public MysqlDataScopeEnum supportDataScope() {
        return MysqlDataScopeEnum.Currency_Grey_Data;
    }

    @Override
    protected void doClear(long actId) {
        List<String> cids = currencyMetaMapper.selectActivityCidList(actId);
        if (CollectionUtils.isEmpty(cids)) {
            return;
        }

        String resp = HttpUtil.get(CLEAN_URL, Map.of("cids", StringUtils.join(cids, ',')));
        logger.info("CurrencyGreyDataClear request with cids:{} resp:{}", cids, resp);
    }

    @Override
    protected Map<String, Integer> doPreview(long actId) {
        return Collections.emptyMap();
    }
}
