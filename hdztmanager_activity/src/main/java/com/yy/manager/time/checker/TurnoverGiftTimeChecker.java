package com.yy.manager.time.checker;

import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.entity.RankingItemTransform;
import com.yy.manager.hdzt.service.IRankingItemTransformService;
import com.yy.manager.thrift.client.TPropStoreServiceClient;
import com.yy.manager.thrift.to_service.THudongPropTimer;
import com.yy.manager.utils.Convert;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;

@Component
public class TurnoverGiftTimeChecker implements TimeChecker {

    @Autowired
    private IRankingItemTransformService rankingItemTransformService;

    @Autowired
    private TPropStoreServiceClient tPropStoreServiceClient;

    @Override
    public String checkerName() {
        return "营收礼物上线时间校验";
    }

    @Override
    public String checkerDesc() {
        return "校验营收礼物定时上线时间";
    }


    @Override
    public boolean check(HdztActivity activity) {
        long startTime = activity.getBeginTime().getTime();
        List<RankingItemTransform> rankingItemTransforms =
                rankingItemTransformService.selectByActId(activity.getActId());
        Set<Integer> appids = new HashSet<>();
        Set<Integer> giftIds = new HashSet<>();
        for (RankingItemTransform rankingItemTransform : rankingItemTransforms) {
            appids.add(getTAppIdByBusiId(rankingItemTransform.getBusiId()));
            int giftId = Convert.toInt(rankingItemTransform.getBusiItemId());
            if(giftId > 1000000) {
                continue;
            }
            if(giftId > 0) {
                giftIds.add(giftId);
            }
        }
        List<Integer> appidList = new ArrayList<>(appids);
        Map<Integer, Long> execueteTimeMap = new HashMap<>();
        for (Integer appid : appidList) {
            List<THudongPropTimer> tHudongPropTimers = tPropStoreServiceClient.GetTHudongPropTimer(appid);
            for (THudongPropTimer tHudongPropTimer : tHudongPropTimers) {
                execueteTimeMap.put(tHudongPropTimer.getPropId(), tHudongPropTimer.getExecuteTime());
            }
        }
        for (Integer giftId : giftIds) {
            if(!execueteTimeMap.containsKey(giftId)) {
                return false;
            }
            if(execueteTimeMap.get(giftId) > startTime) {
                return false;
            }
        }
        return true;
    }

    public static int getTAppIdByBusiId(int busiId) {
        return switch (busiId) {
            case 400 -> 36;
            case 500 -> 2;
            case 810 -> 34;
            default -> 0;
        };

    }

    @Override
    public int getOrder() {
        return 0;
    }

    @Data
    public static class TurnoverGiftTimeConfig {

        protected Long startTime;

        protected Long executeTime;

        protected int visible;

        protected int canBuy;

        protected String propName;

        protected List<Long> propIds;
    }
}
