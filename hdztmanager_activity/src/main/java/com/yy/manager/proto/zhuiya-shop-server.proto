syntax = "proto3";
package com.yy.manager.proto;

enum Shop {
  BEAN_LOTTERY = 0;   // 玩豆抽奖
  HDPT_ACTIVITY = 1;  // 活动商城
  ZLT_EXP = 2;        // 助力团经验点商城
}

enum Period {
  TOTAL = 0;  // 总
  DAY = 1;    // 日
}

enum CurrencyType {
  ZHUIYA_CURRENCY = 0;  // 虚拟货币
}

enum PropsType {
  ZHUIYA_PRESENT = 0;   // 追玩present
  HDPT_AWARD = 1;       // 活动平台award
}

message ItemLimit {
  Period period = 1;    // 周期
  int64 limit = 2;      // 总限额, -1代表不限
  int64 userLimit = 3;  // uid限额, -1代表不限
  int64 deviceLimit = 4;// 设备限额, -1代表不限
}

message ItemPrice {
  CurrencyType currencyType = 1;  // 货币类型

  // 虚拟货币
  string currency = 2;            // 货币id
  int64 price = 3;                // 价格
}

message ItemProps {
  PropsType propsType = 1;        // 道具类型

  // 追玩
  repeated int64 pid = 2;         // 道具id

  // 活动
  int64 taskId = 3;               // 奖池id
  int64 packageId = 4;            // 奖包id

  // 交友
//  string rewardId = 5;
//  string password = 6;
//  string subResId = 7;
//  int64 count = 8;
}

message ShopItemConfig {
  string shopId = 1;                // 商城id
  string itemId = 2;                // 商品id
  string itemName = 3;              // 商品名
  string itemDesc = 4;              // 商品说明
  string itemImage = 8;             // 商品图片
  ItemPrice itemPrice = 5;          // 价格
  ItemProps itemProps = 6;          // 道具
  repeated ItemLimit itemLimit = 7; // 限额
  int64 beginTime = 11;             // 开始时间
  int64 endTime = 12;               // 结束时间
  map<string, string> expand = 13;  // 拓展信息
  int32 weight = 14;                // 权重
}

message ListShopItemConfigReq {
  Shop shop = 1;      // 商城
  string shopId = 2;  // 商城id
}
message ListShopItemConfigResp {
  repeated ShopItemConfig shopItemConfig = 1;
}

message SaveShopItemConfigReq {
  Shop shop = 1;        // 商城
  ShopItemConfig shopItemConfig = 2;
}
message SaveShopItemConfigResp {
  int32 code = 1;       // 0-成功, 其他失败
  string message = 2;   // 一般为失败提示
}

message HideShopItemConfigReq {
  Shop shop = 1;        // 商城
  string shopId = 2;    // 商城id
  string itemId = 3;    // 商品id
}
message HideShopItemConfigResp {
  int32 code = 1;       // 0-成功, 其他失败
  string message = 2;   // 一般为失败提示
}

service ZhuiyaShopConfigYrpc {

  // 商品配置 functionName=shop/listShopItemConfig
  rpc listShopItemConfig (ListShopItemConfigReq) returns (ListShopItemConfigResp);

  // 保存商品 functionName=shop/saveShopItemConfig
  rpc saveShopItemConfig (SaveShopItemConfigReq) returns (SaveShopItemConfigResp);

  // 隐藏商品 functionName=shop/hideShopItemConfig
  rpc hideShopItemConfig (HideShopItemConfigReq) returns (HideShopItemConfigResp);

}

// s2s: zhuiya_shop_yrpc
