package com.yy.manager.actoffline.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.actoffline.entity.OfflineTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OfflineTaskMapper extends BaseMapper<OfflineTask> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_task
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    OfflineTask selectByActId(@Param("actId") long actId);

    List<OfflineTask> selectProcessingTasks();

    int insertRepeatable(OfflineTask offlineTask);
}