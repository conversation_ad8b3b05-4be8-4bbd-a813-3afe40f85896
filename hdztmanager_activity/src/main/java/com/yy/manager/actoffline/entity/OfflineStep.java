package com.yy.manager.actoffline.entity;

import com.baomidou.mybatisplus.annotation.TableId;

public class OfflineStep {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column offline_step.id
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    @TableId
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column offline_step.step
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    private Integer step;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column offline_step.step_name
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    private String stepName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column offline_step.step_desc
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    private String stepDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column offline_step.step_type
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    private Byte stepType;


    private Byte idempotent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column offline_step.step_executor
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    private String stepExecutor;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column offline_step.id
     *
     * @return the value of offline_step.id
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column offline_step.id
     *
     * @param id the value for offline_step.id
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column offline_step.step
     *
     * @return the value of offline_step.step
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public Integer getStep() {
        return step;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column offline_step.step
     *
     * @param step the value for offline_step.step
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public void setStep(Integer step) {
        this.step = step;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column offline_step.step_name
     *
     * @return the value of offline_step.step_name
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public String getStepName() {
        return stepName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column offline_step.step_name
     *
     * @param stepName the value for offline_step.step_name
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public void setStepName(String stepName) {
        this.stepName = stepName == null ? null : stepName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column offline_step.step_desc
     *
     * @return the value of offline_step.step_desc
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public String getStepDesc() {
        return stepDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column offline_step.step_desc
     *
     * @param stepDesc the value for offline_step.step_desc
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public void setStepDesc(String stepDesc) {
        this.stepDesc = stepDesc == null ? null : stepDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column offline_step.step_type
     *
     * @return the value of offline_step.step_type
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public Byte getStepType() {
        return stepType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column offline_step.step_type
     *
     * @param stepType the value for offline_step.step_type
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public void setStepType(Byte stepType) {
        this.stepType = stepType;
    }

    public Byte getIdempotent() {
        return idempotent;
    }

    public void setIdempotent(Byte idempotent) {
        this.idempotent = idempotent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column offline_step.step_executor
     *
     * @return the value of offline_step.step_executor
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public String getStepExecutor() {
        return stepExecutor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column offline_step.step_executor
     *
     * @param stepExecutor the value for offline_step.step_executor
     *
     * @mbg.generated Wed May 17 12:01:06 CST 2023
     */
    public void setStepExecutor(String stepExecutor) {
        this.stepExecutor = stepExecutor == null ? null : stepExecutor.trim();
    }
}