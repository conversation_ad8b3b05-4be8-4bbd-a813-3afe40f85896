package com.yy.manager.actoffline.executor;

import com.yy.manager.actoffline.enums.StepState;
import com.yy.manager.ecology.service.IReRuleScriptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("shutdownScriptExecutor")
public class ShutdownScriptExecutor implements StepExecutor {

    @Autowired
    private IReRuleScriptService reRuleScriptService;

    @Override
    public StepState check(long actId) {
        int count = reRuleScriptService.getEnabledScriptCount(actId);
        return count > 0 ? StepState.nys : StepState.done;
    }

    @Override
    public StepState execute(long actId, long opUid) {
        int rs = reRuleScriptService.disableScriptByActId(actId, opUid);
        log.info("ShutdownScriptExecutor disable rule script with actId:{}, opUid:{}, rs:{}", actId, opUid, rs);
        return StepState.done;
    }
}
