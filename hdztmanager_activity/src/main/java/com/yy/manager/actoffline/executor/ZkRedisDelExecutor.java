package com.yy.manager.actoffline.executor;

import com.yy.manager.actoffline.enums.StepState;
import com.yy.manager.tools.service.GeActivityHttpService;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("zkRedisDelExecutor")
public class ZkRedisDelExecutor implements StepExecutor {

    @Autowired
    private GeActivityHttpService geActivityHttpService;

    @Override
    public StepState check(long actId) {
        Response response = geActivityHttpService.invokeHdzkInnerActAction(true,"checkDoneDelRedis", 0L, actId);
        if (response == null || response.getResult() != Const.ONE) {
            return StepState.nys;
        }

        return StepState.done;
    }

    @Override
    public StepState execute(long actId, long opUid) {
        Response response = geActivityHttpService.invokeHdzkInnerActAction(true,"delHdzkRedis", opUid, actId);
        if (response.getResult() != Const.ZERO) {
            return StepState.nys;
        }
        return StepState.proceeding;
    }
}
