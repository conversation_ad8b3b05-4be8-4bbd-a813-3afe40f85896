package com.yy.manager.actoffline.executor;

import com.yy.manager.actoffline.enums.StepState;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.service.IHdztActivityService;
import com.yy.manager.tools.service.HdztRankingHttpService;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("actArchiveExecutor")
public class ActArchiveExecutor implements StepExecutor {

    private static final int ARCHIVE = 77;

    @Autowired
    private IHdztActivityService hdztActivityService;

    @Autowired
    private HdztRankingHttpService hdztRankingHttpService;

    @Override
    public StepState check(long actId) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity != null && activity.getStatus() != null && activity.getStatus() != ARCHIVE) {
            return StepState.nys;
        }

        return StepState.done;
    }

    @Override
    public StepState execute(long actId, long opUid) {
        Response response = hdztRankingHttpService.invokeActOffLine("setActArchive", opUid, actId);
        if (response == null || response.getResult() != Const.HTTP_OK) {
            return StepState.nys;
        }

        return StepState.done;
    }
}
