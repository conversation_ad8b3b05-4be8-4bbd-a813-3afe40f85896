package com.yy.manager.actoffline.controller;

import com.yy.common.utils.SecurityUtils;
import com.yy.manager.actoffline.entity.OfflineStepDetail;
import com.yy.manager.actoffline.service.ActOfflineService;
import com.yy.manager.actoffline.vo.OfflineActInfoVo;
import com.yy.manager.actoffline.vo.OfflineDetailVo;
import com.yy.manager.actoffline.vo.OfflineStepDetailVo;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.tools.service.GeActivityHttpService;
import com.yy.manager.tools.service.HdztRankingHttpService;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.PageResponse;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-13 16:50
 **/
@RestController
@RequestMapping("/actOffline")
public class ActOfflineController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private GeActivityHttpService geActivityHttpService;

    @Autowired
    private HdztRankingHttpService hdztRankingHttpService;

    @Autowired
    private ActOfflineService actOfflineService;

    @ApiOperation("复制活动中控redis到pika")
    @PreAuthorize("@ss.hasPermi('activity:actOnffLine:copyHdzk2Pika')")
    @RequestMapping("copyHdzk2Pika/{actId}")
    public Response copyHdzk2Pika(@PathVariable long actId) {
        long opUid = SecurityUtils.getLoginUid();
        return geActivityHttpService.invokeHdzkInnerActAction(true,"copyHdzk2Pika", opUid, actId);
    }

    @ApiOperation("删除活动中控redis数据")
    @PreAuthorize("@ss.hasPermi('activity:actOnffLine:delHdzkRedis')")
    @RequestMapping("delHdzkRedis/{actId}")
    public Response delHdzkRedis(@PathVariable long actId) {
        long opUid = SecurityUtils.getLoginUid();
        //检查中控在线环境是否存在活动定制代码
        Response response = geActivityHttpService.invokeHdzkInnerActAction(true,"checkActCustomizedClass", opUid, actId);
        if (response.getResult() != Const.ONE) {
            return response;
        }

        return geActivityHttpService.invokeHdzkInnerActAction(true,"delHdzkRedis", opUid, actId);
    }

    @ApiOperation("设置活动为归档状态")
    @PreAuthorize("@ss.hasPermi('activity:actOnffLine:setActArchive')")
    @RequestMapping("setActArchive/{actId}")
    public Response setActArchive(@PathVariable long actId) {
        long opUid = SecurityUtils.getLoginUid();
        //校验中控redis数据有无备份完成
        Response response = geActivityHttpService.invokeHdzkInnerActAction(true,"checkDoneArchive", opUid, actId);
        if (response.getResult() != Const.ONE) {
            return response;
        }
        return hdztRankingHttpService.invokeActOffLine("setActArchive", opUid, actId);
    }

    @ApiOperation("复制活动中台redis到pika")
    @PreAuthorize("@ss.hasPermi('activity:actOnffLine:copyHdzt2Pika')")
    @RequestMapping("copyHdzt2Pika/{actId}")
    public Response copyHdzt2Pika(@PathVariable long actId) {
        long opUid = SecurityUtils.getLoginUid();
        return hdztRankingHttpService.invokeActOffLine("copyHdzt2Pika", opUid, actId);
    }


    @ApiOperation("删除活动中台redis数据")
    @PreAuthorize("@ss.hasPermi('activity:actOnffLine:delHdztRedis')")
    @RequestMapping("delHdztRedis/{actId}")
    public Response delHdztRedis(@PathVariable long actId) {
        long opUid = SecurityUtils.getLoginUid();
        //检查中控在线环境是否存在活动定制代码
        Response response = geActivityHttpService.invokeHdzkInnerActAction(true,"checkActCustomizedClass", opUid, actId);
        if (response.getResult() != Const.ONE) {
            return response;
        }

        return hdztRankingHttpService.invokeActOffLine("delHdztRedis", opUid, actId);
    }

    @GetMapping("/act/list")
    public PageResponse<OfflineActInfoVo> queryPageActList(@RequestParam(name = "group") int group,
                                                           @RequestParam(name = "actId", required = false) Long actId,
                                                           @RequestParam(name = "status", required = false) Integer status,
                                                           @RequestParam(name = "pageNo") int pageNo,
                                                           @RequestParam(name = "pageSize") int pageSize) {
        return actOfflineService.queryPageActList(group, actId, status, pageNo, pageSize);
    }

    @GetMapping("/task/detail")
    public Response<OfflineDetailVo> queryActOfflineDetail(long actId) {
        return actOfflineService.queryOfflineStepDetail(actId);
    }

    @GetMapping("/task/step/detail")
    public Response<OfflineStepDetailVo> queryOfflineStepDetail(long actId, int stepId) {
        return actOfflineService.queryOfflineStepDetail(actId, stepId);
    }

    @PostMapping("/task")
    public Response<Long> addActOfflineTask(long actId) {
        return actOfflineService.addActOfflineTask(actId);
    }

    @PutMapping("/step/execute")
    public Response<Integer> executeStep(long actId, int stepId) {
        long uid = SecurityUtils.getLoginUid();
        return actOfflineService.executeOfflineStep(uid, actId, stepId);
    }

    @PutMapping("/step/fresh")
    public Response<Integer> freshProcessStep(long actId, int stepId) {
        return actOfflineService.refreshProcessingStepState(actId, stepId);
    }
}
