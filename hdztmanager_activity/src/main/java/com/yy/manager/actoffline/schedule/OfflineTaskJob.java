package com.yy.manager.actoffline.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.ImmutableSet;
import com.yy.manager.actoffline.entity.OfflineStepDetail;
import com.yy.manager.actoffline.entity.OfflineTask;
import com.yy.manager.actoffline.enums.StepState;
import com.yy.manager.actoffline.mapper.OfflineStepDetailMapper;
import com.yy.manager.actoffline.mapper.OfflineTaskMapper;
import com.yy.manager.actoffline.service.ActOfflineService;
import com.yy.manager.commonservice.CommonService;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.service.IHdztActivityAttrService;
import com.yy.manager.hdzt.service.IHdztActivityService;
import com.yy.manager.service.BaiduInfoFlowRobotService;
import com.yy.manager.tools.consts.ActStatus;
import com.yy.manager.tools.consts.HdztActAttrName;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.SysEnvHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
@Profile({"prod", "test"})
public class OfflineTaskJob {

    private static final Set<Integer> MERGE_STEP_IDS = ImmutableSet.of(2, 3);

    private static final String MERGE_CODE_NOTIFY = "活动已经上线%s天，请负责该活动同学将中控代码合并到history分支\n " +
            "操作指引：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/VrOaPpOR20/sGkkhMVTmeL1rt\n" +
            "合并完代码操作后请到以下操作界面确认相关步骤\n" +
            "操作界面：https://" + (SysEnvHelper.isDeploy() ? StringUtils.EMPTY : "test-") + "manager-hdzt.yy.com/admin-static/actOffline/actOfflineOperation?actId=%s";

    private static final String PROCESSING_TASK_NOTIFY = "活动已经结束%s天，请负责该活动同学做数据离线化操作\n " +
            "操作指引：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/VrOaPpOR20/sGkkhMVTmeL1rt\n" +
            "操作界面：https://" + (SysEnvHelper.isDeploy() ? StringUtils.EMPTY : "test-") + "manager-hdzt.yy.com/admin-static/actOffline/actOfflineOperation?actId=%s";

    @Resource
    private OfflineTaskMapper offlineTaskMapper;

    @Resource
    private OfflineStepDetailMapper stepDetailMapper;

    @Autowired
    private ActOfflineService actOfflineService;

    @Autowired
    private IHdztActivityService hdztActivityService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BaiduInfoFlowRobotService infoFlowRobotService;


    @Autowired
    private IHdztActivityAttrService hdztActivityAttrService;

    @Scheduled(cron = "1 0 * * * ?")
    public void addOfflineTask1() {
        addOfflineTask(1);
    }

    @Scheduled(cron = "2 10 * * * ?")
    public void addOfflineTask2() {
        addOfflineTask(2);
    }

    @Scheduled(cron = "2 20 * * * ?")
    public void addOfflineTask3() {
        addOfflineTask(3);
    }

    @Scheduled(cron = "2 30 * * * ?")
    public void addOfflineTask4() {
        addOfflineTask(4);
    }

    @Scheduled(cron = "2 40 * * * ?")
    public void addOfflineTask5() {
        addOfflineTask(5);
    }

    @Scheduled(cron = "2 50 * * * ?")
    public void addOfflineTask6() {
        addOfflineTask(6);
    }

    @Scheduled(cron = "35 45 * * * ?")
    public void addOfflineTask7() {
        addOfflineTask(7);
    }

    public void addOfflineTask(int group) {
        List<HdztActivity> activities = hdztActivityService.selectAllEffectActivity(group);
        if (CollectionUtils.isEmpty(activities)) {
            return;
        }

        int mergePeriodDays = actOfflineService.getMergePeriodDays();
        long periodMills = Math.max(0, mergePeriodDays - 1) * DateUtils.MILLIS_PER_DAY;
        for (HdztActivity activity : activities) {
            final long actId = activity.getActId();
            if (activity.getStatus() != ActStatus.NORMAL.intValue() || commonService.isGrey(actId)) {
                continue;
            }

            Date now = commonService.getNow(actId);
            if (activity.getBeginTime().getTime() + periodMills <= now.getTime()) {
                actOfflineService.addActOfflineTask(actId);
            }
        }
    }

    @Scheduled(cron = "9 19 11 * * ?")
    public void notifyCodeMerge() {
        if (!SysEnvHelper.isDeploy()) {
            return;
        }
        List<OfflineTask> tasks = offlineTaskMapper.selectProcessingTasks();
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        int mergePeriodDays = actOfflineService.getMergePeriodDays();
        for (OfflineTask task : tasks) {
            final long actId = task.getActId();
            if (commonService.isGrey(actId)) {
                continue;
            }
            HdztActivity activity = hdztActivityService.selectByActId(actId);
            if (activity == null || activity.getStatus() != ActStatus.NORMAL.intValue()) {
                continue;
            }

            QueryWrapper<OfflineStepDetail> qw = new QueryWrapper<OfflineStepDetail>()
                    .eq("act_id", actId)
                    .in("step_id", MERGE_STEP_IDS);
            List<OfflineStepDetail> stepDetails = stepDetailMapper.selectList(qw);
            if (CollectionUtils.isNotEmpty(stepDetails)) {
                for (OfflineStepDetail stepDetail : stepDetails) {
                    if (stepDetail.getState().intValue() != StepState.done.ordinal()) {
                        //如流通知
                        String msg = String.format(MERGE_CODE_NOTIFY, mergePeriodDays, stepDetail.getActId());
                        msg = buildActRuliuMsg(activity, false, "代码合并", msg);
                        String technical = hdztActivityAttrService.getActTechnical(task.getActId());
                        infoFlowRobotService.asyncSendNotifyConfigKey(Const.RL_DEFAULT_CONFIG, msg, StringUtils.isBlank(technical) ? Collections.emptyList() : List.of(technical));
                        break;
                    }
                }
            }
        }
    }

    @Scheduled(cron = "9 9 11 * * ?")
    public void notifyProcessingTask() {
        if (!SysEnvHelper.isDeploy()) {
            return;
        }
        List<OfflineTask> tasks = offlineTaskMapper.selectProcessingTasks();
        int offlinePeriodDays = actOfflineService.getOfflinePeriodDays();
        for (OfflineTask task : tasks) {
            final long actId = task.getActId();
            if (commonService.isGrey(actId)) {
                continue;
            }
            HdztActivity activity = hdztActivityService.selectByActId(actId);
            if (activity == null || activity.getStatus() != ActStatus.NORMAL.intValue()) {
                continue;
            }

            Date now = commonService.getNow(actId);
            String attrValue = hdztActivityAttrService.getActAttr(actId, HdztActAttrName.OFFLINE_PERIOD_DAYS);
            final int actOfflinePeriodDays = StringUtils.isNumeric(attrValue) ? Integer.parseInt(attrValue) : offlinePeriodDays;
            long validOfflineMills = activity.getEndTime().getTime() + actOfflinePeriodDays * DateUtils.MILLIS_PER_DAY;
            if (now.getTime() >= validOfflineMills) {
                //如流通知
                String msg = String.format(PROCESSING_TASK_NOTIFY, offlinePeriodDays, task.getActId());
                msg = buildActRuliuMsg(activity, false, "离线操作", msg);
                String technical = hdztActivityAttrService.getActTechnical(task.getActId());
                infoFlowRobotService.asyncSendNotifyConfigKey(Const.RL_DEFAULT_CONFIG, msg, StringUtils.isBlank(technical) ? Collections.emptyList() : List.of(technical));
            }
        }
    }

    public String buildActRuliuMsg(HdztActivity activity, boolean error, String title, String msg) {
        return "### <font color=\"" + (error ? "red" : "green") + "\">【后台->" + title + (error ? "错误告警" : "通知") + "】</font>\n" + "#### [" + activity.getActId() + "]" + activity.getActName() + "\n" +
                "\n" + msg + "\n";
    }

    @Scheduled(initialDelay = 500, fixedDelay = 3000)
    public void refreshTimerStepState() {
        List<OfflineTask> tasks = offlineTaskMapper.selectProcessingTasks();
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        for (OfflineTask task : tasks) {
            actOfflineService.updateProcessingStepState(task, null);
        }
    }
}
