package com.yy.manager.datasource.database;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.manager.datasource.entity.enums.DataSourceType;
import com.yy.manager.utils.ActGroupHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;


/**
 * @Author: 曾文帜
 * @Desciption:
 * @Date: 2022-05-06 17:54
 * @Modified:补充Author
 */
public class GameecologyDataSourceContextHolder {
    private static Map<Integer, DataSourceType> dataSourceTypeMap
            = ImmutableMap.<Integer, DataSourceType>builder()
            .put(1, DataSourceType.HDZK)
            .put(2, DataSourceType.HDZK2)
            .put(3, DataSourceType.HDZK3)
            .put(4, DataSourceType.HDZK4)
            .put(5, DataSourceType.HDZK5)
            .put(6, DataSourceType.HDZK6)
            .put(7, DataSourceType.HDZK7)
            .build();

    public static DataSourceType getDataSource(long actId) {
        int group = ActGroupHelper.getActGroup(actId);
        return dataSourceTypeMap.get(group);
    }

    public static void setDataSourceTypeByActId(long actId) {
        DataSourceType dataSourceType = getDataSource(actId);
        Assert.notNull(dataSourceType, "actId not find group," + actId);
        DynamicDataSourceContextHolder.setDataSourceType(dataSourceType);
    }

    public static List<DataSourceType> getDataSources() {
        return Lists.newArrayList(dataSourceTypeMap.values());
    }
}
