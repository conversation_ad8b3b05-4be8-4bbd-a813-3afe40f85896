package com.yy.manager.datasource.lx;

import com.yy.hd.lingxi.DataSourceHolder;
import com.yy.hd.lingxi.vo.JdbcDataSource;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-05-12 15:42
 **/
@Component
public class LxDataSourceRegistry {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Setter(onMethod = @__(@Autowired))
    private DataSourceHolder dataSourceHolder;

    @Setter(onMethod = @__({@Autowired, @Qualifier("ecologyJdbcTemplate")}))
    private JdbcTemplate ecologyJdbcTemplate;

    @Setter(onMethod = @__({@Autowired, @Qualifier("hdztJdbcTemplate")}))
    private JdbcTemplate hdztJdbcTemplate;

    @Setter(onMethod = @__({@Autowired, @Qualifier("hdztManagerJdbcTemplate")}))
    private JdbcTemplate hdztManagerJdbcTemplate;

    @Setter(onMethod = @__({@Autowired, @Qualifier("ecologyJdbcTemplate7")}))
    private JdbcTemplate ecologyJdbcTemplate7;

    @Setter(onMethod = @__({@Autowired, @Qualifier("hdztJdbcTemplate7")}))
    private JdbcTemplate hdztJdbcTemplate7;

    @Setter(onMethod = @__({@Autowired, @Qualifier("hdptTemplateJdbcTemplate")}))
    private JdbcTemplate hdptTemplateJdbcTemplate;

//    @Setter(onMethod = @__(@Autowired))
//    private TurnoverClient turnoverClient;

    @PostConstruct
    public void init() {
        {
            JdbcDataSource jdbc = JdbcDataSource.builder()
                    .dsKey("xlogic")
                    .desc("活动中控")
                    .jdbcTemplate(ecologyJdbcTemplate)
                    .build();
            dataSourceHolder.registerJdbcTemplate(jdbc);
        }

        {
            JdbcDataSource jdbc = JdbcDataSource.builder()
                    .dsKey("hdzt")
                    .desc("活动中台")
                    .jdbcTemplate(hdztJdbcTemplate)
                    .build();
            dataSourceHolder.registerJdbcTemplate(jdbc);
        }

        {
            JdbcDataSource jdbc = JdbcDataSource.builder()
                    .dsKey("hdpt_manager")
                    .desc("管理后台")
                    .jdbcTemplate(hdztManagerJdbcTemplate)
                    .build();
            dataSourceHolder.registerJdbcTemplate(jdbc);
        }

        {
            JdbcDataSource jdbc = JdbcDataSource.builder()
                    .dsKey("xlogic7")
                    .desc("活动中控7")
                    .jdbcTemplate(ecologyJdbcTemplate7)
                    .build();
            dataSourceHolder.registerJdbcTemplate(jdbc);
        }

        {
            JdbcDataSource jdbc = JdbcDataSource.builder()
                    .dsKey("hdzt7")
                    .desc("活动中台7")
                    .jdbcTemplate(hdztJdbcTemplate7)
                    .build();
            dataSourceHolder.registerJdbcTemplate(jdbc);
        }

        {
            JdbcDataSource jdbc = JdbcDataSource.builder()
                    .dsKey("hdptTemplate")
                    .desc("活动模板")
                    .jdbcTemplate(hdptTemplateJdbcTemplate)
                    .build();
            dataSourceHolder.registerJdbcTemplate(jdbc);
        }



//        {
//            ThriftDataSource thrift = ThriftDataSource.builder()
//                    .dsKey("zwyyfStatService")
//                    .desc("营收语音房统计")
//                    .iface(TZwyyfStatService.Iface.class)
//                    .client(turnoverClient.getZwyyfStat())
//                    .build();
//            dataSourceHolder.registerThriftClient(thrift);
//        }
    }
}
