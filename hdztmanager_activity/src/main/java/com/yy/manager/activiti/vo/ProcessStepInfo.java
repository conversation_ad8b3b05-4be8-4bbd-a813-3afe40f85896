package com.yy.manager.activiti.vo;

import com.yy.manager.activiti.enums.ActivityState;
import com.yy.manager.actoffline.enums.StepState;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ProcessStepInfo {

    protected String activityId;

    protected String stepName;

    protected String stepDoc;

    protected ActivityState state = ActivityState.nys;

    protected long assignUid;

    protected String failMessage;

    protected Date startTime;

    protected Date endTime;
}
