package com.yy.manager.activiti.controller;

import com.yy.common.utils.SecurityUtils;
import com.yy.manager.activiti.service.ProcessCoreService;
import com.yy.manager.activiti.vo.ProcessInfo;
import com.yy.manager.utils.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("process")
public class ProcessController {

    @Autowired
    private ProcessCoreService processCoreService;

    @GetMapping("query")
    public Response<ProcessInfo> queryProcessInfo(int procId, int actId) {
        return processCoreService.queryProcessInfo(procId, actId);
    }

    @PostMapping("start")
    public Response<Long> startProcess(int procId, int actId, @RequestBody Map<String, Object> procVars) {
        long loginUid = SecurityUtils.getLoginUid();
        if (loginUid <= 0) {
            return Response.fail(401, "you need login first");
        }

        return processCoreService.startProcess(procId, actId, procVars, loginUid);
    }

    @PostMapping("activity/restart")
    public Response<Long> restartProcessActivity(long procInstanceId, String activityId) {
        long loginUid = SecurityUtils.getLoginUid();
        if (loginUid <= 0) {
            return Response.fail(401, "you need login first");
        }

        return processCoreService.restartActivity(procInstanceId, activityId, loginUid);
    }

    @PostMapping("activity/execute")
    public Response<Long> startProcessActivity(long procInstanceId, String activityId) {
        long loginUid = SecurityUtils.getLoginUid();
        if (loginUid <= 0) {
            return Response.fail(401, "you need login first");
        }

        return processCoreService.executeActivity(procInstanceId, activityId, loginUid);
    }
}
