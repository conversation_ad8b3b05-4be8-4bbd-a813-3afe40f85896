package com.yy.manager.activiti.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.expression.ExpressionUtil;
import com.yy.manager.activiti.entity.ProcessActivityDefinition;
import com.yy.manager.activiti.entity.ProcessActivityInstance;
import com.yy.manager.activiti.entity.ProcessFlowDefinition;
import com.yy.manager.activiti.enums.ActivityState;
import com.yy.manager.activiti.vo.ProcessStepInfo;
import com.yy.manager.actoffline.enums.StepState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public final class ProcessUtil {

    public static List<ProcessActivityDefinition> toSimpleProcessActivityList(String startActivityId, List<ProcessActivityDefinition> activityDefinitions, List<ProcessFlowDefinition> flowDefinitions) {
        if (CollectionUtils.isEmpty(activityDefinitions)) {
            return Collections.emptyList();
        }

        Map<String, ProcessActivityDefinition> activityDefinitionMap = activityDefinitions.stream().collect(Collectors.toMap(ProcessActivityDefinition::getActivityId, Function.identity()));

        ProcessActivityDefinition startDefinition = activityDefinitionMap.get(startActivityId);
        if (startDefinition == null) {
            throw new IllegalStateException("start activity not exist");
        }
        List<ProcessActivityDefinition> result = new ArrayList<>(activityDefinitions.size());
        result.add(startDefinition);

        if (CollectionUtils.isEmpty(flowDefinitions)) {
            return result;
        }

        Map<String, String> flowMap = flowDefinitions.stream().collect(Collectors.toMap(ProcessFlowDefinition::getStartActivityId, ProcessFlowDefinition::getEndActivityId));

        String current = startActivityId;
        while (flowMap.containsKey(current)) {
            current = flowMap.get(current);
            ProcessActivityDefinition activityDefinition = activityDefinitionMap.get(current);
            if (activityDefinition == null) {
                throw new IllegalStateException("activity definition invalid");
            }

            result.add(activityDefinition);

            if (result.size() > activityDefinitions.size()) {
                throw new IllegalStateException("activity definition has cycle");
            }
        }

        return result;
    }

    public static List<ProcessStepInfo> buildProcessSteps(List<ProcessActivityDefinition> orderedActivityDefines, List<ProcessActivityInstance> activityInstances) {
        if (CollectionUtils.isEmpty(orderedActivityDefines)) {
            return Collections.emptyList();
        }

        Map<String, ProcessActivityInstance> activityInstanceMap = new HashMap<>(orderedActivityDefines.size());
        if (CollectionUtils.isNotEmpty(activityInstances)) {
            for (ProcessActivityInstance activityInstance : activityInstances) {
                activityInstanceMap.put(activityInstance.getActivityId(), activityInstance);
            }
        }

        List<ProcessStepInfo> result = new ArrayList<>(orderedActivityDefines.size());
        for (ProcessActivityDefinition activityDefinition : orderedActivityDefines) {
            ProcessStepInfo stepInfo = new ProcessStepInfo();
            stepInfo.setActivityId(activityDefinition.getActivityId());
            stepInfo.setStepName(activityDefinition.getActivityName());
            stepInfo.setStepDoc(activityDefinition.getActivityDesc());
            stepInfo.setState(ActivityState.nys);

            ProcessActivityInstance activityInstance = activityInstanceMap.get(activityDefinition.getActivityId());
            if (activityInstance != null) {
                stepInfo.setState(ActivityState.of(activityInstance.getState()));
                stepInfo.setStartTime(activityInstance.getStartTime());
                stepInfo.setAssignUid(activityInstance.getAssignUid());
                stepInfo.setFailMessage(activityInstance.getFailMessage());
                stepInfo.setEndTime(activityInstance.getEndTime());
            }

            result.add(stepInfo);
        }

        return result;
    }

    public static long getAssignUid(String assignExp, Map<String, Object> procVars, long defaultAssignUid) {
        if (StringUtils.isEmpty(assignExp)) {
            return 0;
        }
        try {
            Object value = ExpressionUtil.eval(assignExp, procVars);
            return Convert.toLong(value, defaultAssignUid);
        } catch (Exception e) {
            log.info("eval assign expression fail exp:{}", assignExp, e);
        }

        return defaultAssignUid;
    }
}
