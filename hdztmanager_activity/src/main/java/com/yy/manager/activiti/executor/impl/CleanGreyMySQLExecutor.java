package com.yy.manager.activiti.executor.impl;

import com.yy.manager.activiti.executor.ExecuteContext;
import com.yy.manager.activiti.executor.JavaExecutor;
import com.yy.manager.clear.service.mysql.ActMysqlDataClearService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("cleanGreyMySQLExecutor")
public class CleanGreyMySQLExecutor implements JavaExecutor {

    @Autowired
    private ActMysqlDataClearService actMysqlDataClearService;

    @Override
    public void execute(ExecuteContext context) {
        actMysqlDataClearService.cleanGreyData(String.valueOf(context.getActId()));
        log.info("clean grey MySQL data done actId:{}", context.getActId());
    }
}
