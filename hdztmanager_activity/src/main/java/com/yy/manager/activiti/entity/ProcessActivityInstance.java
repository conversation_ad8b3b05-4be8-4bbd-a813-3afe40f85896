package com.yy.manager.activiti.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ProcessActivityInstance {

    @TableId
    protected Long id;

    protected Long procInstanceId;

    protected Integer procId;

    protected String activityId;

    protected Integer actId;

    protected Integer state;

    protected Long assignUid;

    protected Integer retry;

    protected String failMessage;

    protected Date startTime;

    protected Date endTime;
}
