package com.yy.manager.activiti.executor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yy.manager.activiti.executor.ExecuteContext;
import com.yy.manager.activiti.executor.JavaExecutor;
import com.yy.manager.template.config.FtsGroupCenterActConfigManager;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.Response;
import com.yy.manager.utils.SysEnvHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
@Component("groupConfigDeployExecutor")
public class GroupConfigDeployExecutor implements JavaExecutor {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private FtsGroupCenterActConfigManager ftsGroupCenterActConfigManager;

    @Override
    public void execute(ExecuteContext context) {
        if (!SysEnvHelper.isDeploy()) {
            return;
        }

        final long actId = context.getActId();

        String url = "https://" + Const.TEST_HOST + "/activityHelp/queryExternalConfig?actId=" + actId + "&dataBase=" + ftsGroupCenterActConfigManager.getModule().name();

        var response = restTemplate.getForEntity(url, String.class);
        if (response.getStatusCode() != HttpStatus.OK) {
            throw new RuntimeException("get remote config failed");
        }

        if (StringUtils.isEmpty(response.getBody())) {
            return;
        }

        Response<LinkedHashMap<String, List<LinkedHashMap<String, Object>>>> resp = JSON.parseObject(response.getBody(), new TypeReference<Response<LinkedHashMap<String, List<LinkedHashMap<String, Object>>>>>() {});
        if (!resp.success()) {
            throw new RuntimeException("get remote config failed");
        }

        if (MapUtils.isEmpty(resp.getData())) {
            return;
        }

        ftsGroupCenterActConfigManager.addConfig(context.getAssignUid(), actId, resp.getData());
    }
}
