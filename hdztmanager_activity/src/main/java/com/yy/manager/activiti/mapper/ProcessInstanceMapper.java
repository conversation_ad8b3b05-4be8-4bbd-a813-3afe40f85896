package com.yy.manager.activiti.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.activiti.entity.ProcessInstance;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@DS("hdztmanager")
public interface ProcessInstanceMapper extends BaseMapper<ProcessInstance> {

    int insertSelective(ProcessInstance instance);

    int updateProcessInstanceState(@Param("id") long id, @Param("fromState") int fromState, @Param("toState") int toState, @Param("endTime") Date endTime);
}
