package com.yy.manager.activiti.executor.impl;

import com.yy.manager.activiti.executor.ExecuteContext;
import com.yy.manager.activiti.executor.JavaExecutor;
import com.yy.manager.datasource.entity.enums.DataSourceType;
import com.yy.manager.tools.service.ActivityHelpService;
import com.yy.manager.utils.Response;
import com.yy.manager.utils.SysEnvHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("currencyConfigDeployExecutor")
public class CurrencyConfigDeployExecutor implements JavaExecutor {

    @Autowired
    private ActivityHelpService activityHelpService;

    @Override
    public void execute(ExecuteContext context) {
        Response<?> res = activityHelpService.synActConfigToDeploy(context.getActId(), DataSourceType.CURRENCY.getGroup(), true);
        log.info("synActConfigToDeploy with actId:{} res:{}", context.getActId(), res);
        if (SysEnvHelper.isDeploy()) {
            if (res == null) {
                throw new RuntimeException("synActConfigToDeploy with db currency fail, response is null");
            }

            // result == 540 配置为空，不执行任何操作，当做成功
            if (res.getResult() != Response.OK && res.getResult() != 540) {
                throw new RuntimeException("synActConfigToDeploy with db currency fail, " + res.getReason());
            }
        }
    }
}
