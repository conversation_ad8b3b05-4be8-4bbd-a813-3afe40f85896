package com.yy.manager.activiti.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class ProcessInstance {

    @TableId
    protected Long id;

    protected Integer procId;

    protected Integer actId;

    protected String procBusinessKey;

    protected Long assignUid;

    protected Map<String, Object> procVars;

    protected Integer state;

    protected Date startTime;

    protected Date endTime;
}
