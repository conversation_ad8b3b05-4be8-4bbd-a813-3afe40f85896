package com.yy.manager.autotest.service;

import com.yy.manager.ecology.entity.HdzjComponent;
import com.yy.manager.ecology.service.IHdzjComponentService;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.service.IHdztActivityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class AutotestConfigGenerator {

    @Autowired
    private IHdztActivityService hdztActivityService;

    @Autowired
    private IHdzjComponentService hdzjComponentService;

    public String generateAutoTestConfig(long actId, int componentId, int componentIndex) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity == null) {
            throw new IllegalArgumentException("act not found");
        }

        HdzjComponent component = hdzjComponentService.get(actId, componentId, componentIndex);
        if (component == null) {
            throw new IllegalArgumentException("component not found");
        }

        Resource resource = new ClassPathResource("auto-test/" + getConfigFilename(activity.getBusiId()));
        assert resource.exists();
        String xml;
        try {
            xml = IOUtils.toString(resource.getInputStream(), "UTF-8");
        } catch (IOException e) {
            log.error("get config resource fail:", e);
            throw new RuntimeException("get config resource fail!");
        }
        xml = StringUtils.replace(xml, "${actId}", String.valueOf(actId));
        xml = StringUtils.replace(xml, "${componentId}", String.valueOf(componentId));
        xml = StringUtils.replace(xml, "${componentIndex}", String.valueOf(componentIndex));
        xml = StringUtils.replace(xml, "${componentDesc}", component.getCmptTitle());
        return xml;
    }

    private static String getConfigFilename(int businessId) {
        return switch (businessId) {
            case 400 -> "baby-config.xml";
            case 500 -> "fts-config.xml";
            case 810 -> "skc-config.xml";
            default -> throw new UnsupportedOperationException("business unsupported");
        };
    }
}
