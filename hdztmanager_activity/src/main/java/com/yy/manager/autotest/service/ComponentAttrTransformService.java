package com.yy.manager.autotest.service;

import cn.hutool.json.XML;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
//import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.yy.common.utils.StringUtils;
import com.yy.manager.ecology.entity.HdzjComponentAttr;
import com.yy.manager.ecology.service.IHdzjComponentAttrService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ComponentAttrTransformService {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

//    private static final XmlMapper XML_MAPPER = new XmlMapper();

    static {
        OBJECT_MAPPER.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
//        XML_MAPPER.configure(SerializationFeature.INDENT_OUTPUT, true);
    }

    @Autowired
    private IHdzjComponentAttrService hdzjComponentAttrService;

    public String transformComponentAttr(long actId, int componentId, int componentIndex, String rootName) throws Exception {
        List<HdzjComponentAttr> attrs = hdzjComponentAttrService.list(actId, componentId, componentIndex);
        if (CollectionUtils.isEmpty(attrs)) {
            return StringUtils.EMPTY;
        }

        ObjectNode json = OBJECT_MAPPER.createObjectNode();
        for (HdzjComponentAttr attr : attrs) {
            String key = attr.getName();
            String value = attr.getValue();
            JsonNode node;
            if (StringUtils.isEmpty(value)) {
                node = OBJECT_MAPPER.readTree(value);
            } else if (org.apache.commons.lang3.StringUtils.startsWith(value, "[") || org.apache.commons.lang3.StringUtils.startsWith(value, "{") || org.apache.commons.lang3.StringUtils.isNumeric(value)) {
                node = OBJECT_MAPPER.readTree(value);
            } else {
                node = OBJECT_MAPPER.readTree('"' + value + '"');
            }

            json.set(key, node);
        }

//        return XML_MAPPER.writer().withRootName(rootName).writeValueAsString(json);
        return null;
    }
}
