package com.yy.manager.activityattr.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.common.utils.StringUtils;
import com.yy.manager.activityattr.bean.ActivityAttrSaveDTO;
import com.yy.manager.ecology.entity.GeActAttr;
import com.yy.manager.ecology.service.IGeActAttrService;
import com.yy.manager.hdzt.entity.HdztActivityAttr;
import com.yy.manager.hdzt.service.IHdztActivityAttrService;
import com.yy.manager.hdztmanager.service.ISysParameterService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ActivityAttrService {

    private static final String HDZT_DATABASE_NAME = "hdzt";

    private static final String UNSUPPORT_EDIT_ACT_ATTR ="unsupport_edit_act_attr";
    @Autowired
    private IGeActAttrService geActAttrService;

    @Autowired
    private IHdztActivityAttrService hdztActivityAttrService;

    @Autowired
    private ISysParameterService sysParameterService;

    public List<ActivityAttrSaveDTO> listAttrList(long actId,String dataBase) {
        List<ActivityAttrSaveDTO> list = Collections.emptyList();
        List<HdztActivityAttr> actAttrs = Collections.emptyList();
        if(HDZT_DATABASE_NAME.equals(dataBase)){
            actAttrs = hdztActivityAttrService.selectAttrList(actId);
        }else{
            List<GeActAttr> result = geActAttrService.list(actId, new QueryWrapper<GeActAttr>().eq("act_id", actId));
            if(CollectionUtils.isNotEmpty(result)){
                actAttrs = result.stream().map(v->{
                    HdztActivityAttr item = new HdztActivityAttr();
                    item.setActId((int)v.getActId());
                    item.setAttrname(v.getAttrName());
                    item.setAttrvalue(v.getAttrValue());
                    item.setRemark(v.getAttrDesc());
                    return item;
                }).collect(Collectors.toList());
            }
        }

        String uneditAttrs = sysParameterService.queryParameterValue(UNSUPPORT_EDIT_ACT_ATTR);
        if(CollectionUtils.isNotEmpty(actAttrs)) {
            list = actAttrs.stream().map(v->{
                ActivityAttrSaveDTO item = new ActivityAttrSaveDTO();
                BeanUtils.copyProperties(v,item);
                if(StringUtils.isNotEmpty(uneditAttrs) && uneditAttrs.contains(v.getAttrname())){
                    item.setSupportEdit(false);
                }else{
                    item.setSupportEdit(true);
                }
                return item;

            }).collect(Collectors.toList());
        }

        return list;

    }


    public void saveActAttr(ActivityAttrSaveDTO dto) {
        if(HDZT_DATABASE_NAME.equals(dto.getDataBase())){
            HdztActivityAttr hdztActivityAttr = new HdztActivityAttr();
            BeanUtils.copyProperties(dto,hdztActivityAttr);
            hdztActivityAttrService.insertOrUpdate(dto.getActId(),hdztActivityAttr);
        }else{
            GeActAttr geActAttr = new GeActAttr();
            geActAttr.setActId(dto.getActId());
            geActAttr.setAttrName(dto.getAttrname());
            geActAttr.setAttrValue(dto.getAttrvalue());
            geActAttr.setAttrDesc(dto.getRemark());
            geActAttrService.insertOrUpdate(dto.getActId(),geActAttr);
        }

    }

    public void deleteActAttr(long actId,String dataBase,String attrname) {
        if(HDZT_DATABASE_NAME.equals(dataBase)){
            hdztActivityAttrService.deleteActAttr(actId,attrname);
        }else{
            geActAttrService.deleteActAttr(actId,attrname);
        }

    }



}
