package com.yy.manager.activityattr.bean;

import lombok.Data;

@Data
public class ActivityAttrSaveDTO {
    /**
     * 活动标识
     */
    private Integer actId;

    /**
     * 属性名称
     */
    private String attrname;

    /**
     * 属性取值
     */
    private String attrvalue;

    /**
     * 属性说明
     */
    private String remark;

    private Boolean add;
    private String dataBase;

    /**
     * 属性是否支持编辑、删除
     */
    private Boolean supportEdit;
}
