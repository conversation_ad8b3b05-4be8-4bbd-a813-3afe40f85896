package com.yy.manager.rankexcelgen.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.WorkbookUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.common.exception.ServiceException;
import com.yy.manager.rankexcelgen.bean.excel.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: CXZ
 * @Desciption: excel装换器，负责解析excel
 * @Date: 2022/6/29 23:20
 * @Modified:
 */
@Service
public class ActConfigExcelConvertor {

    private static final String RANK_SHEET_NAME = "榜单配置";
    private static final String ROLE_SHEET_NAME = "角色配置";
    private static final String GIFT_SHEET_NAME = "礼物导入";
    /**
     * 注释前缀
     */
    private static final String COMMENT_PREFIX = "##";

    private static final String XLS = "xls";
    private static final String XLSX = "xlsx";

    /**
     * excel读取的最大行数
     */
    private static final int MAX_ROW = 1000;

    /**
     * excel读取的最大列数
     */
    private static final int MAX_CELL = 100;

    /**
     * 活动属性允许偏离第一行的最大行数
     */
    private static final int ACT_INFO_MAX_ROW = 10;

    /**
     * 活动快定位标识1
     */
    private static final String ACT_INFO_START_KEY = "活动";
    /**
     * 活动快定位标识2
     */
    private static final String ACT_TIME_START_KEY = "活动时间";
    /**
     * 榜单块定位标识
     */
    private static final String RANK_INFO_START_KEY = "榜单ID";

    /**
     * 导入并解析excel流
     *
     * @param fileStream
     * @return
     */
    public ExcelConfig importByExcelStream(InputStream fileStream) {
        Workbook workbook = null;
        workbook = WorkbookUtil.createBook(fileStream, null);

        Sheet rankSheet = WorkbookUtil.getOrCreateSheet(workbook, RANK_SHEET_NAME);
        if (WorkbookUtil.isEmpty(rankSheet)) {
            throw new ServiceException("没有找到" + RANK_SHEET_NAME, 500);
        }
        RankSheet rankConfig = readRankSheet(rankSheet);

        Sheet roleSheet = WorkbookUtil.getOrCreateSheet(workbook, ROLE_SHEET_NAME);
        if (roleSheet == null) {
            throw new ServiceException("没有找到" + ROLE_SHEET_NAME, 500);
        }
        List<RoleConfig> roleConfigs = readSheet(roleSheet, RoleConfig.class);
        if (roleConfigs.isEmpty()) {
            throw new ServiceException(ROLE_SHEET_NAME + "没有发现数据", 500);
        }

        Sheet giftSheet = WorkbookUtil.getOrCreateSheet(workbook, GIFT_SHEET_NAME);
        if (giftSheet == null) {
            throw new ServiceException("没有找到" + GIFT_SHEET_NAME, 500);
        }
        List<GiftConfig> giftConfigs = readSheet(giftSheet, GiftConfig.class);
        if (giftConfigs.isEmpty()) {
            throw new ServiceException(GIFT_SHEET_NAME + "没有发现数据", 500);
        }
        return new ExcelConfig(roleConfigs, rankConfig, giftConfigs);

    }

    /**
     * 导入并解析excel文件
     *
     * @param actConfigFile
     * @return
     */
    public ExcelConfig importByExcelFile(MultipartFile actConfigFile)  {

        if (actConfigFile == null) {
            throw new ServiceException("上传文件异常", 500);
        }


        String originalFilename = actConfigFile.getOriginalFilename();
        assert originalFilename != null;

        String extension = originalFilename.substring(originalFilename.lastIndexOf(".")+1);
        if (!XLS.equals(extension) && !XLSX.equals(extension)) {
            throw new ServiceException("文件类型异常，" + extension, 500);
        }
        InputStream fileStream;
        try {
            fileStream = actConfigFile.getInputStream();
        } catch (IOException e) {
            throw new ServiceException("文件读取异常，" + e.getMessage(), 500);
        }

        return importByExcelStream(fileStream);

    }

    /**
     * 读取简单规则的表格
     *
     * @param sheet
     * @param clazz
     * @param <T>
     * @return
     */
    private <T> List<T> readSheet(Sheet sheet, Class<T> clazz) {
        ExcelReader excelReader = new ExcelReader(sheet);
        List<List<Object>> read = excelReader.read(0, MAX_ROW);
        if (read.isEmpty() || StringUtils.isBlank(findFirstWord(read.get(0)))) {
            throw new ServiceException(sheet.getSheetName() + "第一行无数据", 500);
        }

        List<List<Object>> dataList = Lists.newArrayList();
        for (int i = 1; i < read.size(); i++) {
            //不允许跳行
            if (StringUtils.isBlank(findFirstWord(read.get(i)))) {
                break;
            }
            dataList.add(read.get(i));
        }
        if (!dataList.isEmpty()) {
            List<Map<String, String>> mapList = toMapList(read.get(0), dataList);
            return JSON.parseArray(JSON.toJSONString(mapList), clazz);
        }
        return Lists.newArrayList();
    }

    /**
     * 解析榜单配置sheet
     *
     * @param sheet
     * @return
     */
    private RankSheet readRankSheet(Sheet sheet) {
        ExcelReader excelReader = new ExcelReader(sheet);
        excelReader.setIgnoreEmptyRow(false);
        List<List<Object>> read = excelReader.read(0, MAX_ROW, false);
        ActInfo actInfo = getActInfo(read);
        List<RankArea> rankAreas = getRankInfos(read);
        return new RankSheet(actInfo, rankAreas);
    }

    /**
     * 解析活动块信息
     *
     * @param data
     * @return
     */
    private ActInfo getActInfo(List<List<Object>> data) {
        for (int i = 0; i < data.size() && i < ACT_INFO_MAX_ROW; i++) {

            List<Object> row = data.get(i);
            String firstWord = findFirstWord(row);
            if (ACT_TIME_START_KEY.equals(firstWord)) {
                int wordColumn = findWordColumn(row, firstWord);
                firstWord = ACT_INFO_START_KEY;
                row.set(wordColumn - 1, firstWord);
            }
            if (ACT_INFO_START_KEY.equals(firstWord) && data.size() > i + 1) {
                List<Map<String, String>> mapList = toMapList(row, Collections.singletonList(data.get(i + 1)));
                if (mapList.isEmpty()) {
                    break;
                }
                JSONObject jsonObject = new JSONObject(toMap(mapList.get(0)));
                ActInfo actInfo = JSON.parseObject(jsonObject.toJSONString(), ActInfo.class);
                return actInfo;
            }
        }
        throw new ServiceException("榜单配置页读取不到活动配置，请确认有" + ACT_INFO_START_KEY + "或者" + ACT_TIME_START_KEY, 500);


    }

    /**
     * 解析榜单块新
     *
     * @param data
     * @return
     */
    private List<RankArea> getRankInfos(List<List<Object>> data) {
        List<RankArea> rankInfos = Lists.newArrayList();
        for (int i = 0; i < data.size(); i++) {
            List<Object> row = data.get(i);
            String firstWord = findFirstWord(row);
            //按 榜单ID 定位榜单配置
            if (RANK_INFO_START_KEY.equals(firstWord)) {
                int wordColumn = findWordColumn(row, RANK_INFO_START_KEY);
                RankArea rankArea = getRankInfo(data, i, wordColumn);
                rankInfos.add(rankArea);
            }
        }
        if (CollectionUtils.isEmpty(rankInfos)) {
            throw new ServiceException("榜单配置页读取不到榜单配置，请确认有" + RANK_INFO_START_KEY, 500);
        }
        return rankInfos;
    }

    /**
     * 解析榜单块新
     *
     * @param data
     * @return
     */
    private RankArea getRankInfo(List<List<Object>> data, int rankIdKeyRow, int rankIdKeyColumn) {
        //榜单模模块区分
        String rankModule = rankIdKeyRow > 4 ? toString(data.get(rankIdKeyRow - 3).get(rankIdKeyColumn), "") : "";
        //榜单阶段组
        String phaseCodeString = rankIdKeyRow > 3 ? findFirstWord(data.get(rankIdKeyRow - 2)) : "";

        //处理榜单阶段
        List<Object> phaseConfigs = rankIdKeyRow > 2 ? data.get(rankIdKeyRow - 1) : Lists.newArrayList();
        String firstPhaseString = rankIdKeyRow > 2 ? findFirstWord(phaseConfigs) : "";
        if (!phaseCodeString.isEmpty() && firstPhaseString.isEmpty()) {
            throw new ServiceException("无法找到阶段信息" + rankIdKeyColumn, 500);
        } else if (!firstPhaseString.isEmpty() && phaseCodeString.isEmpty()) {
            throw new ServiceException("无法找到阶段组信息" + rankIdKeyColumn, 500);
        } else if (phaseCodeString.isEmpty() && firstPhaseString.isEmpty()) {
            //榜单没有阶段，设置0阶段
            List<Object> phaseKeys = Lists.newArrayList(data.get(rankIdKeyColumn));
            Collections.fill(phaseKeys, "");
            phaseKeys.set(rankIdKeyColumn + 2, "0:阶段");
            phaseConfigs = phaseKeys;
        }


        //阶段时间信息
        List<Map<String, String>> phaseMapList = toMapList(phaseConfigs, Collections.singletonList(data.get(rankIdKeyRow)));
        //找不到阶段信息报错
        if (phaseMapList.isEmpty()) {
            throw new ServiceException("阶段信息读取错误" + rankIdKeyColumn, 500);
        }
        Map<String, String> phaseMap = phaseMapList.get(0);
        phaseMap.remove("");

        //转成list<Map>
        List<ImmutableMap<String, String>> phaseList = phaseMap.entrySet().stream()
                .map(item -> ImmutableMap.of("phase", item.getKey(), "time", item.getValue()))
                .collect(Collectors.toList());

        ImmutableMap<String, Object> rankGroupObject = ImmutableMap.of("rankModule", rankModule,
                "phaseGroup", phaseCodeString, "phaseInfos", phaseList);

        RankArea rankArea = JSONObject.parseObject(JSON.toJSONString(rankGroupObject), RankArea.class);

        List<PhaseInfo> phaseInfos = rankArea.getPhaseInfos();
        if (phaseInfos.size() != phaseMap.size()) {
            throw new ServiceException("榜单阶段信息配置异常，" +  StrUtil.COMMA + rankIdKeyColumn, 500);
        }

        //榜单时间替换成阶段
        List<Object> rankConfigKeys = Lists.newArrayList(data.get(rankIdKeyRow));

        //向下寻找榜单配置，id是id
        List<List<Object>> myRankConfigs = Lists.newArrayList();
        for (int i = rankIdKeyRow + 1; i < data.size(); i++) {
            List<Object> row = data.get(i);
            if (row.size() < rankIdKeyColumn || !StringUtils.isNumeric(toString(row.get(rankIdKeyColumn), ""))) {
                break;
            }
            myRankConfigs.add(row);

        }
        //榜单ID 后一位是  榜单名称
        String rankNameKey = toString(rankConfigKeys.get(rankIdKeyColumn + 1), "");
        if (StringUtils.isBlank(rankNameKey)) {
            rankConfigKeys.set(rankIdKeyColumn + 1, "榜单名称");
        }

        List<Map<String, String>> rankMapList = toMapList(rankConfigKeys, myRankConfigs);
        List<String> phaseTimeKeys = Lists.newArrayList(phaseMap.values());
        List<RankInfo> rankInfos = Lists.newArrayList();
        //把榜单阶段配置多列变成一列
        for (Map<String, String> map : rankMapList) {
            String rankId = map.get(RANK_INFO_START_KEY);
            Map<Long, String> phaseConfigMap = Maps.newLinkedHashMap();
            for (int i = 0; i < phaseTimeKeys.size(); i++) {
                String phaseTimeString = phaseTimeKeys.get(i);
                long phaseId = phaseInfos.get(i).getPhaseIdAndName().getPhaseId();
                String phaseConfig = map.get(phaseTimeString);
                phaseConfigMap.put(phaseId, phaseConfig);
                map.remove(phaseTimeString);
            }
            Map<String, Object> rankMap = Maps.newHashMap(map);
            rankMap.put("phaseConfigs", phaseConfigMap);
            RankInfo rankInfo = JSON.parseObject(JSON.toJSONString(rankMap), RankInfo.class);
            rankInfos.add(rankInfo);
        }
        rankArea.setRankInfos(rankInfos);
        return rankArea;
    }

    private Map<String, Object> toMap(Map<String, ?> map) {
        return (Map<String, Object>) map;
    }

    private List<Map<String, String>> toMapList(List<Object> keys, List<List<Object>> valueLists) {
        List<Map<String, String>> mapList = Lists.newArrayList();
        for (List<Object> valueList : valueLists) {
            Map<String, String> map = Maps.newLinkedHashMap();
            boolean isValidRow = false;
            for (int i = 0; i < keys.size() && i < MAX_CELL; i++) {
                String key = toString(keys.get(i), null);
                String value = valueList.size() > i ? toString(valueList.get(i), null) : null;
                if (StringUtils.isNotBlank(key) || StringUtils.isNotBlank(value)) {
                    map.put(key, value);
                    isValidRow = true;
                }
            }
            if (isValidRow) {
                mapList.add(map);
            }
        }
        return mapList;

    }

    /**
     * 发现行的第一个有有效单词
     *
     * @param row
     * @return
     */
    private String findFirstWord(List<Object> row) {
        String firstWord = row.stream().map(item -> toString(item, null))
                .limit(MAX_CELL)
                .filter(StringUtils::isNotBlank)
                .findFirst().orElse("");

        return firstWord;
    }

    /**
     * 发现单词位置
     *
     * @param row
     * @param word
     * @return
     */
    private int findWordColumn(List<Object> row, String word) {
        return IntStream.range(0, row.size())
                .filter(i -> word.equals(toString(row.get(i), null)))
                .findFirst().orElse(-1);

    }

    /**
     * 读取单元格数据，会自动去除注释和首位空格
     *
     * @param obj
     * @param defaultValue
     * @return
     */
    public static String toString(Object obj, String defaultValue) {
        String string = Convert.toStr(obj, defaultValue);
        if (string == null) {
            return defaultValue;
        }
        return string.replaceAll(COMMENT_PREFIX + "[^\n]+", "").trim();
    }


}
