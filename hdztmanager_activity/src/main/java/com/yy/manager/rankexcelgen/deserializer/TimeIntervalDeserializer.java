package com.yy.manager.rankexcelgen.deserializer;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.yy.manager.rankexcelgen.bean.excel.TimeInterval;
import com.yy.manager.utils.DateUtil;
import com.yy.manager.utils.rankingexcelgen.RankingExcelUtil;

import java.lang.reflect.Type;

/**
 * @Author: CXZ
 * @Desciption: 时间区域反序列器
 * @Date: 2022/7/6 16:45
 * @Modified:
 */
public class TimeIntervalDeserializer implements ObjectDeserializer {


    @Override
    public  <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        final String input = parser.lexer.stringVal();
        String[] dates = RankingExcelUtil.getDates(input);
        TimeInterval timeInterval = new TimeInterval();
        timeInterval.setStartTime(DateUtil.getDate(dates[0],DateUtil.DEFAULT_PATTERN));
        timeInterval.setEndTime(DateUtil.getDate(dates[1],DateUtil.DEFAULT_PATTERN));
        return (T) timeInterval;
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_STRING;
    }
}
