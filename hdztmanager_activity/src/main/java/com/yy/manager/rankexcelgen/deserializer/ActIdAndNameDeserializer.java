package com.yy.manager.rankexcelgen.deserializer;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.yy.common.exception.ServiceException;
import com.yy.manager.rankexcelgen.bean.excel.ActIdAndName;

import java.lang.reflect.Type;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: CXZ
 * @Desciption: 活动id和名称反序列器
 * @Date: 2022/7/6 16:45
 * @Modified:
 */
public class ActIdAndNameDeserializer implements ObjectDeserializer {


    /**
     * 活动ID(活动名称)
     */
    private static final Pattern ACT_INFO_PATTERN =  Pattern.compile("^(\\d+)[\\(（](.+)[\\)）]$");
    @Override
    public  <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        final String input = parser.lexer.stringVal();

        Matcher matcher = ACT_INFO_PATTERN.matcher(input);
        //输入异常
        if (!matcher.find()) {
            throw new ServiceException("无法找到活动id和活动名称配置，" + input, 500);
        }
        ActIdAndName actIdAndName =  new ActIdAndName();
        actIdAndName.setActId(Long.parseLong(matcher.group(1)));
        actIdAndName.setActName(matcher.group(2));
        return (T) actIdAndName;
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_STRING;
    }
}
