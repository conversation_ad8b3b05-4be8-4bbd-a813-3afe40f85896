package com.yy.manager.rankexcelgen.deserializer;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.google.common.collect.Lists;
import com.yy.common.exception.ServiceException;
import com.yy.common.utils.StringUtils;
import com.yy.manager.rankexcelgen.bean.excel.RankPhaseConfig;
import com.yy.manager.rankexcelgen.bean.excel.PhasePkInfo;
import com.yy.manager.rankexcelgen.bean.excel.QualificationInfo;
import com.yy.manager.rankexcelgen.service.ActConfigExcelConvertor;

import java.lang.reflect.Type;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: CXZ
 * @Desciption: 榜单阶段配置反序列器
 * @Date: 2022/7/6 16:45
 * @Modified:
 */
public class PhaseConfigDeserializer implements ObjectDeserializer {

    /**
     * 空配置
     */
    private static final Pattern ALL_MEMBER_PATTERN = Pattern.compile("^(全)?报名者$");
    /**
     * 晋级来源
     * 2|12,偏24前24复2
     */
    private static final Pattern QUALIFICATION_PATTERN = Pattern.compile("^(\\d+)\\|(\\d+),(偏(\\d+))?([前后])(\\d+)(复(\\d+))?$");

    /**
     * *PK,近n/随,日/全,1002|12[3;0;1]|[3;2]
     */
    private static final Pattern PK_PATTERN = Pattern.compile("^\\*PK,([近随])(\\d+)?,([日全]),(\\d+)\\|(\\d+)(\\[\\d+;\\d+;\\d+\\])?(\\|(\\[\\d+(;\\d+)*]))?$");
    /**
     * 换行符
     */
    public static final String LINE_BREAK = "\n";

    @Override
    public  <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        final String input = parser.lexer.stringVal();
        parser.lexer.nextToken(JSONToken.COMMA);
        RankPhaseConfig phaseConfig = new RankPhaseConfig();
        String configStr = ActConfigExcelConvertor.toString(input, "");
        //中文符号替换成英文符号
        configStr =  configStr.replace("，", StrUtil.COMMA)
                .replace("；",";") ;
        //空和报名者忽略
        if (!StringUtils.isBlank(configStr) && !ALL_MEMBER_PATTERN.matcher(configStr).find()) {
            List<QualificationInfo> qualificationInfoList = Lists.newArrayList();
            List<PhasePkInfo> phasePkInfos = Lists.newArrayList();

            //换行
            for (String configStrItem : configStr.split(LINE_BREAK)) {
                configStrItem =  ActConfigExcelConvertor.toString(configStrItem, "");
                if (StringUtils.isBlank(configStr)){
                    continue;
                }
                Matcher qualificationMatcher = QUALIFICATION_PATTERN.matcher(configStrItem);
                Matcher pkMatcher = PK_PATTERN.matcher(configStrItem);
                if (qualificationMatcher.find()) {
                    QualificationInfo qualificationInfo =  new QualificationInfo();
                    qualificationInfo.setSrcRankId(Long.parseLong(qualificationMatcher.group(1)));
                    qualificationInfo.setSrcPhaseId(Long.parseLong(qualificationMatcher.group(2)));
                    qualificationInfo.setPassPolicy(qualificationMatcher.group(5));
                    qualificationInfo.setPassCount(Integer.parseInt(qualificationMatcher.group(6)));
                    qualificationInfo.setPassOffset(Convert.toInt(qualificationMatcher.group(4),0));
                    qualificationInfo.setPkReviveCount(Convert.toInt(qualificationMatcher.group(8),0));
                    qualificationInfoList.add(qualificationInfo);
                } else if (pkMatcher.find()) {
                    PhasePkInfo phasePkInfo = new PhasePkInfo();
                    phasePkInfo.setStrategy(pkMatcher.group(1));
                    phasePkInfo.setNum(Convert.toInt(pkMatcher.group(2),0));
                    phasePkInfo.setType(pkMatcher.group(3));
                    phasePkInfo.setSrcRankId(Long.parseLong(pkMatcher.group(4)));
                    phasePkInfo.setSrcPhaseId(Long.parseLong(pkMatcher.group(5)));
                    String pkAwardsStr = Convert.toStr(pkMatcher.group(6), "");
                    phasePkInfo.setPkAwards(pkAwardsStr.replace(";",  StrUtil.COMMA));
                    String rankAwardsStr = Convert.toStr(pkMatcher.group(8), "");
                    phasePkInfo.setRankAwards(rankAwardsStr.replace(";", StrUtil.COMMA));
                    phasePkInfos.add(phasePkInfo);
                }

                else {
                    throw new ServiceException("榜单阶段信息配置错误，" + configStrItem, 500);
                }
            }
            phaseConfig.setPhasePkInfos(phasePkInfos);
            phaseConfig.setSrcQualificationInfos(qualificationInfoList);
        }



        return (T) phaseConfig;
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_STRING;
    }
}
