package com.yy.manager.rankexcelgen.bean.excel;

import com.alibaba.fastjson.annotation.JSONField;
import com.yy.manager.rankexcelgen.deserializer.ExtMapDeserializer;
import com.yy.manager.rankexcelgen.deserializer.GiftDeserializer;
import com.yy.manager.rankexcelgen.deserializer.MemberDeserializer;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2022/6/29 22:24
 * @Modified:
 */
@Data
public class RankInfo {

    /**
     * 榜单id
     */
    @JSONField(name = "榜单ID")
    private long rankId;
    @JSONField(name = "榜单名称")
    private String rankName;
    private Map<Long, RankPhaseConfig> phaseConfigs;
    @JSONField(name = "角色配置:.", deserializeUsing = MemberDeserializer.class)
    private List<List<String>> role;
    @JSONField(name = "礼物配置", deserializeUsing = GiftDeserializer.class)
    private List<String> giftItem;
    @JSONField(name = "贡献榜MEMBER_KEY", deserializeUsing = MemberDeserializer.class)
    private List<List<String>> contributedMember;
    @JSONField(name = "榜单扩展属性键值", deserializeUsing = ExtMapDeserializer.class)
    private Map<String, String> extKeyMap;




}
