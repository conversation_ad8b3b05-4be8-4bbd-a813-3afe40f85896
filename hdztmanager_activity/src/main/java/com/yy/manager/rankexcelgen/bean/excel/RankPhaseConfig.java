package com.yy.manager.rankexcelgen.bean.excel;

import com.alibaba.fastjson.annotation.JSONType;
import com.google.common.collect.Lists;
import com.yy.manager.rankexcelgen.deserializer.PhaseConfigDeserializer;
import lombok.Data;

import java.util.List;

/**
 * @Author: CXZ
 * @Desciption: 榜单的阶段配置-晋级和pk
 * @Date: 2022/6/29 22:24
 * @Modified:
 */
@Data
@JSONType(deserializer = PhaseConfigDeserializer.class )
public class RankPhaseConfig {

    private List<QualificationInfo> srcQualificationInfos = Lists.newArrayList();

    private  List<PhasePkInfo> phasePkInfos;


}
