package com.yy.manager.rankexcelgen.bean.db;

import com.yy.manager.hdzt.entity.*;
import lombok.Data;

import java.util.List;

/**
 * @Author: CXZ
 * @Desciption: 存放活动相关的所有数据配置
 * @Date: 2022/7/12 16:14
 * @Modified:
 */
@Data
public class ActAllInfo {

    private HdztActivity activity;
    private List<HdztActor> actors;
    private List<RankingItemTransform> itemTransforms;
    private List<RankingPhaseGroup> rankingPhaseGroups;
    private List<RankingPhase> phaseInfos;
    private List<RankingConfig> rankingConfigs;
    private List<RankingMember> rankingMembers;
    private List<RankingItem> rankingItems;
    private List<RankingPhaseQualification> rankingPhaseQualifications;
    private List<RankingPhasePk> rankingPhasePks;
    private List<RankingPhasePkgroup> rankingPhasePkgroups;
    private List<RankingConfigAttr> rankingConfigAttrs;
}
