package com.yy.manager.rankexcelgen.deserializer;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.yy.common.exception.ServiceException;
import com.yy.common.utils.StringUtils;
import com.yy.manager.rankexcelgen.bean.excel.PhaseGroupAndName;

import java.lang.reflect.Type;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: CXZ
 * @Desciption: 阶段分组code和名称反序列器
 * @Date: 2022/7/6 16:45
 * @Modified:
 */
public class PhaseGroupAndNameDeserializer implements ObjectDeserializer {

    /**
     *   王牌主播分区(WPZBFQ)
     */
    private static final Pattern PHASE_GROUP_PATTERN =  Pattern.compile("^([a-zA-Z0-9_\\u4e00-\\u9fa5]+)\\((\\w+)\\)$");


    @Override
    public  <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        final String input = parser.lexer.stringVal();
        Matcher matcher = PHASE_GROUP_PATTERN.matcher(input);
        if (StringUtils.isBlank(input)) {
            return null;
        }
        //输入异常
        if (!matcher.find()) {
            throw new ServiceException("阶段分组错误，请用 阶段组名称(阶段组code) 表示" + input, 500);
        }

        PhaseGroupAndName phaseGroupAndName =  new PhaseGroupAndName();
        phaseGroupAndName.setCode(matcher.group(2));
        phaseGroupAndName.setName(matcher.group(1));
        return (T) phaseGroupAndName;
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_STRING;
    }
}
