package com.yy.manager.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.07.23 10:06
 */
@Component
public class JdbcTemplateGroupFactory {

    @Autowired
    Map<String, JdbcTemplate> jdbcTemplateMap;

    public JdbcTemplate getJdbcTemplateByName(String name) {
        return jdbcTemplateMap.get(name);
    }

}
