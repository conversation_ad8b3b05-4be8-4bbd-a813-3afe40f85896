package com.yy.manager.ecology.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.manager.ecology.entity.ActLayerViewDefine;
import com.yy.manager.ecology.service.IActLayerViewDefineService;
import com.yy.manager.utils.JsonUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 活动浮层挂件tab展示配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
@RestController
@RequestMapping("/viewDefine")
public class ActLayerViewDefineController {

    @Autowired
    private IActLayerViewDefineService actLayerViewDefineService;

    @ApiOperation("查询挂件tab")
    @PreAuthorize("@ss.hasPermi('activity:viewDefine:query')")
    @GetMapping("/queryViewDefine")
    public String viewDefine(Integer actId) {
        if (actId == null || actId <= 0) {
            return JsonUtil.makeJsonResult("0", null, null);
        }
        ActLayerViewDefine define = new ActLayerViewDefine();
        define.setActId(actId);
        List<ActLayerViewDefine> list = actLayerViewDefineService.list(actId, new QueryWrapper<>(define));
        return JsonUtil.makeJsonResult("0", list, null);
    }

    @ApiOperation("新增挂件tab")
    @Log(title = "新增挂件tab", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('activity:viewDefine:add')")
    @PostMapping("/addViewDefine")
    public String addViewDefine(@RequestBody ActLayerViewDefine viewDefine) {
        if (viewDefine == null || viewDefine.getActId() == null || viewDefine.getItemTypeKey() == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }

        if (actLayerViewDefineService.save(viewDefine.getActId(), viewDefine)) {
            return JsonUtil.makeJsonResult("0", "添加成功", null);
        } else {
            return JsonUtil.makeJsonResult("1", "添加失败!", null);
        }
    }

    @ApiOperation("更新挂件tab")
    @Log(title = "更新挂件tab", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('activity:viewDefine:edit')")
    @PostMapping("/updateViewDefine")
    public String updateViewDefine(@RequestBody ActLayerViewDefine define) {
        if (define == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }
        UpdateWrapper<ActLayerViewDefine> updateWrapper = new UpdateWrapper<>();

        ActLayerViewDefine viewDefine = new ActLayerViewDefine();
        viewDefine.setActId(define.getActId());
        viewDefine.setItemTypeKey(define.getItemTypeKey());
        updateWrapper.setEntity(viewDefine);

        if (actLayerViewDefineService.update(define.getActId(), define, updateWrapper)) {
            return JsonUtil.makeJsonResult("0", "更新成功", null);
        } else {
            return JsonUtil.makeJsonResult("1", "更新失败!", null);
        }
    }

    @ApiOperation("复制挂件tab")
    @Log(title = "复制挂件tab", businessType = BusinessType.COPY)
    @PreAuthorize("@ss.hasPermi('activity:viewDefine:copy')")
    @PostMapping("/copyViewDefine")
    public String copyViewDefine(@RequestBody JSONObject data) {
        if (data == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }
        Integer srcActId = data.getInteger("srcActId");
        Integer targetActId = data.getInteger("targetActId");
        Date startShowTime = data.getDate("startShowTime");
        Date endShowTime = data.getDate("endShowTime");
        if (srcActId == null || targetActId == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }
        if (actLayerViewDefineService.copyConfig(srcActId, targetActId, startShowTime, endShowTime)) {
            return JsonUtil.makeJsonResult("0", "复制成功", null);
        } else {
            return JsonUtil.makeJsonResult("1", "复制失败!", null);
        }
    }

}
