package com.yy.manager.ecology.mapper;

import com.yy.manager.ecology.entity.ActTaskScore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
public interface ActTaskScoreDao extends BaseMapper<ActTaskScore> {

    /**
     *
     * @param taskScores
     * @return
     */
    boolean replaceIntoTasks(@Param(value="list") List<ActTaskScore> taskScores);
}
