package com.yy.manager.ecology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * <p>
 * 方法执行日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@TableName("ge_method_retry")
public class GeMethodRetry implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统分组
     */
    private String groupCode;

    /**
     * ge_method_log 的id
     */
    private Long logId;

    /**
     * 日志标识md5
     */
    private String logIndex;

    /**
     * 方法执行时间
     */
    private Long executionTime;

    /**
     * 重试索引，表示是第几次重试
     */
    private Integer retryIndex;

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }
    public String getLogIndex() {
        return logIndex;
    }

    public void setLogIndex(String logIndex) {
        this.logIndex = logIndex;
    }
    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }
    public Integer getRetryIndex() {
        return retryIndex;
    }

    public void setRetryIndex(Integer retryIndex) {
        this.retryIndex = retryIndex;
    }

    @Override
    public String toString() {
        return "GeMethodRetry{" +
            "groupCode=" + groupCode +
            ", logId=" + logId +
            ", logIndex=" + logIndex +
            ", executionTime=" + executionTime +
            ", retryIndex=" + retryIndex +
        "}";
    }
}
