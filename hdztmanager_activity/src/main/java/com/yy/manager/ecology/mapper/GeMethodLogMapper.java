package com.yy.manager.ecology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.ecology.entity.GeMethodLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 方法执行日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
public interface GeMethodLogMapper extends BaseMapper<GeMethodLog> {

    /**
     * 查询方法id
     * @param group
     * @return
     */
    @Select("select method_id,MAX(method_show) as method_show  from ge_method_log where group_code = #{group} group by method_id")
    List<GeMethodLog> queryMethodLabelByGroup(String group);
    /**
     * 查询
     * @param geMethodLog
     * @return
     */
    List<GeMethodLog> queryGeMethodLogs( @Param("entity")GeMethodLog geMethodLog);
}
