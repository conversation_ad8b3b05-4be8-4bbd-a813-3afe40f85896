package com.yy.manager.ecology.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.ImmutableMap;
import com.yy.manager.ecology.entity.GeParameter;
import com.yy.manager.ecology.entity.HdzjComponent;
import com.yy.manager.ecology.service.GeSupportService;
import com.yy.manager.ecology.service.IGeParameterService;
import com.yy.manager.ecology.service.IHdzjComponentService;
import com.yy.manager.exception.SuperException;
import com.yy.manager.utils.ActGroupHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2021/12/7 19:15
 * @Modified:
 */
@Service
@DS("#{@ad.getGameeCologyDs(#actId)}")
public class GeSupportServiceImpl implements GeSupportService {
    @Autowired
    private IHdzjComponentService hdzjComponentService;
    @Autowired
    private IGeParameterService geParameterService;

    @Override
    public JSONArray queryComponentList(long cmptId, Long actId) {
        LambdaQueryWrapper<HdzjComponent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HdzjComponent::getCmptId, cmptId);
        if (actId != null) {
            queryWrapper.eq(HdzjComponent::getActId, actId);
        }
        List<HdzjComponent> list = hdzjComponentService.list(actId, queryWrapper);
        Map<Integer, List<HdzjComponent>> collect =
                list.stream()
                        .sorted(Comparator.comparing(HdzjComponent::getActId).reversed().thenComparingInt(HdzjComponent::getCmptUseInx))
                        .collect(Collectors.groupingBy(HdzjComponent::getActId, LinkedHashMap::new, Collectors.toList()));
        JSONArray array = new JSONArray();
        for (Map.Entry<Integer, List<HdzjComponent>> entry : collect.entrySet()) {
            JSONObject hdzjComponent = new JSONObject(true);
            hdzjComponent.put("act_id", entry.getKey());
            List<Object> collect1 = entry.getValue().stream()
                    .map(item -> new JSONObject(ImmutableMap.of("use_inx", item.getCmptUseInx(), "title", item.getCmptTitle())))
                    .collect(Collectors.toList());
            hdzjComponent.put("component_list", collect1);
            array.add(hdzjComponent);
        }
        return array;

    }

    @Override
    public JSONObject queryActServerGroupInfo(long actId) {
        int actGroup = ActGroupHelper.getActGroup(actId);
        LambdaQueryWrapper<GeParameter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GeParameter::getName, "group_server_config");
        List<GeParameter> list = geParameterService.list(actId, queryWrapper);
        if (list.isEmpty()) {
            throw new SuperException("无法找到配置");
        }
        JSONObject groupConfig = JSONObject.parseObject(list.get(0).getValue()).getJSONObject(String.valueOf(actGroup));
        if (groupConfig == null) {
            throw new SuperException(actGroup + "分组无法找到配置，活动id=" + actId);
        }
        groupConfig.put("group", actGroup);
        return groupConfig;
    }

}
