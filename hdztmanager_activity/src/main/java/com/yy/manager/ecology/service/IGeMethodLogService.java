package com.yy.manager.ecology.service;

import com.yy.manager.ecology.entity.GeMethodLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.ecology.entity.RetryMethodLabelVo;
import com.yy.manager.utils.Response;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 方法执行日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
public interface IGeMethodLogService extends IService<GeMethodLog> {

    /**
     * 查找方法标签
     * @param groupCode
     * @return
     */
    List<RetryMethodLabelVo>  queryMethodLabel(String groupCode);

    /**
     * 查询
     * @param geMethodLog
     * @return
     */
     List<GeMethodLog> queryGeMethodLogs(GeMethodLog geMethodLog);

    /**
     * 执行重试
     * @param request
     * @param geMethodLog
     * @return
     */
    Response retryMethod(HttpServletRequest request, GeMethodLog geMethodLog);
}
