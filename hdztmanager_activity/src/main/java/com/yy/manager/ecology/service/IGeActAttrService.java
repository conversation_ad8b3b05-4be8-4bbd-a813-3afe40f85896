package com.yy.manager.ecology.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.ecology.entity.GeActAttr;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/29 18:33
 **/
public interface IGeActAttrService extends IService<GeActAttr> {
    /**
     *
     * @param actId
     * @param attrName
     * @return
     */
    GeActAttr select(long actId, String attrName);

    /**
     *
     * @param actId
     * @return
     */
    int updateActivityGrey(long actId);

    /**
     *
     * @param actId
     * @param wrapper
     * @return
     */
    List<GeActAttr> list(long actId, QueryWrapper wrapper);

    /**
     *
     * @param actId
     * @param entity
     * @return
     */
    boolean save(long actId, GeActAttr entity);

    /**
     *
     * @param actId
     * @param attrName
     * @return
     */
    int updateActivityGrey(long actId, String attrName);

    /**
     *
     * @param actId
     * @param attrName
     * @return
     */
    int updateActivityGrey2(long actId, String attrName);

    /**
     *
     * @param actId
     * @return
     */
    int deleteAwardRecord(long actId);

    /**
     *
     * @param actId
     * @return
     */
    int deleteTaskAwardRecord(long actId);

    /**
     *
     * @param actId
     * @return
     */
    int updateActResult(long actId);

    /**
     *
     * @param actId
     * @return
     */
    int countAwardRecord(long actId);

    /**
     *
     * @param actId
     * @return
     */
    int countTaskAwardRecord(long actId);

    /**
     *
     * @param actId
     * @return
     */
    int countActResult(long actId);

    /**
     *
     * @param actId
     * @param attrName
     * @return
     */
    String queryActivityAttr(long actId, String attrName);

    /**
     *
     * @param actId
     * @param attrName
     * @return
     */
    String queryActivityGrey(long actId, String attrName);

    /**
     *
     * @param actId
     * @param attrName
     * @return
     */
    String queryActivityGreyNew(long actId, String attrName);

    int saveArchiveGroupCode(long actId, int archiveGroup);

    int insertOrUpdate(long actId,GeActAttr entity);

    int deleteActAttr(long actId, String attrName);

    int countHdzjComponent(long actId,long componentId);

    int deleteComponentData(String tableName, long actId);


    long countComponentData(String tableName, long actId);
}
