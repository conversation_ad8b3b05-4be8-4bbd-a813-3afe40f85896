package com.yy.manager.ecology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.ecology.entity.ActShowRankConfig;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/22 15:17
 **/
public interface ActShowRankConfigService extends IService<ActShowRankConfig> {
    /**
     * 获取列表
     *
     * @param actId 活动id
     * @return 榜单展示列表
     **/
    List<ActShowRankConfig> list(long actId);

    /**
     * 删除指定数据
     *
     * @param actId      活动id
     * @param excludeIds 排除掉的id
     **/
    void remove(long actId, List<Long> excludeIds);

    /**
     * 批量保存或更新
     *
     * @param actId      活动id
     * @param entityList 实体列表
     * @return 保存的结果
     **/
    boolean saveOrUpdateBatch(long actId, Collection<ActShowRankConfig> entityList);

    /**
     * 批量保存
     *
     * @param actId      活动id
     * @param entityList 实体列表
     * @return 保存结果
     **/
    boolean saveBatch(long actId, Collection<ActShowRankConfig> entityList);
}
