package com.yy.manager.ecology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/2/18 17:53
 **/
@TableName("ge_broadcast_timer_config")
@Data
public class GeBroadcastTimerConfig implements Serializable {
    @Id
    private Long id;
    private Long actId;
    private String broadcastTime;
    private String broadcastTxt;
    private String broadcastRanges;
    private Integer status;
    private Date createTime;
    private Date updateTime;
    private Long operateUid;
    private String remark;
}
