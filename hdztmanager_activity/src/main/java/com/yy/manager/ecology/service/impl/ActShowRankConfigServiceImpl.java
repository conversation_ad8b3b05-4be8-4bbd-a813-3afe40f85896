package com.yy.manager.ecology.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yy.manager.ecology.entity.ActShowRankConfig;
import com.yy.manager.ecology.mapper.ActShowRankConfigMapper;
import com.yy.manager.ecology.service.ActShowRankConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/22 15:18
 **/
@Service
@DS("#{@ad.getGameeCologyDs(#actId)}")
public class ActShowRankConfigServiceImpl extends ServiceImpl<ActShowRankConfigMapper, ActShowRankConfig> implements ActShowRankConfigService {
    @Resource
    private ActShowRankConfigMapper mapper;

    @Override
    public List<ActShowRankConfig> list(long actId) {
        QueryWrapper<ActShowRankConfig> query = new QueryWrapper<>();
        query.eq("act_id", actId);

        return mapper.selectList(query);
    }

    @Override
    public void remove(long actId, List<Long> excludeIds) {
        QueryWrapper<ActShowRankConfig> query = new QueryWrapper<>();
        query.eq("act_id", actId);
        if (CollectionUtils.isNotEmpty(excludeIds)) {
            query.notIn("id", excludeIds);
        }

        mapper.delete(query);
    }

    @Override
    public boolean saveOrUpdateBatch(long actId, Collection<ActShowRankConfig> entityList) {
        return saveOrUpdateBatch(entityList);
    }

    @Override
    public boolean saveBatch(long actId, Collection<ActShowRankConfig> entityList) {
        return saveBatch(entityList);
    }
}
