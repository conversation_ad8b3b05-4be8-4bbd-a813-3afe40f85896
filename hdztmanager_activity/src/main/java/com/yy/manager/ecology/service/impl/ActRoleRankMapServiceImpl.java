package com.yy.manager.ecology.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yy.common.utils.spring.SpringUtils;
import com.yy.manager.ecology.entity.ActRoleRankMap;
import com.yy.manager.ecology.mapper.ActRoleRankMapDao;
import com.yy.manager.ecology.service.IActRoleRankMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
@Service
@DS("#{@ad.getGameeCologyDs(#actId)}")
public class ActRoleRankMapServiceImpl extends ServiceImpl<ActRoleRankMapDao, ActRoleRankMap> implements IActRoleRankMapService {

    @Autowired
    private ActRoleRankMapDao actRoleRankMapDao;

    @Override
    @DS("#{@ad.getGameeCologyDs(#srcActId)}")
    public boolean copyConfig(Integer srcActId, Integer targetActId) {
        ActRoleRankMap define = new ActRoleRankMap();
        define.setActId(srcActId);
        List<ActRoleRankMap> list = list(new QueryWrapper<>(define));
        ActRoleRankMapServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        return aopProxy.copyToTarget(targetActId, srcActId, list);
    }

    @DS("#{@ad.getGameeCologyDs(#targetActId)}")
    public boolean copyToTarget(Integer targetActId, Integer srcActId, List<ActRoleRankMap> srcList) {
        int incr = (targetActId - srcActId) * 10000;
        List<ActRoleRankMap> targetList = srcList.stream().map(s -> {
            s.setActId(targetActId);
            s.setId(s.getId() + incr);
            return s;
        }).collect(Collectors.toList());
        return this.saveBatch(targetList);
    }

    /**
     * 准备一个id
     */
    public long readyActRoleRankMapId(Integer actId) {
        Long id = actRoleRankMapDao.getMaxActRoleRankMapId(actId);
        return id == null ? actId.longValue() * 10000 + 1 : id + 1;
    }

    @Override
    public List<ActRoleRankMap> list(Integer actId) {
        ActRoleRankMap define = new ActRoleRankMap();
        define.setActId(actId);
        return list(new QueryWrapper<>(define));
    }

    @Override
    public boolean save(Integer actId, ActRoleRankMap entity) {
        Long id = entity.getId();
        if(id == null || id == 0) {
            id = readyActRoleRankMapId(actId);
            entity.setId(id);
        }
        return save(entity);
    }

    @Override
    public boolean update(Integer actId, ActRoleRankMap entity, Wrapper<ActRoleRankMap> updateWrapper) {
        return update(entity, updateWrapper);
    }
}
