package com.yy.manager.ecology.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.manager.ecology.entity.ActRoleRankMap;
import com.yy.manager.ecology.service.IActRoleRankMapService;
import com.yy.manager.utils.JsonUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
@RestController
@RequestMapping("/roleRankMap")
public class ActRoleRankMapController {

    @Autowired
    private IActRoleRankMapService actRoleRankMapService;

    @ApiOperation("查询活动挂件成员")
    @PreAuthorize("@ss.hasPermi('activity:roleRankMap:query')")
    @GetMapping("/queryRoleRankMap")
    public String queryRoleRankMap(Integer actId) {
        if (actId == null || actId <= 0) {
            return JsonUtil.makeJsonResult("0", null, null);
        }
        List<ActRoleRankMap> list = actRoleRankMapService.list(actId);
        return JsonUtil.makeJsonResult("0", list, null);
    }

    @ApiOperation("新增活动挂件成员")
    @Log(title = "新增活动挂件成员", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('activity:roleRankMap:add')")
    @PostMapping("/addRoleRankMap")
    public String addRankMap(@RequestBody ActRoleRankMap rankMap) {
        if (rankMap == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }

        if (actRoleRankMapService.save(rankMap.getActId(), rankMap)) {
            return JsonUtil.makeJsonResult("0", "添加成功", null);
        } else {
            return JsonUtil.makeJsonResult("1", "添加失败!", null);
        }
    }

    @ApiOperation("更新活动挂件成员")
    @Log(title = "更新活动挂件成员", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('activity:roleRankMap:copy')")
    @PostMapping("/updateRoleRankMap")
    public String updateRankMap(@RequestBody ActRoleRankMap rankMap) {
        if (rankMap == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }
        UpdateWrapper<ActRoleRankMap> updateWrapper = new UpdateWrapper<>();
        ActRoleRankMap roleRankMap = new ActRoleRankMap();
        roleRankMap.setId(rankMap.getId());

        updateWrapper.setEntity(roleRankMap);

        if (actRoleRankMapService.update(rankMap.getActId(), rankMap, updateWrapper)) {
            return JsonUtil.makeJsonResult("0", "更新成功", null);
        } else {
            return JsonUtil.makeJsonResult("1", "更新失败!", null);
        }
    }

    @ApiOperation("复制活动挂件成员")
    @Log(title = "复制活动挂件成员", businessType = BusinessType.COPY)
    @PreAuthorize("@ss.hasPermi('activity:roleRankMap:copy')")
    @PostMapping("/copyRoleRankMap")
    public String copyRoleRankMap(@RequestBody JSONObject data) {
        if (data == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }
        Integer srcActId = data.getInteger("srcActId");
        Integer targetActId = data.getInteger("targetActId");

        if (srcActId == null || targetActId == null) {
            return JsonUtil.makeJsonResult("1", "参数错误!", null);
        }
        if (actRoleRankMapService.copyConfig(srcActId, targetActId)) {
            return JsonUtil.makeJsonResult("0", "复制成功", null);
        } else {
            return JsonUtil.makeJsonResult("1", "复制失败!", null);
        }
    }
}
