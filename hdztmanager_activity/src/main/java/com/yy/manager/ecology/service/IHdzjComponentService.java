package com.yy.manager.ecology.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.ecology.entity.HdzjComponent;

import java.util.List;

/**
 * <p>
 * 活动浮层挂件展示配置 服务类
 * </p>
 *
 * <AUTHOR> @since 2021-07-21
 */
public interface IHdzjComponentService extends IService<HdzjComponent> {
    /**
     *
     * @param actId
     * @param componentId
     * @param useIndex
     * @return
     */
    HdzjComponent get(long actId, int componentId, int useIndex);

    /**
     *
     * @param actId
     * @param targetActId
     * @param cmptId
     * @param index
     */
    void copyComponent(long actId, long targetActId, long cmptId, long index);

    /**
     *
     * @param actId
     * @param queryWrapper
     * @return
     */
    List<HdzjComponent> list(long actId, Wrapper queryWrapper);

    /**
     *
     * @param actId
     * @param entity
     * @return
     */
    boolean save(long actId, HdzjComponent entity);

    boolean saveBatch(long actId, List<HdzjComponent> entity);
    /**
     *
     * @param actId
     * @param queryWrapper
     * @return
     */
    boolean remove(long actId, Wrapper queryWrapper);
}
