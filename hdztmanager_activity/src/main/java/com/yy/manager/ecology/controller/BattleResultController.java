package com.yy.manager.ecology.controller;

import com.yy.manager.ecology.entity.ActResult;
import com.yy.manager.ecology.entity.ActResultGroup;
import com.yy.manager.ecology.service.IActResultGroupService;
import com.yy.manager.ecology.service.IActResultService;
import com.yy.manager.tools.bean.LoginInfoBean;
import com.yy.manager.utils.IEAFConst;
import com.yy.manager.utils.JsonUtil;
//import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/1
 * @功能说明
 * @版本更新列表
 */
@Controller
@RequestMapping("/BattleResult")
public class BattleResultController {
    @Autowired
    IActResultService actResultService;

    @Autowired
    IActResultGroupService actResultGroupService;

    private static Logger log = LoggerFactory.getLogger(BattleResultController.class);

    @RequestMapping("/selectActResult.do")
    @ResponseBody
    public List<List<Object>> selectActResult(HttpServletRequest request, Map<String, Object> extra, long actId) throws Exception {
        //ActResult actResult = (ActResult) JsonUtil.toBean(JSONObject.fromObject(request.getParameter("actResult")), ActResult.class);
        //log.info("selectActResult base->"+actResult+"|| extra->"+extra);
        List<List<Object>> res = new ArrayList<>();
        List<ActResult> actResultList = actResultService.select(actId, extra, null);
        List<ActResultGroup> actResultGroupList = actResultGroupService.select(actId, null, null, null);
        for (ActResult result : actResultList) {
            List<Object> list = new ArrayList<>();
            list.add(result);
            ActResultGroup actResultGroup = null;
            for (ActResultGroup resultGroup : actResultGroupList) {
                if (result.getActId().equals(resultGroup.getActId()) && result.getType().equals(resultGroup.getType()) && result.getGroupId().equals(resultGroup.getGroupId())) {
                    actResultGroup = resultGroup;
                    break;
                }
            }
            list.add(actResultGroup);
            res.add(list);
        }
        return res;
    }

    @RequestMapping("/updateActResult.do")
    @ResponseBody
    public Map<String, String> updateMemberId(HttpServletRequest request, @RequestBody ActResult actResult) throws Exception {
        //ActResult actResult = (ActResult) JsonUtil.toBean(JSONObject.fromObject(request.getParameter("actResult")), ActResult.class);

        Long uid = Long.valueOf(-1);
        String uname = "";
        LoginInfoBean li = (LoginInfoBean) request.getSession().getAttribute(IEAFConst.LOGIN_INFO);
        if (li != null) {
            uid = li.getUsrid();
            uname = li.getUname();
        }

        log.info("UID:" + uid + " UNAME:" + uname + " updateMemberId: updateActResultByKey to->" + actResult);
        int count = actResultService.updateByKey(actResult.getActId(), actResult);
        Map<String, String> res = new HashMap<String, String>(1);
        if (count == 1) {
            res.put("result", "success");
        } else {
            res.put("result", "update fail");
        }
        return res;
    }

}
