package com.yy.manager.ecology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.ecology.entity.GeBroadcastRankResultConfig;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/18 15:20
 **/
public interface GeBroadcastRankResultConfigService extends IService<GeBroadcastRankResultConfig> {
    /**
     *
     * @param actId
     * @param moduleType
     * @return
     */
    GeBroadcastRankResultConfig get(long actId, int moduleType);

    /**
     *
     * @param actId
     * @param moduleType
     * @param rankId
     * @param phaseId
     * @param uri
     * @return
     */
    GeBroadcastRankResultConfig get(long actId, int moduleType, int rankId, int phaseId, int uri);

    /**
     *
     * @param actId
     * @return
     */
    List<GeBroadcastRankResultConfig> list(long actId);

    /**
     *
     * @param actId
     */
    void remove(long actId);

    /**
     *
     * @param actId
     * @param entityList
     * @return
     */
    boolean saveBatch(long actId, Collection<GeBroadcastRankResultConfig> entityList);

    /**
     *
     * @param actId
     * @param entity
     * @return
     */
    boolean saveOrUpdate(long actId, GeBroadcastRankResultConfig entity);
}
