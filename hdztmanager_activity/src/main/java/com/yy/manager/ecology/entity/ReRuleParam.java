package com.yy.manager.ecology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * <p>
 * 规则参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-23
 */
@TableName("re_rule_param")
public class ReRuleParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则id
     */
    private Long ruleId;

    /**
     * 参数名
     */
    private String paramKey;

    /**
     * 参数类型 number/string/boolean
     */
    private String paramType;

    private String paramDefault;

    /**
     * 参数描述
     */
    private String paramDesc;

    /**
     * 参数来源 0-调用方传入 1-表达式 2-函数 3-事件
     */
    private Integer paramSource;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 函数
     */
    private String functionKey;

    /**
     * 函数参数
     */
    private String functionParam;

    /**
     * 是否可视化参数
     */
    private Boolean visualStatus;

    /**
     * 事件id
     */
    private Integer eventId;

    /**
     * 事件活动id
     */
    private Long eventActId;

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }
    public String getParamKey() {
        return paramKey;
    }

    public void setParamKey(String paramKey) {
        this.paramKey = paramKey;
    }
    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }
    public String getParamDefault() {
        return paramDefault;
    }

    public void setParamDefault(String paramDefault) {
        this.paramDefault = paramDefault;
    }
    public String getParamDesc() {
        return paramDesc;
    }

    public void setParamDesc(String paramDesc) {
        this.paramDesc = paramDesc;
    }
    public Integer getParamSource() {
        return paramSource;
    }

    public void setParamSource(Integer paramSource) {
        this.paramSource = paramSource;
    }
    public String getExpression() {
        return expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }
    public String getFunctionKey() {
        return functionKey;
    }

    public void setFunctionKey(String functionKey) {
        this.functionKey = functionKey;
    }
    public String getFunctionParam() {
        return functionParam;
    }

    public void setFunctionParam(String functionParam) {
        this.functionParam = functionParam;
    }
    public Boolean getVisualStatus() {
        return visualStatus;
    }

    public void setVisualStatus(Boolean visualStatus) {
        this.visualStatus = visualStatus;
    }
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }
    public Long getEventActId() {
        return eventActId;
    }

    public void setEventActId(Long eventActId) {
        this.eventActId = eventActId;
    }

    @Override
    public String toString() {
        return "ReRuleParam{" +
            "ruleId=" + ruleId +
            ", paramKey=" + paramKey +
            ", paramType=" + paramType +
            ", paramDefault=" + paramDefault +
            ", paramDesc=" + paramDesc +
            ", paramSource=" + paramSource +
            ", expression=" + expression +
            ", functionKey=" + functionKey +
            ", functionParam=" + functionParam +
            ", visualStatus=" + visualStatus +
            ", eventId=" + eventId +
            ", eventActId=" + eventActId +
        "}";
    }
}
