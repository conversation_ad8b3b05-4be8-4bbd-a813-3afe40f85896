package com.yy.manager.ecology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 方法执行日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@TableName("ge_method_log")
public class GeMethodLog implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private long id;
    /**
     * 系统分组
     */
    private String groupCode;

    /**
     * 方法id，定义在MethodId，name()
     */
    private String methodId;

    /**
     * 日志标识md5

     */
    private String logIndex;

    /**
     * 实例名称
     */
    private String beanName;

    /**
     * 方法名
     */
    private String method;

    /**
     * 参数中的seq,必须有，用来去重
     */
    private String seq;

    /**
     * 调用参数
     */
    private String params;

    /**
     * 方法描述 定义在MethodId，desc()
     */
    private String methodShow;

    /**
     * 方法执行时间
     */
    private Long time;

    /**
     * 执行状态，0=执行失败，1=执行成功
     */
    private Boolean status;

    /**
     * 异常详情
     */
    private String exceptionDetail;

    /**
     * 重试的日志id，0表示是系统调用非重试记录
     */
    private Long fromId;

    /**
     * 重试索引，第一次是0，往后自动重试递增1
     */
    private Integer retryIndex;

    private Date createTime;

    /**
     * 执行人，系统执行是system
     */
    private String operator;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getMethodId() {
        return methodId;
    }

    public void setMethodId(String methodId) {
        this.methodId = methodId;
    }
    public String getLogIndex() {
        return logIndex;
    }

    public void setLogIndex(String logIndex) {
        this.logIndex = logIndex;
    }
    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }
    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }
    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }
    public String getMethodShow() {
        return methodShow;
    }

    public void setMethodShow(String methodShow) {
        this.methodShow = methodShow;
    }
    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }
    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
    public String getExceptionDetail() {
        return exceptionDetail;
    }

    public void setExceptionDetail(String exceptionDetail) {
        this.exceptionDetail = exceptionDetail;
    }
    public Long getFromId() {
        return fromId;
    }

    public void setFromId(Long fromId) {
        this.fromId = fromId;
    }
    public Integer getRetryIndex() {
        return retryIndex;
    }

    public void setRetryIndex(Integer retryIndex) {
        this.retryIndex = retryIndex;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Override
    public String toString() {
        return "GeMethodLog{" +
            "groupCode=" + groupCode +
            ", methodId=" + methodId +
            ", logIndex=" + logIndex +
            ", beanName=" + beanName +
            ", method=" + method +
            ", seq=" + seq +
            ", params=" + params +
            ", methodShow=" + methodShow +
            ", time=" + time +
            ", status=" + status +
            ", exceptionDetail=" + exceptionDetail +
            ", fromId=" + fromId +
            ", retryIndex=" + retryIndex +
            ", createTime=" + createTime +
            ", operator=" + operator +
        "}";
    }
}
