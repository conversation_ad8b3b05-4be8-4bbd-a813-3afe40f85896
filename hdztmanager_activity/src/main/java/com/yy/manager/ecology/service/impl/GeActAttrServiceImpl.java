package com.yy.manager.ecology.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yy.manager.ecology.entity.GeActAttr;
import com.yy.manager.ecology.mapper.GeActAttrMapper;
import com.yy.manager.ecology.service.IGeActAttrService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/29 18:34
 **/
@Service
@DS("#{@ad.getGameeCologyDs(#actId)}")
public class GeActAttrServiceImpl extends ServiceImpl<GeActAttrMapper, GeActAttr> implements IGeActAttrService {
    @Resource
    private GeActAttrMapper geActAttrMapper;

    @DS("#{@ad.getGameeCologyDs(#actId)}")
    @Override
    public GeActAttr select(long actId, String attrName) {
        return geActAttrMapper.select(actId, attrName);
    }


    @DS("#{@ad.getGameeCologyDs(#actId)}")
    @Override
    public int updateActivityGrey(long actId) {
        String attrName = "activity_grey_env_" + actId;

        int result = geActAttrMapper.updateActivityGrey(actId, attrName);
        result += geActAttrMapper.updateActivityGrey2(actId, "activity_grey_status");

        return result;
    }

    @DS("#{@ad.getGameeCologyDs(#actId)}")
    @Override
    public List<GeActAttr> list(long actId, QueryWrapper wrapper) {
        return this.list(wrapper);
    }

    @DS("#{@ad.getGameeCologyDs(#actId)}")
    @Override
    public boolean save(long actId, GeActAttr entity) {
        return save(entity);
    }

    @Override
    public int updateActivityGrey(long actId, String attrName) {
        return geActAttrMapper.updateActivityGrey(actId, attrName);
    }

    @Override
    public int updateActivityGrey2(long actId, String attrName) {
        return geActAttrMapper.updateActivityGrey2(actId, attrName);
    }

    @Override
    public int deleteAwardRecord(long actId) {
        return geActAttrMapper.deleteAwardRecord(actId);
    }

    @Override
    public int deleteTaskAwardRecord(long actId) {
        return geActAttrMapper.deleteTaskAwardRecord(actId);
    }

    @Override
    public int updateActResult(long actId) {
        return geActAttrMapper.updateActResult(actId);
    }

    @Override
    public int countAwardRecord(long actId) {
        return geActAttrMapper.countAwardRecord(actId);
    }

    @Override
    public int countTaskAwardRecord(long actId) {
        return geActAttrMapper.countTaskAwardRecord(actId);
    }

    @Override
    public int countActResult(long actId) {
        return geActAttrMapper.countActResult(actId);
    }

    @Override
    public String queryActivityAttr(long actId, String attrName) {
        return null;
    }

    @Override
    public String queryActivityGrey(long actId, String attrName) {
        return geActAttrMapper.queryActivityGrey(actId, attrName);
    }

    @Override
    public String queryActivityGreyNew(long actId, String attrName) {
        return geActAttrMapper.queryActivityGreyNew(actId, attrName);
    }

    @Override
    @DS("#{@ad.getGameeCologyDs(#actId)}")
    public int saveArchiveGroupCode(long actId, int archiveGroup) {
        GeActAttr entity = new GeActAttr();
        entity.setActId(actId);
        entity.setAttrName("act_redis_archive_group_code");
        entity.setAttrValue(String.valueOf(archiveGroup));
        entity.setAttrDesc("数据冷化分组");

        return geActAttrMapper.saveAttr(entity);
    }

    @Override
    public int insertOrUpdate(long actId, GeActAttr entity) {
        return geActAttrMapper.saveAttr(entity);
    }


    @Override
    public int deleteActAttr(long actId, String attrName) {
        return geActAttrMapper.deleteActAttr(actId,attrName);
    }

    @Override
    public int countHdzjComponent(long actId, long componentId) {
        return geActAttrMapper.countHdzjComponent(actId, componentId);
    }

    @Override
    public int deleteComponentData(String tableName, long actId) {
        return geActAttrMapper.deleteComponentData(tableName, actId);
    }


    @Override
    public long countComponentData(String tableName, long actId) {
        return geActAttrMapper.countComponentData(tableName, actId);
    }

}
