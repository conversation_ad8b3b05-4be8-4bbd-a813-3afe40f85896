package com.yy.manager.ecology.controller;


import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.manager.ecology.entity.GeMethodLog;
import com.yy.manager.ecology.entity.RetryMethodLabelVo;
import com.yy.manager.ecology.service.IGeMethodLogService;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 方法执行日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@RestController
@RequestMapping("/ecology/geMethodLog")
public class GeMethodLogController {
    @Autowired
    private IGeMethodLogService geMethodLogService;

    @ApiOperation("查询方法标签")
    @GetMapping("/queryMethodLabel")
    public Response<List<RetryMethodLabelVo>> queryMethodLabel(String groupCode) {
        List<RetryMethodLabelVo> retryMethodLabelVos = geMethodLogService.queryMethodLabel(groupCode);
        return Response.success(retryMethodLabelVos);
    }

    @ApiOperation("查询执行记录")
    @PreAuthorize("@ss.hasPermi('activity:methodLog:query')")
    @GetMapping("/queryGeMethodLogs")
    public Response<List<GeMethodLog>> queryGeMethodLogs(GeMethodLog geMethodLog) {
        return Response.success(geMethodLogService.queryGeMethodLogs(geMethodLog));
    }

    @ApiOperation("重新执行")
    @Log(title = "重新执行", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('activity:methodLog:retry')")
    @PostMapping("/retryMethod")
    public Response retryMethod(HttpServletRequest request, @RequestBody GeMethodLog geMethodLog) {
        return geMethodLogService.retryMethod(request, geMethodLog);
    }




}
