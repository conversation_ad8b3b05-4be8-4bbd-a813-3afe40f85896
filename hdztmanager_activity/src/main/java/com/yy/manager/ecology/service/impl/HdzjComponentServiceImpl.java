package com.yy.manager.ecology.service.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yy.manager.ecology.entity.HdzjComponent;
import com.yy.manager.ecology.mapper.HdzjComponentMapper;
import com.yy.manager.ecology.service.IHdzjComponentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2021/12/7 19:15
 * @Modified:
 */
@Service
@DS("#{@ad.getGameeCologyDs(#actId)}")
public class HdzjComponentServiceImpl extends ServiceImpl<HdzjComponentMapper, HdzjComponent> implements IHdzjComponentService {

    @Autowired
    private HdzjComponentMapper hdzjComponentMapper;

    @Override
    public HdzjComponent get(long actId, int componentId, int useIndex) {
        QueryWrapper<HdzjComponent> query = new QueryWrapper<>();
        query.eq("act_id", actId);
        query.eq("cmpt_id", componentId);
        query.eq("cmpt_use_inx", useIndex);

        return getOne(query);
    }

    @Override
    public void copyComponent(long actId, long targetActId, long cmptId, long index) {
        hdzjComponentMapper.copyComponent(actId, targetActId, cmptId, index);
    }

    @Override
    public List<HdzjComponent> list(long actId, Wrapper queryWrapper) {
        return list(queryWrapper);
    }

    @Override
    public boolean save(long actId, HdzjComponent entity) {
        return save(entity);
    }

    @Override
    public boolean saveBatch(long actId, List<HdzjComponent> list) {
        return saveBatch(list);
    }

    @Override
    public boolean remove(long actId, Wrapper queryWrapper) {
        return remove(queryWrapper);
    }
}
