package com.yy.manager.ecology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.ecology.entity.HdzjComponentWhitelist;
import com.yy.manager.utils.PageResponse;
import com.yy.manager.utils.Response;
import com.yy.manager.whitelist.vo.CmptWhitelistVo;

import java.util.List;

public interface IHdzjComponentWhitelistService extends IService<HdzjComponentWhitelist> {

    long selectWhitelistCount(long actId, int componentIndex);

    List<HdzjComponentWhitelist> selectAll(long actId, int componentIndex, int size);

    List<String> selectAll(long actId, int componentIndex);

    PageResponse<HdzjComponentWhitelist> selectList(long actId, int componentIndex, String member, int page, int pageSize);

    Response<Integer> addWhitelist(long actId, int componentIndex, String member, String configValue);

    Response<Integer> updateWhitelistValue(long actId, int componentIndex, String member, String configValue);

    Response<Integer> deleteWhitelist(long actId, int componentIndex, String member);

    int batchInsert(long actId, int componentIndex,List<String> members, String configValue);

    int deleteWhitelistByIndex(long actId, int componentIndex);

    Response<Integer> batchAddWhitelist(long actId, int componentIndex, List<CmptWhitelistVo> list);
}
