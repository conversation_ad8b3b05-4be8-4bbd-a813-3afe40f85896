package com.yy.manager.ecology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.ecology.entity.ActResultGroup;
import com.yy.manager.tools.bean.PageInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2022/11/23 11:08
 * @Modified:补充Author
 */
public interface IActResultGroupService extends IService<ActResultGroup> {
    /**
     *
     * @param actId
     * @param me
     * @param extra
     * @param pageInfo
     * @return
     */
    List<ActResultGroup> select(long actId, ActResultGroup me, Map<String, Object> extra, PageInfo pageInfo);

    /**
     *
     * @param actId
     * @return
     */
    List<ActResultGroup> select(long actId);

    /**
     *
     * @param actId
     */
    void remove(long actId);

    /**
     *
     * @param actId
     * @param entityList
     * @return
     */
    boolean saveBatch(long actId, Collection<ActResultGroup> entityList);
}
