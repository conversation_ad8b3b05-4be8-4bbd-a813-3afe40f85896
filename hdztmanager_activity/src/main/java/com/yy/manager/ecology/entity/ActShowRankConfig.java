package com.yy.manager.ecology.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/2/22 15:15
 **/
@TableName("act_show_rank_config")
@Data
public class ActShowRankConfig implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 活动id
     */
    private Long actId;

    /**
     * name
     */
    private String name;

    /**
     * 展示配置
     */
    private String config;

    /**
     * 1===有效
     */
    private Integer status;

    /**
     * 展示开始时间
     */
    private Date startTime;

    /**
     * 展示结束时间
     */
    private Date endTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序，从小到大排序
     */
    private Integer sort;

    /**
     * 扩展参数
     */
    private String ext;
}
