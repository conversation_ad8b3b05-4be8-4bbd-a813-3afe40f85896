package com.yy.manager.awardconfig.bean;

import lombok.Data;

import java.util.List;

/**
 * 奖包视图
 *
 * <AUTHOR>
 * @date 2021/12/13 11:39
 **/
@Data
public class AwardPackageVo {
    private String packageName;
    private String packageType;
    private String packageImage;
    private String tips;
    private String skipUrl;

    private String remark;
    private String packageStatus;
    private Integer probability;
    private Integer position;
    private Long packageTotal;
    private Long userHitLimit;
    private Long dailyHitLimit;
    private String dailyLimitGroup;
    private int packageId;
    private String unit;
    private String viewExtjson;
    private List<AwardPackageItemVo> items;
}
