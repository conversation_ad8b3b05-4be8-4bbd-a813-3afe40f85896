package com.yy.manager.awardconfig.controller;

import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.manager.awardconfig.bean.AwardIssueLogReq;
import com.yy.manager.awardconfig.bean.AwardIssueLogVo;
import com.yy.manager.awardconfig.service.AwardIssueLogService;
import com.yy.manager.tools.service.ExportExcelService;
import com.yy.manager.utils.PageResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021/12/16 11:14
 **/
@Controller
@RequestMapping("awardIssueLog")
public class AwardIssueLogController {
    @Autowired
    private AwardIssueLogService awardIssueLogService;
    @Autowired
    private ExportExcelService exportExcelService;

    @ApiOperation("查询奖品发放记录")
    @PreAuthorize("@ss.hasPermi('activity:awardIssueLog:query')")
    @RequestMapping("pageAwardIssueLog")
    @ResponseBody
    public PageResponse<AwardIssueLogVo> pageAwardIssueLog(@RequestBody AwardIssueLogReq param) {
        if (param == null || param.getActId() <= 0) {
            return PageResponse.empty();
        }

        return awardIssueLogService.pageAwardIssueLog(param);
    }
    @ApiOperation("导出奖品发放记录")
    @Log(title = "导出奖品发放记录", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('activity:awardIssueLog:export')")
    @RequestMapping("exportExcel")
    public String exportExcel(AwardIssueLogReq param, HttpServletResponse response) throws Exception {
        exportExcelService.exportAwardIssue(response, param);
        return null;
    }
}
