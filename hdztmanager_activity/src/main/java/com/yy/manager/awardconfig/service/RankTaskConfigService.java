package com.yy.manager.awardconfig.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.manager.awardconfig.bean.RankTaskConfigDeleteDTO;
import com.yy.manager.awardconfig.bean.RankTaskConfigSaveDTO;
import com.yy.manager.awardconfig.bean.RankTaskConfigVo;
import com.yy.manager.hdzt.entity.AwardPackage;
import com.yy.manager.hdzt.entity.AwardTask;
import com.yy.manager.hdzt.entity.RankingConfig;
import com.yy.manager.hdzt.entity.RankingPhase;
import com.yy.manager.hdzt.entity.RankingTask;
import com.yy.manager.hdzt.entity.RankingTaskAwardConfig;
import com.yy.manager.hdzt.entity.RankingTaskItem;
import com.yy.manager.hdzt.service.IAwardPackageService;
import com.yy.manager.hdzt.service.IAwardTaskService;
import com.yy.manager.hdzt.service.IRankingConfigService;
import com.yy.manager.hdzt.service.IRankingPhaseService;
import com.yy.manager.hdzt.service.IRankingTaskAwardConfigService;
import com.yy.manager.hdzt.service.IRankingTaskItemService;
import com.yy.manager.hdzt.service.IRankingTaskService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/15 10:14
 **/
@Service
public class RankTaskConfigService {
    @Autowired
    private IRankingTaskService rankingTaskService;
    @Autowired
    private IRankingTaskItemService rankingTaskItemService;
    @Autowired
    private IRankingTaskAwardConfigService rankingTaskAwardConfigService;
    @Autowired
    private IRankingConfigService rankingConfigService;
    @Autowired
    private IRankingPhaseService rankingPhaseService;
    @Autowired
    private IAwardTaskService awardTaskService;
    @Autowired
    private IAwardPackageService awardPackageService;

    /**
     * 获取榜单任务配置
     **/
    @DS("#{@ad.getHdztDs(#actId)}")
    public List<RankTaskConfigVo> listRankTaskConfig(long actId) {
        List<RankTaskConfigVo> voList = new ArrayList<>();

        List<RankingTask> rankingTasks = rankingTaskService.listByActId(actId);
        if (CollectionUtils.isEmpty(rankingTasks)) {
            return voList;
        }

        // 任务item和奖池信息
        List<RankingTaskItem> rankingTaskItems = rankingTaskItemService.listByActId(actId);
        Map<String, List<RankingTaskItem>> rankingTaskItemMap = new HashMap<>();
        /*Map<String, RankingTaskItem> rankingTaskItemMap = rankingTaskItems.stream()
                .collect(Collectors.toMap(keyItem -> keyItem.getRankId() + ":" + keyItem.getPhaseId() + ":" + keyItem.getTaskId()+ ":" + keyItem.getItemId(), valueItem -> valueItem));*/
        rankingTaskItems.stream().forEach(keyItem -> {
            String key = keyItem.getRankId() + ":" + keyItem.getPhaseId() + ":" + keyItem.getTaskId();
            List<RankingTaskItem> itemList = rankingTaskItemMap.getOrDefault(key, Lists.newArrayList());
            itemList.add(keyItem);
            rankingTaskItemMap.put(key, itemList);
        });


        List<RankingTaskAwardConfig> rankingTaskAwardConfigs = rankingTaskAwardConfigService.listByActId(actId);
        Map<String, RankingTaskAwardConfig> rankingTaskAwardConfigMap = rankingTaskAwardConfigs.stream()
                .collect(Collectors.toMap(keyItem -> keyItem.getRankId() + ":" + keyItem.getPhaseId() + ":" + keyItem.getTaskId(), valueItem -> valueItem));

        // 榜单和阶段相关信息
        String rankIds = rankingTasks.stream().map(RankingTask::getRankId).map(String::valueOf).distinct().collect(Collectors.joining(StrUtil.COMMA));
        String phaseIds = rankingTasks.stream().map(RankingTask::getPhaseId).map(String::valueOf).distinct().collect(Collectors.joining(StrUtil.COMMA));
        Map<Integer, RankingConfig> rankingConfigMap = rankingConfigService.mapRankConfig(actId, rankIds);
        Map<Integer, RankingPhase> rankingPhaseMap = rankingPhaseService.mapRankingPhase(actId, phaseIds);

        // 奖池和奖包数据
        String poolIds = rankingTaskAwardConfigs.stream().map(RankingTaskAwardConfig::getPoolId).map(String::valueOf).distinct().collect(Collectors.joining(StrUtil.COMMA));
        Map<Integer, AwardTask> awardTaskMap = awardTaskService.mapAwardTask(actId, poolIds);
        List<Integer> packageIds = rankingTaskAwardConfigs.stream().map(RankingTaskAwardConfig::getPackageId).distinct().collect(Collectors.toList());
        Map<Integer, AwardPackage> awardPackageMap = awardPackageService.mapAwardPackage(packageIds);

        rankingTasks.forEach(rankingTask -> {
            RankTaskConfigVo vo = new RankTaskConfigVo();
            vo.setActId(rankingTask.getActId());
            vo.setRankId(rankingTask.getRankId());
            vo.setPhaseId(rankingTask.getPhaseId());
            vo.setTaskName(rankingTask.getTaskName());
            vo.setTaskLevel(rankingTask.getTaskLevel());
            vo.setTaskId(rankingTask.getTaskId());
            vo.setRemark(rankingTask.getRemark());

            // 附加榜单信息
            RankingConfig rankingConfig = rankingConfigMap.get(rankingTask.getRankId());
            if (rankingConfig != null) {
                vo.setRankName(rankingConfig.getRankName());
            }

            // 附加榜单阶段信息
            RankingPhase rankingPhase = rankingPhaseMap.get(rankingTask.getPhaseId());
            if (rankingPhase != null) {
                vo.setPhaseName(rankingPhase.getPhaseName());
                vo.setTaskValueType(rankingPhase.getTaskValueType());
            }

            // 附加任务item信息
            String key = rankingTask.getRankId() + ":" + rankingTask.getPhaseId() + ":" + rankingTask.getTaskId();
            List<RankingTaskItem> list = rankingTaskItemMap.get(key);
            if (!CollectionUtils.isEmpty(list)) {
                vo.setItems(list);
                if (list.size() == 1) {
                    RankingTaskItem rankingTaskItem = list.get(0);
                    vo.setItemId(rankingTaskItem.getItemId());
                    vo.setOldItemId(rankingTaskItem.getItemId());
                    vo.setPassValue(rankingTaskItem.getPassValue());
                    vo.setItemExtJson(rankingTaskItem.getExtjson());
                }

            }

            // 附加奖池信息
            RankingTaskAwardConfig rankingTaskAwardConfig = rankingTaskAwardConfigMap.get(key);
            if (rankingTaskAwardConfig != null) {
                vo.setPoolId(rankingTaskAwardConfig.getPoolId());
                vo.setPackageId(rankingTaskAwardConfig.getPackageId());
                vo.setPackageNum(rankingTaskAwardConfig.getPackageNum());

                vo.setOldPoolId(rankingTaskAwardConfig.getPoolId());
                vo.setOldPackageId(rankingTaskAwardConfig.getPackageId());

                AwardTask awardTask = awardTaskMap.get(rankingTaskAwardConfig.getPoolId());
                if (awardTask != null) {
                    vo.setPoolName(awardTask.getTaskName());
                }

                AwardPackage awardPackage = awardPackageMap.get(rankingTaskAwardConfig.getPackageId());
                if (awardPackage != null) {
                    vo.setPackageName(awardPackage.getPackageName());
                }
            } else {
                vo.setPoolId(0);
                vo.setPackageId(0);
                vo.setPackageNum(0);
                vo.setOldPoolId(0);
                vo.setOldPackageId(0);
            }


            voList.add(vo);
        });

        return voList;
    }

    @DS("#{@ad.getHdztDs(#dto.actId)}")
    public void saveRankTaskConfig(RankTaskConfigSaveDTO dto) {
        QueryWrapper<RankingTask> queryRankingTask = new QueryWrapper<>();
        handleRankTask(dto, queryRankingTask);

        QueryWrapper<RankingTaskItem> queryRankingTaskItem = new QueryWrapper<>();
        handleRankingTaskItem(dto, queryRankingTaskItem);

        QueryWrapper<RankingTaskAwardConfig> queryRankingAwardConfig = new QueryWrapper<>();
        handleRankingTaskAwardConfig(dto, queryRankingAwardConfig);
    }

    @DS("#{@ad.getHdztDs(#dto.actId)}")
    public void saveRankTaskItem(RankingTaskItem dto) {
        QueryWrapper<RankingTaskItem> queryRankingTaskItem = new QueryWrapper<>();
        handleRankingTaskItem(dto, queryRankingTaskItem);
    }

    private void buildQueryWrapper(QueryWrapper queryWrapper, RankTaskConfigSaveDTO dto) {
        queryWrapper.eq("act_id", dto.getActId());
        queryWrapper.eq("rank_id", dto.getRankId());
        queryWrapper.eq("phase_id", dto.getPhaseId());
        queryWrapper.eq("task_id", dto.getTaskId());
    }

    private void buildQueryWrapper(QueryWrapper queryWrapper, RankingTaskItem dto) {
        queryWrapper.eq("act_id", dto.getActId());
        queryWrapper.eq("rank_id", dto.getRankId());
        queryWrapper.eq("phase_id", dto.getPhaseId());
        queryWrapper.eq("task_id", dto.getTaskId());
    }

    private RankingTask handleRankTask(RankTaskConfigSaveDTO dto, QueryWrapper<RankingTask> queryRankingTask) {
        buildQueryWrapper(queryRankingTask, dto);

        RankingTask rankingTask = rankingTaskService.getOne(queryRankingTask);
        boolean add = false;
        if (rankingTask == null) {
            rankingTask = new RankingTask();
            rankingTask.setCtime(new Date());
            add = true;
        }
        rankingTask.setUtime(new Date());
        rankingTask.setTaskId(dto.getTaskId());
        rankingTask.setRankId(dto.getRankId());
        rankingTask.setTaskLevel(dto.getTaskLevel());
        rankingTask.setTaskName(dto.getTaskName());
        rankingTask.setActId(dto.getActId());
        rankingTask.setPhaseId(dto.getPhaseId());
        rankingTask.setRemark(dto.getRemark());
        if (add) {
            this.rankingTaskService.save(rankingTask);
        } else {
            this.rankingTaskService.update(rankingTask, queryRankingTask);
        }

        return rankingTask;
    }

    private RankingTaskItem handleRankingTaskItem(RankingTaskItem dto, QueryWrapper<RankingTaskItem> queryRankingTaskItem) {
        buildQueryWrapper(queryRankingTaskItem, dto);
        queryRankingTaskItem.eq("item_id", dto.getItemId());
        RankingTaskItem rankingTaskItem = rankingTaskItemService.getOne(queryRankingTaskItem);
        boolean add = false;
        if (rankingTaskItem == null) {
            rankingTaskItem = new RankingTaskItem();
            rankingTaskItem.setCtime(new Date());
            rankingTaskItem.setWeight(0);
            add = true;
        }
        rankingTaskItem.setItemId(dto.getItemId());
        rankingTaskItem.setRankId(dto.getRankId());
        rankingTaskItem.setTaskId(dto.getTaskId());
        rankingTaskItem.setActId(dto.getActId());
        rankingTaskItem.setPassValue(dto.getPassValue());
        rankingTaskItem.setPhaseId(dto.getPhaseId());
        rankingTaskItem.setRemark(dto.getRemark());
        rankingTaskItem.setUtime(new Date());

        String itemExtJson = dto.getExtjson();
        if (StringUtils.isNotEmpty(itemExtJson)) {
            try {
                itemExtJson = JSON.parseObject(itemExtJson).toJSONString();
            } catch (Exception ex) {

            }
        }
        rankingTaskItem.setExtjson(itemExtJson);

        if (add) {
            rankingTaskItemService.save(rankingTaskItem);
        } else {
            rankingTaskItemService.update(rankingTaskItem, queryRankingTaskItem);
        }

        return rankingTaskItem;
    }

    private RankingTaskItem handleRankingTaskItem(RankTaskConfigSaveDTO dto, QueryWrapper<RankingTaskItem> queryRankingTaskItem) {
        buildQueryWrapper(queryRankingTaskItem, dto);
        queryRankingTaskItem.eq("item_id", dto.getOldItemId());
        RankingTaskItem rankingTaskItem = rankingTaskItemService.getOne(queryRankingTaskItem);
        boolean add = false;
        if (rankingTaskItem == null) {
            rankingTaskItem = new RankingTaskItem();
            rankingTaskItem.setCtime(new Date());
            rankingTaskItem.setWeight(0);
            add = true;
        }
        rankingTaskItem.setItemId(dto.getItemId());
        rankingTaskItem.setRankId(dto.getRankId());
        rankingTaskItem.setTaskId(dto.getTaskId());
        rankingTaskItem.setActId(dto.getActId());
        rankingTaskItem.setPassValue(dto.getPassValue());
        rankingTaskItem.setPhaseId(dto.getPhaseId());
        rankingTaskItem.setRemark(dto.getRemark());
        rankingTaskItem.setUtime(new Date());

        String itemExtJson = dto.getItemExtJson();
        if (StringUtils.isNotEmpty(itemExtJson)) {
            try {
                itemExtJson = JSON.parseObject(itemExtJson).toJSONString();
            } catch (Exception ex) {

            }
        }
        rankingTaskItem.setExtjson(itemExtJson);

        if (add) {
            rankingTaskItemService.save(rankingTaskItem);
        } else {
            rankingTaskItemService.update(rankingTaskItem, queryRankingTaskItem);
        }

        return rankingTaskItem;
    }

    private RankingTaskAwardConfig handleRankingTaskAwardConfig(RankTaskConfigSaveDTO dto, QueryWrapper<RankingTaskAwardConfig> queryRankingAwardConfig) {
        if (dto.getPoolId() == null || dto.getPackageId() == null || dto.getPoolId() <= 0 || dto.getPackageId() <= 0) {
            return null;
        }
        buildQueryWrapper(queryRankingAwardConfig, dto);
        queryRankingAwardConfig.eq("pool_id", dto.getOldPoolId());
        queryRankingAwardConfig.eq("package_id", dto.getOldPackageId());
        RankingTaskAwardConfig rankingTaskAwardConfig = rankingTaskAwardConfigService.getOne(queryRankingAwardConfig);
        boolean add = false;
        if (rankingTaskAwardConfig == null) {
            rankingTaskAwardConfig = new RankingTaskAwardConfig();
            rankingTaskAwardConfig.setCtime(new Date());
            add = true;
        }
        rankingTaskAwardConfig.setTaskId(dto.getTaskId());
        rankingTaskAwardConfig.setRankId(dto.getRankId());
        rankingTaskAwardConfig.setActId(dto.getActId());
        rankingTaskAwardConfig.setRemark(dto.getRemark());
        rankingTaskAwardConfig.setPhaseId(dto.getPhaseId());
        rankingTaskAwardConfig.setPackageId(dto.getPackageId());
        rankingTaskAwardConfig.setPackageNum(dto.getPackageNum());
        rankingTaskAwardConfig.setPoolId(dto.getPoolId());
        rankingTaskAwardConfig.setUtime(new Date());
        if (add) {
            rankingTaskAwardConfigService.save(rankingTaskAwardConfig);
        } else {
            rankingTaskAwardConfigService.update(rankingTaskAwardConfig, queryRankingAwardConfig);
        }

        return rankingTaskAwardConfig;
    }

    /**
     * 删除榜单任务
     **/
    @DS("#{@ad.getHdztDs(#param.actId)}")
    public void deleteRankTaskConfig(RankTaskConfigDeleteDTO param) {
        rankingTaskService.removeRankingTask(param);
        rankingTaskItemService.remove(param);
        rankingTaskAwardConfigService.remove(param);
    }
}
