package com.yy.manager.awardconfig.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yy.common.annotation.Log;
import com.yy.common.core.service.LogininforService;
import com.yy.common.enums.BusinessType;
import com.yy.common.utils.SecurityUtils;
import com.yy.common.utils.SysEnvHelper;
import com.yy.common.utils.poi.ExcelUtil;
import com.yy.common.utils.spring.SpringUtils;
import com.yy.manager.awardconfig.bean.*;
import com.yy.manager.awardconfig.service.AwardTaskConfigService;
import com.yy.manager.clear.bean.DropDownVo;
import com.yy.manager.exception.SuperException;
import com.yy.manager.hdzt.service.IAwardModelService;
import com.yy.manager.hdzt.service.IHdztBusinessService;
import com.yy.manager.hdztmanager.service.ISysParameterService;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/10 17:02
 **/
@RestController
@RequestMapping("/awardConfig")
public class AwardConfigController {
    private final static Logger logger = LoggerFactory.getLogger(AwardConfigController.class);
    @Autowired
    private AwardTaskConfigService awardTaskConfigService;
    @Autowired
    private IHdztBusinessService hdztBusinessService;
    @Autowired
    private ISysParameterService sysParameterService;
    @Autowired
    private IAwardModelService awardModelService;
    private static final String UPDATE_AWARD_ADMIN = "update_award_admin";

    @ApiOperation("导出excel")
    @PreAuthorize("@ss.hasPermi('activity:awardConfig:query')")
    @RequestMapping("awardTask2Excel")
    public void awardTask2Excel(@RequestParam(name = "actId", defaultValue = "0") long actId, HttpServletResponse response) {
        List<AwardToExcel> awardToExcels = awardModelService.awardToExcels(actId);
        ExcelUtil.exportExcel(awardToExcels, "excel", AwardToExcel.class, response);
    }

    /**
     * 根据活动id获取奖池列表
     **/
    @ApiOperation("查询活动奖池列表")
    @PreAuthorize("@ss.hasPermi('activity:awardConfig:query')")
    @RequestMapping("listAwardTask")
    public Response<List<AwardTaskVo>> listAwardTask(@RequestParam(name = "actId", defaultValue = "0") long actId) {
        // 没有传活动id，直接返回空数据
        // 活动id是用于定位数据源
        Response<List<AwardTaskVo>> response = Response.ok();
        if (actId <= 0) {
            return response;
        }
        response.setData(awardTaskConfigService.listAwardTask(actId));

        return response;
    }

    /**
     * 保存奖池信息
     **/
    @ApiOperation("保存奖池信息")
    @Log(title = "保存奖池信息", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('activity:awardConfig:edit')")
    @PostMapping("saveAwardConfig")
    public Response<String> saveAwardConfig(HttpServletRequest request, HttpServletResponse response
            , @RequestBody AwardTaskSaveDTO dto) {
        try {
            if (dto == null) {
                return Response.fail(999, "参数异常");
            }
            long loginUid = SecurityUtils.getLoginUid();
            String updateAardAdminUids = sysParameterService.queryParameterValue(UPDATE_AWARD_ADMIN);
            if(!updateAardAdminUids.contains(String.valueOf(loginUid))) {
                throw new SuperException("该功能仅限开发人员使用,请联系开发", 1);
            }

            logger.info("saveAwardConfig loginUser={},dto={}", SecurityUtils.getUsername(), dto);

            Assert.hasLength(dto.getTaskName(), "请输入奖池名称");
            Assert.isTrue(dto.getModel() == 100 || dto.getModel() == 101 || dto.getModel() == 200 || dto.getModel() == 201, "奖池类型配置错误");
            Assert.isTrue(dto.getEndTime() != null && dto.getOpenTime() != null && dto.getEndTime().after(dto.getOpenTime()), "奖池时间配置错误");
            Assert.isTrue(dto.getStatus() == 0 || dto.getStatus() == 1, "奖池状态配置错误");
            Assert.isTrue(dto.getActId() > 0, "活动id配置错误");
            awardTaskConfigService.saveAwardTask(dto);

            return Response.success("保存成功");
        } catch (Exception ex) {
            logger.error("saveAwardConfig error,dto={}", JSON.toJSONString(dto), ex);
            return Response.fail(500, ex.getMessage());
        }
    }

    /**
     * 复制奖池
     **/
    @ApiOperation("复制奖池信息")
    @Log(title = "复制奖池信息", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('activity:awardConfig:copy')")
    @PostMapping("copyAwardConfig")
    public Response<String> copyAwardConfig(HttpServletRequest request, HttpServletResponse response
            , @RequestBody AwardTaskCopyDTO dto) {
        try {
            if (dto == null) {
                return Response.fail(999, "参数异常");
            }

            logger.info("copyAwardConfig loginUser={},dto={}", SecurityUtils.getUsername(), dto);

            awardTaskConfigService.copyAwardTask(dto);

            return Response.success("保存成功");
        } catch (Exception ex) {
            logger.error("copyAwardConfig error,dto={}", JSON.toJSONString(dto), ex);
            return Response.fail(500, ex.getMessage());
        }
    }

    @ApiOperation("删除奖池信息")
    @Log(title = "删除奖池信息", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('activity:awardConfig:delete')")
    @PostMapping("deleteAwardTask")
    public Response<String> deleteAwardTask(HttpServletRequest request, HttpServletResponse response
            , @RequestBody DeleteAwardTaskDTO param) {
        try {
            if (SysEnvHelper.isDeploy()) {
                return Response.success("线上环境不允许删除");
            }

            if (param == null) {
                return Response.fail(999, "参数异常");
            }
            logger.info("deleteAwardTask loginUser={},dto={}", SecurityUtils.getUsername(), param);

            awardTaskConfigService.deleteAwardTask(param);

            return Response.success("删除成功");
        } catch (Exception ex) {
            logger.error("deleteAwardTask error,dto={}", JSON.toJSONString(param), ex);

            return Response.fail(500, ex.getMessage());
        }
    }

    /**
     * 获取对应奖池的奖包
     **/
    @ApiOperation("查询奖池的奖包信息")
    @RequestMapping("listAwardPackage")
    public Response<List<AwardPackageVo>> listAwardPackage(@RequestParam(name = "taskId", defaultValue = "0") int taskId
            , @RequestParam(name = "actId", defaultValue = "0") long actId) {
        if (taskId <= 0 || actId <= 0) {
            return Response.ok();
        }

        return Response.success(awardTaskConfigService.listAwardPackage(actId, taskId));
    }

    @ApiOperation("保存奖包信息")
    @Log(title = "保存奖包信息", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('activity:awardPackage:edit')")
    @PostMapping("saveAwardPackage")
    public Response<String> saveAwardPackage(HttpServletRequest request, HttpServletResponse response
            , @RequestBody AwardPackageSaveDTO dto) {
        try {
            if (dto == null) {
                return Response.fail(999, "参数异常");
            }
            long loginUid = SecurityUtils.getLoginUid();
            String updateAardAdminUids = sysParameterService.queryParameterValue(UPDATE_AWARD_ADMIN);
            if(!updateAardAdminUids.contains(String.valueOf(loginUid))) {
                throw new SuperException("该功能仅限开发人员使用,请联系开发", 1);
            }
            logger.info("saveAwardPackage loginUser={},dto={}", SecurityUtils.getUsername(), dto);
            Assert.isTrue(dto.getActId() != null && dto.getActId() > 0 && dto.getTaskId() > 0, "参数异常");
            Assert.hasLength(dto.getPackageName(), "请输入奖包名称");
            Assert.isTrue(dto.getPackageType() == 0 || dto.getPackageType() == 1, "奖包类型有误");
            Assert.isTrue(dto.getPackageStatus() == 0 || dto.getPackageStatus() == 1, "奖包状态有误");

            long opUid = SecurityUtils.getLoginUid();
            dto.setOpUid(opUid);
            awardTaskConfigService.saveAwardPackage(dto);

            return Response.success("保存成功");
        } catch (Exception ex) {
            logger.error("saveAwardPackage error,dto={}", JSON.toJSONString(dto), ex);

            return Response.fail(500, ex.getMessage());
        }
    }

    /**
     * TODO 暂时不校验参数
     **/
    @ApiOperation("保存奖包奖项信息")
    @Log(title = "保存奖包奖项信息", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('activity:awardPackageItem:edit')")
    @PostMapping("saveAwardPackageItem")
    public Response<String> saveAwardPackageItem(HttpServletRequest request, HttpServletResponse response
            , @RequestBody AwardPackageItemSaveDTO dto) {
        try {
            if (dto == null) {
                return Response.fail(999, "参数异常");
            }
            long loginUid = SecurityUtils.getLoginUid();
            String updateAardAdminUids = sysParameterService.queryParameterValue(UPDATE_AWARD_ADMIN);
            if(!updateAardAdminUids.contains(String.valueOf(loginUid))) {
                throw new SuperException("该功能仅限开发人员使用,请联系开发", 1);
            }
            logger.info("saveAwardPackageItem loginUser={},dto={}", SecurityUtils.getUsername(), dto);


            awardTaskConfigService.saveAwardPackageItem(dto);

            return Response.success("保存成功");
        } catch (Exception ex) {
            logger.error("saveAwardPackageItem error,dto={}", JSON.toJSONString(dto), ex);

            return Response.fail(500, ex.getMessage());
        }
    }

    /**
     * 获取下拉框选项
     * dropDownType = 0 业务数据下拉
     * dropDownType = 1 礼物类型下拉
     **/
    @ApiOperation("查询业务/礼物ID-名称下拉框")
    @GetMapping("listDropDown")
    public Response<List<DropDownVo>> listDropDown(@RequestParam(name = "dropDownType", defaultValue = "0") int dropDownType) {
        switch (dropDownType) {
            case 0:
                return Response.success(SpringUtils.getAopProxy(this).buildBusinessDropDown());
            case 1:
                return Response.success(buildGiftTypeDropDown());
            default:
                return Response.success(new ArrayList<>());
        }
    }

    /**
     * 获取业务下拉
     **/
    @DS("hdzt")
    public List<DropDownVo> buildBusinessDropDown() {
        List<DropDownVo> voList = new ArrayList<>();
        hdztBusinessService.listValid().forEach(hdztBusiness -> {
            voList.add(new DropDownVo(hdztBusiness.getBusiId() + "", hdztBusiness.getName()));
        });

        return voList;
    }

    private List<DropDownVo> buildGiftTypeDropDown() {
        List<DropDownVo> voList = new ArrayList<>();
        voList.add(new DropDownVo("132", "交友统一发奖"));
        voList.add(new DropDownVo("133", "虚拟资产系统"));
        voList.add(new DropDownVo("131", "约战礼物"));
        voList.add(new DropDownVo("130", "陪玩礼物"));
        voList.add(new DropDownVo("129", "追玩礼物"));
        voList.add(new DropDownVo("128", "YY基础平台积分"));
        voList.add(new DropDownVo("127", "YY基础平台铭牌"));
        voList.add(new DropDownVo("126", "YY基础平台尾灯"));
        voList.add(new DropDownVo("125", "YY基础平台勋章"));
        voList.add(new DropDownVo("124", "约战时效宝石"));
        voList.add(new DropDownVo("123", "约战时效礼物"));
        voList.add(new DropDownVo("122", "游戏宝贝奖品"));
        voList.add(new DropDownVo("121", "YY交友奖品"));
        voList.add(new DropDownVo("74", "约战宝石"));
        voList.add(new DropDownVo("64", "约战特权"));
        voList.add(new DropDownVo("63", "约战礼物"));
        voList.add(new DropDownVo("1", "实物"));

        return voList;
    }

    /**
     * 删除奖包
     **/
    @GetMapping("removePackage")
    public Response<String> removePackage(@RequestParam(name = "actId", defaultValue = "0") long actId,
                                          @RequestParam(name = "taskId", defaultValue = "0") long taskId,
                                          @RequestParam(name = "packageId", defaultValue = "0") long packageId) {
        if (SysEnvHelper.isDeploy()) {
            return Response.success("线上环境不允许删除");
        }
        if (actId <= 0 || taskId <= 0 || packageId <= 0) {
            return Response.success("参数异常");
        }
        long loginUid = SecurityUtils.getLoginUid();
        String updateAardAdminUids = sysParameterService.queryParameterValue(UPDATE_AWARD_ADMIN);
        if(!updateAardAdminUids.contains(String.valueOf(loginUid))) {
            throw new SuperException("该功能仅限开发人员使用,请联系开发", 1);
        }
        awardTaskConfigService.deletePackage(actId, taskId, packageId);

        return Response.ok();
    }



}
