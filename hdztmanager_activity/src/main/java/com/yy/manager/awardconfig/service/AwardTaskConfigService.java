package com.yy.manager.awardconfig.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.common.utils.spring.SpringUtils;
import com.yy.manager.awardconfig.bean.AwardPackageItemSaveDTO;
import com.yy.manager.awardconfig.bean.AwardPackageItemVo;
import com.yy.manager.awardconfig.bean.AwardPackageSaveDTO;
import com.yy.manager.awardconfig.bean.AwardPackageVo;
import com.yy.manager.awardconfig.bean.AwardTaskCopyDTO;
import com.yy.manager.awardconfig.bean.AwardTaskSaveDTO;
import com.yy.manager.awardconfig.bean.AwardTaskVo;
import com.yy.manager.awardconfig.bean.DeleteAwardTaskDTO;
import com.yy.manager.clear.bean.DropDownVo;
import com.yy.manager.commonservice.CommonService;
import com.yy.manager.exception.SuperException;
import com.yy.manager.factory.RedisGroupFactory;
import com.yy.manager.hdzt.entity.AwardModel;
import com.yy.manager.hdzt.entity.AwardPackage;
import com.yy.manager.hdzt.entity.AwardPackageItem;
import com.yy.manager.hdzt.entity.AwardTask;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.entity.RankingItem;
import com.yy.manager.hdzt.service.IAwardPackageItemService;
import com.yy.manager.hdzt.service.IAwardPackageService;
import com.yy.manager.hdzt.service.IAwardTaskService;
import com.yy.manager.hdzt.service.IHdztActivityService;
import com.yy.manager.hdzt.service.IRankingItemService;
import com.yy.manager.hdzt.service.impl.AwardModelServiceImpl;
import com.yy.manager.hdztmanager.service.ISysParameterService;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.DateUtil;
import com.yy.manager.utils.EmailUtil;
import com.yy.manager.utils.SysEnvHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/10 17:17
 **/
@Service
public class AwardTaskConfigService {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private IAwardTaskService awardTaskService;
    @Autowired
    private AwardModelServiceImpl awardModelService;
    @Autowired
    private IAwardPackageService awardPackageService;
    @Autowired
    private IAwardPackageItemService awardPackageItemService;
    @Autowired
    private IRankingItemService rankingItemService;
    @Autowired
    private IHdztActivityService hdztActivityService;

    @Autowired
    private RedisGroupFactory redisGroupFactory;

    @Autowired
    private ISysParameterService sysParameterService;

    private final static Integer GIFT_TYPE = 132;

    private final static Integer CURRENCY_TYPE = 133;


    private final static String EXPAND = "expand";


    private final static String VALID_START_TIME = "validStartTime";


    private final static String VALID_END_TIME = "validEndTime";

    public static final String PROBABILITY_CHANGE_RECORD = "probability_change_record";

    @Autowired
    private CommonService commonService;

    /**
     * 根据活动获取奖池配置
     **/
    @DS("#{@ad.getHdztDs(#actId)}")
    public List<AwardTaskVo> listAwardTask(long actId) {
        List<AwardTaskVo> voList = new ArrayList<>();
        List<AwardTask> awardTasks = awardTaskService.listAwardTask(actId);
        awardTasks.forEach(awardTask -> voList.add(new AwardTaskVo(awardTask)));

        return voList;
    }

    /**
     * 保持奖池配置
     **/
    @DS("#{@ad.getHdztDs(#dto.actId)}")
    public void saveAwardTask(AwardTaskSaveDTO dto) {
        if (dto.getAdd()) {
            dto.setTaskId((int) awardTaskService.maxTaskId() + 1);
        }
        AwardTask exist = awardTaskService.getById(dto.getTaskId());
        if (dto.getAdd() && exist != null) {
            throw new SuperException("奖池id已存在,请重新配置");
        }
        if (exist == null) {
            exist = new AwardTask();
            exist.setCtime(new Date());
        }
        exist.setUtime(new Date());
        exist.setActId(dto.getActId());
        exist.setEndtime(dto.getEndTime());
        exist.setModel(dto.getModel());
        exist.setOpentime(dto.getOpenTime());
        exist.setRemark(dto.getRemark());
        exist.setStatus(dto.getStatus());
        exist.setTaskId(dto.getTaskId());
        exist.setTaskName(dto.getTaskName());

        awardTaskService.saveOrUpdate(exist);
    }

    /**
     * 复制奖池配置
     **/
    @DS("#{@ad.getHdztDs(#dto.fromActId)}")
    public void copyAwardTask(AwardTaskCopyDTO dto) {
        AwardTask sourceAwardTask = awardTaskService.getById(dto.getFromTaskId());
        if (sourceAwardTask == null || !Objects.equals(dto.getFromActId(), sourceAwardTask.getActId())) {
            throw new SuperException("不存在目标奖池");
        }

        List<AwardModel> sourceAwardModels = awardModelService.list(dto.getFromTaskId());
        List<Integer> packageIds = sourceAwardModels.stream().map(AwardModel::getPackageId).collect(Collectors.toList());
        List<AwardPackage> sourceAwardPackages = new ArrayList<>();
        List<AwardPackageItem> sourceItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageIds)) {
            sourceAwardPackages = awardPackageService.listPackage(packageIds);
            sourceItems = awardPackageItemService.listPackageItem(packageIds);
        }
        sourceAwardModels = sourceAwardModels.stream()
                .sorted(Comparator.comparingInt(AwardModel::getPackageId))
                .collect(Collectors.toList());

        AwardTaskConfigService aopProxy = SpringUtils.getAopProxy(this);
        aopProxy.doCopy(dto, sourceAwardTask, sourceAwardModels, sourceAwardPackages, sourceItems);
    }

    @DS("#{@ad.getHdztDs(#dto.toActId)}")
    public void doCopy(AwardTaskCopyDTO dto, AwardTask sourceAwardTask, List<AwardModel> sourceAwardModels
            , List<AwardPackage> sourceAwardPackages, List<AwardPackageItem> sourceItems) {
        HdztActivity hdztActivity = hdztActivityService.getById(dto.getToActId());
        if (hdztActivity == null) {
            throw new SuperException("活动信息不存在：" + dto.getToActId());
        }
        AwardTask toAwardTask = new AwardTask();
        BeanUtils.copyProperties(sourceAwardTask, toAwardTask);
        toAwardTask.setTaskId((int) awardTaskService.maxTaskId() + 1);
        toAwardTask.setCtime(new Date());
        toAwardTask.setUtime(new Date());
        toAwardTask.setActId(dto.getToActId());
        toAwardTask.setTaskName(dto.getToTaskName());
        toAwardTask.setOpentime(hdztActivity.getBeginTime());
        toAwardTask.setEndtime(DateUtil.addHours(hdztActivity.getEndTime(), 3));
        awardTaskService.save(toAwardTask);

        List<AwardModel> toAwardModels = new ArrayList<>();
        List<AwardPackage> toAwardPackages = sourceAwardPackages;
        List<AwardPackageItem> toItems = sourceItems;
        Integer packageId = 0;
        for (AwardModel sourceAwardModel : sourceAwardModels) {
            AwardModel awardModel = new AwardModel();
            if (packageId > 0) {
                packageId++;
            } else {
                packageId = (int) awardModelService.nextPackageId();
            }
            BeanUtils.copyProperties(sourceAwardModel, awardModel);
            awardModel.setUtime(new Date());
            awardModel.setCtime(new Date());
            awardModel.setConsumed(0L);
            awardModel.setTaskId(toAwardTask.getTaskId());
            awardModel.setPackageId(packageId);
            toAwardModels.add(awardModel);

            Optional<AwardPackage> awardPackageOptional = toAwardPackages.stream()
                    .filter(awardPackage -> awardPackage.getPackageId().equals(sourceAwardModel.getPackageId()))
                    .findFirst();
            if (awardPackageOptional.isPresent()) {
                awardPackageOptional.get().setPackageId(packageId);
                awardPackageOptional.get().setCtime(new Date());
                awardPackageOptional.get().setUtime(new Date());
            }

            List<AwardPackageItem> subItems = toItems.stream()
                    .filter(item -> item.getPackageId().equals(sourceAwardModel.getPackageId()))
                    .collect(Collectors.toList());
            for (AwardPackageItem subItem : subItems) {
                subItem.setPackageId(packageId);
                subItem.setCtime(new Date());
                subItem.setUtime(new Date());
            }
        }

        if (CollectionUtils.isNotEmpty(toAwardModels)) {
            this.awardModelService.saveBatch(toAwardModels);
        }

        if (CollectionUtils.isNotEmpty(toAwardPackages)) {
            this.awardPackageService.saveBatch(toAwardPackages);
        }

        if (CollectionUtils.isNotEmpty(toItems)) {
            this.awardPackageItemService.saveBatch(toItems);
        }
    }

    /**
     * 根据奖池id获取奖包
     **/
    @DS("#{@ad.getHdztDs(#actId)}")
    public List<AwardPackageVo> listAwardPackage(long actId, int taskId) {
        List<AwardPackageVo> voList = new ArrayList<>();
        if (taskId <= 0) {
            return voList;
        }
        AwardTask awardTask = awardTaskService.getById(taskId);
        if (awardTask == null || !awardTask.getActId().equals(actId)) {
            return voList;
        }
        List<AwardModel> awardModels = awardModelService.list(taskId);
        if (CollectionUtils.isEmpty(awardModels)) {
            return voList;
        }
        List<Integer> packageIds = awardModels.stream().map(AwardModel::getPackageId).collect(Collectors.toList());
        List<AwardPackage> awardPackages = awardPackageService.listPackage(packageIds);
        if (CollectionUtils.isEmpty(awardPackages)) {
            return voList;
        }
        List<AwardPackageItem> packageItems = awardPackageItemService.listPackageItem(packageIds);

        // packageId -> AwardPackage
        Map<Integer, AwardPackage> awardPackageMap = awardPackages.stream()
                .collect(Collectors.toMap(AwardPackage::getPackageId, awardPackage -> awardPackage));

        // packageId -> List<AwardPackageItem>
        Map<Integer, List<AwardPackageItem>> awardPackageItemMap = packageItems.stream().collect(Collectors.toMap(
                AwardPackageItem::getPackageId
                , Lists::newArrayList
                , (newList, oldList) -> {
                    oldList.addAll(newList);
                    return oldList;
                }));

        awardModels.forEach(awardModel -> {
            AwardPackageVo vo = toAwardPackageVo(awardModel, awardPackageMap, awardPackageItemMap);
            voList.add(vo);
        });

        return voList;
    }

    private AwardPackageVo toAwardPackageVo(AwardModel awardModel, Map<Integer, AwardPackage> awardPackageMap
            , Map<Integer, List<AwardPackageItem>> awardPackageItemMap) {
        AwardPackageVo vo = new AwardPackageVo();
        vo.setPackageId(awardModel.getPackageId());
        vo.setPackageTotal(awardModel.getPackageTotal());
        vo.setDailyHitLimit(awardModel.getDailyHitLimit());
        vo.setDailyLimitGroup(awardModel.getDailyLimitGroup());
        vo.setUserHitLimit(awardModel.getUserHitLimit());
        vo.setPosition(awardModel.getPosition());
        vo.setProbability(awardModel.getProbability());
        vo.setPackageStatus(awardModel.getStatus() + "");
        vo.setRemark(awardModel.getRemark());

        AwardPackage awardPackage = awardPackageMap.get(awardModel.getPackageId());
        if (awardPackage != null) {
            vo.setPackageImage(awardPackage.getPackageImage());
            vo.setPackageName(awardPackage.getPackageName());
            vo.setPackageType(awardPackage.getPackageType() + "");
            vo.setTips(awardPackage.getMouseoverTips());
            vo.setSkipUrl(awardPackage.getSkipUrl());
            vo.setUnit(awardPackage.getUnit());
            vo.setViewExtjson(awardPackage.getViewExtjson());
        }

        List<AwardPackageItem> packageItemList = awardPackageItemMap.get(awardModel.getPackageId());
        vo.setItems(toAwardPackageItemVo(packageItemList));

        return vo;
    }

    private List<AwardPackageItemVo> toAwardPackageItemVo(List<AwardPackageItem> packageItemList) {
        List<AwardPackageItemVo> items = new ArrayList<>();
        if (packageItemList == null) {
            return items;
        }
        packageItemList.forEach(packageItem -> {
            AwardPackageItemVo vo = new AwardPackageItemVo();
            vo.setGiftCode(packageItem.getGiftCode());
            vo.setBusinessId(packageItem.getBusiId());
            vo.setItemId(packageItem.getItemId());
            vo.setIssueType(packageItem.getIssueType() + "");
            vo.setStatus(packageItem.getStatus() + "");
            vo.setGiftType(packageItem.getGiftType());
            vo.setGiftName(packageItem.getGiftName());
            vo.setGiftNum(packageItem.getGiftNum());
            vo.setPosition(packageItem.getPosition());
            vo.setUnit(packageItem.getUnit());
            vo.setViewExtjson(packageItem.getViewExtjson());

            if (GIFT_TYPE.equals(packageItem.getGiftType())) {
                try {
                    JSONObject issueConfig = JSON.parseObject(packageItem.getIssueConfig());
                    if (issueConfig.containsKey(EXPAND)) {
                        JSONObject expand = issueConfig.getJSONObject(EXPAND);
                        issueConfig.put(EXPAND, expand.toJSONString());
                    }
                    if (issueConfig.containsKey(VALID_START_TIME)) {
                        issueConfig.put(VALID_START_TIME, issueConfig.getLongValue(VALID_START_TIME) * 1000);
                    }
                    if (issueConfig.containsKey(VALID_END_TIME)) {
                        issueConfig.put(VALID_END_TIME, issueConfig.getLongValue(VALID_END_TIME) * 1000);
                    }
                    vo.setIssueConfig(issueConfig);
                    vo.setIssueConfigJson("");
                } catch (Exception ex) {
                    logger.error("parse issue config error,{}", packageItem.getIssueConfig(), ex);
                }
            } else if(CURRENCY_TYPE.equals(packageItem.getGiftType())) {
                try {
                    JSONObject issueConfig = JSON.parseObject(packageItem.getIssueConfig());
                    if (issueConfig.containsKey(EXPAND)) {
                        JSONObject expand = issueConfig.getJSONObject(EXPAND);
                        issueConfig.put(EXPAND, expand.toJSONString());
                    }
                    vo.setIssueConfig(issueConfig);
                    vo.setIssueConfigJson("");
                } catch (Exception ex) {
                    logger.error("parse issue config error,{}", packageItem.getIssueConfig(), ex);
                }
            } else {
                vo.setIssueConfigJson(packageItem.getIssueConfig());
            }

            items.add(vo);
        });

        return items;
    }

    /**
     * 保存奖包
     **/
    @DS("#{@ad.getHdztDs(#dto.actId)}")
    public void saveAwardPackage(AwardPackageSaveDTO dto) {
        long actId = dto.getActId();
        AwardModel query = new AwardModel();
        query.setTaskId(dto.getTaskId());
        query.setPackageId(dto.getPackageId());
        AwardModel awardModel = awardModelService.getOne(new QueryWrapper<>(query));
        AwardModel before = awardModel;

        int packageId = dto.getPackageId();

        AwardPackage awardPackage = new AwardPackage();

        boolean add = false;
        if (awardModel == null) {
            awardModel = new AwardModel();
            // add new
            awardModel.setCtime(new Date());
            packageId = (int) awardModelService.nextPackageId();
            awardModel.setPackageId(packageId);
            awardModel.setTaskId(dto.getTaskId());
            awardPackage.setCtime(new Date());
            query.setPackageId(packageId);
            add = true;
        }

        awardModel.setPackageTotal(dto.getPackageTotal());
        awardModel.setDailyHitLimit(dto.getDailyHitLimit());
        awardModel.setDailyLimitGroup(dto.getDailyLimitGroup());
        awardModel.setPosition(dto.getPosition());
        awardModel.setProbability(dto.getProbability());
        awardModel.setUserHitLimit(dto.getUserHitLimit());
        awardModel.setUtime(new Date());
        awardModel.setStatus(dto.getPackageStatus());
        awardModel.setRemark(dto.getRemark());

        awardPackage.setPackageId(packageId);
        awardPackage.setPackageImage(dto.getPackageImage());
        awardPackage.setPackageType(dto.getPackageType());
        awardPackage.setPackageName(dto.getPackageName());
        awardPackage.setRemark(dto.getRemark());
        awardPackage.setMouseoverTips(dto.getTips());
        awardPackage.setSkipUrl(dto.getSkipUrl());
        awardPackage.setStatus(dto.getPackageStatus());
        awardPackage.setUtime(new Date());
        awardPackage.setUnit(dto.getUnit());
        awardPackage.setViewExtjson(dto.getViewExtjson());
        if (add) {
            awardModelService.save(awardModel);
        } else {
            awardModelService.update(awardModel, new QueryWrapper<>(query));
            try {
                if (SysEnvHelper.isDeploy() && !commonService.isGrey(actId)) {
                    RedisTemplate redisTemplate = redisGroupFactory.getHdztRedisByGroup(getHdztRedisGroupByActId(actId));
                    redisTemplate.opsForList().leftPush(PROBABILITY_CHANGE_RECORD, JSON.toJSONString(dto));
                }
            } catch (Exception e) {
                logger.error("probability change record error", e);
            }
        }
        awardPackageService.saveOrUpdate(awardPackage);
    }

    /**
     * 后面要补全逻辑，从数据库获取活动的redis分组
     *
     * @param actId
     * @return
     */
    private long getHdztRedisGroupByActId(long actId) {
        HdztActivity hdztActivity = hdztActivityService.selectByActId(actId);
        return hdztActivity.getActGroup();
    }

    /**
     * 保存奖项
     **/
    @DS("#{@ad.getHdztDs(#dto.actId)}")
    public void saveAwardPackageItem(AwardPackageItemSaveDTO dto) {

        AwardPackageItem query = new AwardPackageItem();
        query.setItemId(dto.getItemId());
        query.setPackageId(dto.getPackageId());

        AwardPackageItem exist = awardPackageItemService.getOne(new QueryWrapper<>(query));

        // add new one
        boolean add = false;
        if (exist == null) {
            exist = new AwardPackageItem();
            exist.setPackageId(dto.getPackageId());
            exist.setItemId(dto.getItemId());
            exist.setCtime(new Date());
            add = true;
            exist.setGiftImage("");
        }

        exist.setUtime(new Date());
        exist.setGiftCode(dto.getGiftCode());
        exist.setBusiId(dto.getBusinessId());
        exist.setGiftName(dto.getGiftName());
        exist.setGiftNum(dto.getGiftNum());
        exist.setGiftType(dto.getGiftType());
        exist.setIssueType(dto.getIssueType());
        exist.setPosition(dto.getPosition());
        exist.setStatus(dto.getStatus());
        exist.setRemark(dto.getRemark());

        if (GIFT_TYPE.equals(dto.getGiftType())) {
            if (dto.getIssueConfig().containsKey(VALID_START_TIME)) {
                dto.getIssueConfig().put(VALID_START_TIME, dto.getIssueConfig().getLongValue(VALID_START_TIME) / 1000);
            }

            if (dto.getIssueConfig().containsKey(VALID_END_TIME)) {
                dto.getIssueConfig().put(VALID_END_TIME, dto.getIssueConfig().getLongValue(VALID_END_TIME) / 1000);
            }
            if (dto.getIssueConfig().containsKey(EXPAND)) {
                JSONObject expandObject = dto.getIssueConfig().getJSONObject(EXPAND);
                dto.getIssueConfig().put(EXPAND, expandObject);
            }

            exist.setIssueConfig(JSON.toJSONString(dto.getIssueConfig()));
        } else if(CURRENCY_TYPE.equals(dto.getGiftType())) {
            if (dto.getIssueConfig().containsKey(EXPAND)) {
                JSONObject expandObject = dto.getIssueConfig().getJSONObject(EXPAND);
                dto.getIssueConfig().put(EXPAND, expandObject);
            }
            exist.setIssueConfig(JSON.toJSONString(dto.getIssueConfig()));
        } else {
            exist.setIssueConfig(dto.getIssueConfigJson());
        }

        if (add) {
            awardPackageItemService.save(exist);
        } else {
            awardPackageItemService.update(exist, new QueryWrapper<AwardPackageItem>(query));
        }
    }

    /**
     * 获取发奖奖池
     **/
    @DS("#{@ad.getHdztDs(#actId)}")
    public List<DropDownVo> listAwardTask(long actId, List<Integer> models) {
        QueryWrapper<AwardTask> query = new QueryWrapper<>();
        query.eq("act_id", actId);
        query.eq("status", 1);
        if (CollectionUtils.isNotEmpty(models)) {
            query.in("model", models);
        }
        List<AwardTask> awardTasks = awardTaskService.list(query);

        List<DropDownVo> voList = new ArrayList<>();
        awardTasks.forEach(awardTask -> voList.add(
                new DropDownVo(awardTask.getTaskId() + "", awardTask.getTaskName()))
        );

        return voList;
    }

    @DS("#{@ad.getHdztDs(#actId)}")
    public List<DropDownVo> listPackage(long actId, int taskId) {
        List<DropDownVo> voList = new ArrayList<>();
        List<AwardModel> awardModels = awardModelService.list(taskId);
        if (CollectionUtils.isEmpty(awardModels)) {
            return voList;
        }
        List<Integer> packageIds = awardModels.stream()
                .filter(awardModel -> awardModel.getStatus() == 1)
                .map(AwardModel::getPackageId)
                .collect(Collectors.toList());
        List<AwardPackage> awardPackages = awardPackageService.listPackage(packageIds);
        awardPackages.forEach(awardPackage -> voList.add(
                new DropDownVo(awardPackage.getPackageId() + "", awardPackage.getPackageName()))
        );

        return voList;
    }

    /**
     * 获取榜单的累榜item
     **/
    @DS("#{@ad.getHdztDs(#actId)}")
    public String getRankItem(long actId, int rankId) {
        QueryWrapper<RankingItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("rank_id", rankId);

        RankingItem rankingItem = this.rankingItemService.getOne(queryWrapper);
        if (rankingItem != null) {
            if (StringUtils.isNotBlank(rankingItem.getItemAlias())) {
                return rankingItem.getItemAlias();
            }
            return rankingItem.getItemIds();
        }

        return "";
    }

    /**
     * 删除任务
     **/
    @DS("#{@ad.getHdztDs(#param.actId)}")
    public void deleteAwardTask(DeleteAwardTaskDTO param) {

    }

    /**
     * 删除奖包
     **/
    @DS("#{@ad.getHdztDs(#actId)}")
    public void deletePackage(long actId, long taskId, long packageId) {
        awardModelService.remove(taskId, packageId);
        awardPackageService.removeById(packageId);
        awardPackageItemService.remove(packageId);
    }

    @Scheduled(cron = "0 */10 * * * ?")
    public void probabilityChangeEmailTimer() {
        try {
            for (int group : Const.HDZT_REDIS_GROUP_LIST) {
                RedisTemplate redisTemplate = redisGroupFactory.getHdztRedisByGroup(group);
                List<String> ranges = redisTemplate.opsForList().range(PROBABILITY_CHANGE_RECORD, 0, -1);
                if (ranges == null || ranges.isEmpty()) {
                    continue;
                }
                logger.info("probabilityChangeEmailTimer send email start, size:{}", ranges.size());
                redisTemplate.delete(PROBABILITY_CHANGE_RECORD);
                Collections.reverse(ranges);
                Map<Integer, List<AwardPackageSaveDTO>> beansMap = Maps.newHashMap();
                for (String range : ranges) {
                    AwardPackageSaveDTO bean = JSON.parseObject(range, AwardPackageSaveDTO.class);
                    Integer taskId = bean.getTaskId();
                    List<AwardPackageSaveDTO> beans = beansMap.computeIfAbsent(taskId, k -> new ArrayList<>());
                    beans.add(bean);
                }

                StringBuilder sb = new StringBuilder();

                for (Map.Entry<Integer, List<AwardPackageSaveDTO>> entry : beansMap.entrySet()) {
                    int taskId = entry.getKey();
                    List<AwardPackageSaveDTO> dtoList = entry.getValue();
                    Long actId = dtoList.get(0).getActId();
                    List<AwardModel> list = awardModelService.listAwards(actId, taskId);
                    sb.append(emailContent(list, dtoList)).append("<br><br><br><br><br>");
                }
                String emailAddress = sysParameterService.getParamValue("award_change_notify_email_address", Const.SEND_TO);
                EmailUtil.send(sb.toString(), Lists.newArrayList(emailAddress.split(",")));
                logger.info("probabilityChangeEmailTimer send email done, emailAddress:{}", emailAddress);

            }
        } catch (Exception e) {
            logger.error("probabilityChangeEmailTimer error,e ", e);
        }
    }

    private String emailContent(List<AwardModel> current, List<AwardPackageSaveDTO> beans) {
        List<Integer> changeList = beans.stream().map(AwardPackageSaveDTO::getPackageId).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        sb.append("<h4>当前配置</h4><table>")
                .append("<tr><th>奖池ID</th><th>奖包ID</th><th>概率</th><th>奖包总数</th><th>单用户总数限制</th><th>每日限制</th><th>奖池名称</th></tr>");

        for (AwardModel awardModel : current) {
            if (changeList.contains(awardModel.getPackageId())) {
                sb.append("<tr style='color:red'><td>");
            } else {
                sb.append("<tr><td>");
            }
            sb.append(awardModel.getTaskId()).append("</td><td>")
                    .append(awardModel.getPackageId()).append("</td><td>")
                    .append(awardModel.getProbability()).append("</td><td>")
                    .append(awardModel.getPackageTotal()).append("</td><td>")
                    .append(awardModel.getUserHitLimit()).append("</td><td>")
                    .append(awardModel.getDailyHitLimit()).append("</td><td>")
                    .append(awardModel.getRemark()).append("</td></tr>");
        }

        sb.append("</table><br><br><br><h4>修改记录</h4>");

        sb.append("<table>")
                .append("<tr><th>奖池ID</th><th>奖包ID</th><th>概率</th><th>奖包总数</th><th>单用户总数限制</th><th>每日限制</th><th>奖池名称</th><th>操作uid</th></tr>");
        for (AwardPackageSaveDTO awardModel : beans) {
            sb.append("<tr><td>")
                    .append(awardModel.getTaskId()).append("</td><td>")
                    .append(awardModel.getPackageId()).append("</td><td>")
                    .append(awardModel.getProbability()).append("</td><td>")
                    .append(awardModel.getPackageTotal()).append("</td><td>")
                    .append(awardModel.getUserHitLimit()).append("</td><td>")
                    .append(awardModel.getDailyHitLimit()).append("</td><td>")
                    .append(awardModel.getRemark()).append("</td><td>")
                    .append(awardModel.getOpUid()).append("</td></tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }
}
