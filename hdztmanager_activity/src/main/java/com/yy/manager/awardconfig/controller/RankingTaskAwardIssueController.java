package com.yy.manager.awardconfig.controller;

import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.manager.awardconfig.bean.RankTaskAwardIssueReq;
import com.yy.manager.awardconfig.bean.RankTaskAwardIssueVo;
import com.yy.manager.awardconfig.service.RankTaskAwardIssueService;
import com.yy.manager.clear.bean.DropDownVo;
import com.yy.manager.tools.service.ExportExcelService;
import com.yy.manager.utils.PageResponse;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/6 16:07
 **/
@RestController
@RequestMapping("rankingTaskAward")
public class RankingTaskAwardIssueController {
    @Autowired
    private RankTaskAwardIssueService rankTaskAwardIssueService;
    @Autowired
    private ExportExcelService exportExcelService;

    @ApiOperation("查询榜单ID-名称下拉框")
    @PostMapping("listRankDropDown")
    public Response<List<DropDownVo>> listRankDropDown(@RequestParam(name = "actId", defaultValue = "0") int actId) {
        if (actId <= 0) {
            return Response.success(new ArrayList<>());
        }

        return Response.success(rankTaskAwardIssueService.listRankDropDown(actId));
    }

    @ApiOperation("查询榜单任务奖励记录")
    @PreAuthorize("@ss.hasPermi('activity:rankingTaskAward:query')")
    @RequestMapping("pageIssue")
    public PageResponse<RankTaskAwardIssueVo> pageAwardIssueLog(@RequestBody RankTaskAwardIssueReq req) {
        if (req == null || req.getActId() <= 0) {
            return PageResponse.empty();
        }

        return rankTaskAwardIssueService.page(req);
    }

    @ApiOperation("导出榜单任务奖励记录")
    @Log(title = "导出榜单任务奖励记录", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('activity:rankingTaskAward:export')")
    @RequestMapping("exportExcel")
    public String exportExcel(RankTaskAwardIssueReq req, HttpServletResponse response) throws Exception {
        exportExcelService.exportRankTaskAwardIssue(response, req);
        return null;
    }
}
