package com.yy.manager.tools.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.manager.hdzt.entity.*;
import com.yy.manager.hdzt.mapper.*;
import com.yy.manager.tools.bean.RankingConfigVo;
import com.yy.manager.utils.Convert;
import com.yy.manager.utils.DateUtil;
import com.yy.manager.utils.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Author: CXZ
 * @Desciption: 活动榜单请求辅助服务
 * @Date: 2020/9/19 21:31
 * @Modified:
 */
@Service
public class RankHelper {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztActivityDao hdztActivityDao;

    @Autowired
    private com.yy.manager.hdzt.mapper.RankingConfigDao rankingConfigDao;

    @Autowired
    private RankingPhaseDao rankingPhaseDao;

    @Autowired
    private RankingPhaseGroupDao rankingPhaseGroupDao;

    @Autowired
    private RankingPhasePkDao rankingPhasePkDao;

    @Autowired
    private com.yy.manager.hdzt.mapper.RankingPhasePkgroupDao rankingPhasePkgroupDao;

    @Autowired
    private RankingPhaseQualificationDao rankingPhaseQualificationDao;

    @Autowired
    private RankingItemDao rankingItemDao;

    @Autowired
    private RankingMemberDao rankingMemberDao;


    /**
     * 获取活动所有的榜单请求配置
     *
     * @param actId
     * @return
     */
    @DS("#{@ad.getHdztDs(#actId)}")
    public Response getRankConfig(Long actId) {
        //调整mysql分组
        com.yy.manager.hdzt.entity.HdztActivity hdztActivity = hdztActivityDao.selectOneByActId(actId);
        Assert.notNull(hdztActivity, "找不到活动");

        List<RankingConfig> rankingConfigs = rankingConfigDao.selectAllByActId(actId);
        Map<Long, RankingConfig> rankingConfigMap = rankingConfigs.stream()
                .collect(Collectors.toMap(item -> (long) item.getRankId(), Function.identity()));

        Map<Long, RankingPhase> phaseMap = rankingPhaseDao.getRankPhasesByActId(actId)
                .stream()
                .collect(Collectors.toMap(item -> (long) item.getPhaseId(), Function.identity()));


        //各个阶段对应的榜单map
        Map<Long, List<RankingConfigVo>> phaseRankingMap = Maps.newHashMap();
        //榜单对应的符合时间要求的阶段map
        Map<Long, List<Long>> rankingPhasesMap = Maps.newHashMap();

        //遍历活动所有榜单，准备榜单数据和阶段数据
        for (RankingConfig config : rankingConfigMap.values()) {

            long rankId = config.getRankId();

            RankingConfigVo vo = new RankingConfigVo();
            vo.setRankingConfig(config);
            RankingMember rankingMember = rankingMemberDao.selectByActIdAndRankId(actId, rankId);
            RankingItem rankingItem = rankingItemDao.selectByActIdAndRankId(actId, rankId);
            if (rankingMember == null || StringUtils.isBlank(rankingMember.getRoles())) {
                // throw new ServiceException(rankId + ":榜单累计成员配置错误");
                log.warn("getRankConfig error:{}榜单累计成员配置错误", rankId);
                continue;
            }
            boolean noGiftConfig = config.getLimitItem() == 1 && (rankingItem == null || StringUtils.isBlank(rankingItem.getItemIds()));
            if (noGiftConfig) {
                //throw new ServiceException(rankId + ":榜单礼物配置错误");
                log.warn("getRankConfig  error:{}榜单没有配置礼物", rankId);
                continue;
            }
            //榜单扩展属性
            vo.setRankingMember(rankingMember);
            vo.setRankingItem(rankingItem);


            Map<Long, RankingPhase> rankingPhaseMap = rankingPhaseDao.getRankPhasesByCode(actId, config.getPhaseGroupCode())
                    .stream().collect(Collectors.toMap(item -> item.getPhaseId() + 0L, Function.identity()));
            //所有阶段
            if (MapUtils.isNotEmpty(rankingPhaseMap)) {
                for (Long phaseId : rankingPhaseMap.keySet()) {
                    RankingPhase phase = rankingPhaseMap.get(phaseId);
                    if (config.getCalcBeginTime().after(phase.getBeginTime()) || config.getCalcEndTime().before(phase.getEndTime())) {
                        continue;
                    }
                    phaseRankingMap.computeIfAbsent(phaseId, (k) -> Lists.newArrayList()).add(vo);
                    rankingPhasesMap.computeIfAbsent(rankId, (k) -> Lists.newArrayList()).add(phaseId);
                }
            } else {
                phaseRankingMap.computeIfAbsent(0L, (k) -> Lists.newArrayList()).add(vo);
                rankingPhasesMap.put(rankId, Lists.newArrayList(0L));
            }
        }
        List<RankingConfigVo> allContributionRankings = Lists.newArrayList();
        Map<RankingConfigVo, List<RankingConfigVo>> rankContributionRankingsMap = Maps.newHashMap();
        for (Map.Entry<Long, List<RankingConfigVo>> entry : phaseRankingMap.entrySet()) {
            for (RankingConfigVo rankingConfigVo : entry.getValue()) {
                List<RankingConfigVo> contributionRankings = findContributionRankings(rankingConfigVo, entry.getValue(), entry.getKey());
                allContributionRankings.addAll(contributionRankings);
                if (!contributionRankings.isEmpty()) {
                    rankContributionRankingsMap.computeIfAbsent(rankingConfigVo, (k) -> Lists.newArrayList()).addAll(contributionRankings);
                }
            }
        }


        Map<String, List<Long>> phaseGroup = phaseMap.values().stream()
                .sorted(Comparator.comparing(RankingPhase::getBeginTime))
                .collect(Collectors.groupingBy(RankingPhase::getPhaseGroupCode, Collectors.mapping(item -> item.getPhaseId() + 0L, Collectors.toList())));


        if (phaseRankingMap.containsKey(0L)) {
            phaseGroup.put("0", Lists.newArrayList(0L));
        }

        //按阶段组生成榜单请求参数
        List<JSONObject> phaseGroupInfos = Lists.newArrayList();
        for (Map.Entry<String, List<Long>> entry : phaseGroup.entrySet()) {
            String groupName = rankingPhaseGroupDao.getPhaseGroupName(actId, entry.getKey());
            groupName = Convert.toString(groupName, entry.getKey());
            JSONObject phaseGroupInfo = new JSONObject();

            List<JSONObject> phaseInfos = Lists.newArrayList();
            phaseGroupInfo.put("stagename", groupName);
            phaseGroupInfo.put("stage", phaseInfos);
            phaseGroupInfos.add(phaseGroupInfo);

            for (long phaseId : entry.getValue()) {
                JSONObject phaseInfo = new JSONObject(true);
                phaseInfo.put("phaseId", phaseId);
                Optional<RankingPhase> rankingPhase = Optional.ofNullable(phaseMap.get(phaseId));
                phaseInfo.put("name", rankingPhase.map(RankingPhase::getPhaseName).orElse("" + phaseId));
                phaseInfo.put("begin", rankingPhase.map(RankingPhase::getBeginTime).orElse(hdztActivity.getBeginTime()).getTime());
                phaseInfo.put("end", rankingPhase.map(RankingPhase::getEndTime).orElse(hdztActivity.getEndTime()).getTime());
                List<JSONObject> ranks = Lists.newArrayList();
                List<RankingConfigVo> rankingConfigVos = phaseRankingMap.get(phaseId);
                if (CollectionUtils.isEmpty(rankingConfigVos)) {
                    continue;
                }
                for (RankingConfigVo rankingConfigVo : rankingConfigVos) {
                    if (allContributionRankings.contains(rankingConfigVo)) {
                        continue;
                    }
                    RankingConfig rankingConfig = rankingConfigVo.getRankingConfig();
                    JSONObject phaseRankInfo = new JSONObject(true);
                    phaseRankInfo.put("rankId", rankingConfig.getRankId());
                    phaseRankInfo.put("rankName", rankingConfig.getRankName());
                    phaseRankInfo.put("query", getRankQuery(rankingConfigVo, rankingPhase.orElse(null)));
                    ranks.add(phaseRankInfo);
                }
                if (!ranks.isEmpty()) {
                    phaseInfo.put("ranks", ranks);
                    phaseInfos.add(phaseInfo);
                }
            }

        }


        //榜单的贡献榜
        JSONObject fansList = new JSONObject(true);

        for (Map.Entry<RankingConfigVo, List<RankingConfigVo>> entry : rankContributionRankingsMap.entrySet()) {

            Integer rankId = entry.getKey().getRankingConfig().getRankId();
            Pattern pattern = Pattern.compile("(\\d)0+" + rankId);
            RankingConfig rankingConfig = entry.getKey().getRankingConfig();

            JSONArray rankFans = new JSONArray();
            List<RankingConfigVo> rankingConfigVos = entry.getValue().stream().distinct()

                    .sorted((o1, o2) -> {
                        //后缀相同优先
                        Integer rankId1 = o1.getRankingConfig().getRankId();
                        Integer rankId2 = o2.getRankingConfig().getRankId();
                        boolean rankIdEnd1 = rankId1.toString().endsWith(rankId.toString());
                        boolean rankIdEnd2 = rankId2.toString().endsWith(rankId.toString());

                        if (rankIdEnd1 != rankIdEnd2) {
                            return rankIdEnd1 ? -1 : 1;
                        } else if (rankIdEnd1) {
                            Matcher rankMatcher1 = pattern.matcher(rankId1.toString());
                            Matcher rankMatcher2 = pattern.matcher(rankId2.toString());
                            boolean find1 = rankMatcher1.find();
                            boolean find2 = rankMatcher2.find();
                            if (find1 != find2) {
                                return find1 ? -1 : 1;
                            } else if (find1) {
                                return Integer.parseInt(rankMatcher1.group(1)) - Integer.parseInt(rankMatcher2.group(1));
                            }

                        }
                        //榜单时间相同优先
                        boolean sameTime1 = rankingConfig.getCalcBeginTime().equals(o1.getRankingConfig().getCalcBeginTime())
                                && rankingConfig.getCalcEndTime().equals(o1.getRankingConfig().getCalcEndTime());
                        boolean sameTime2 = rankingConfig.getCalcBeginTime().equals(o2.getRankingConfig().getCalcBeginTime())
                                && rankingConfig.getCalcEndTime().equals(o2.getRankingConfig().getCalcEndTime());
                        if (sameTime1 != sameTime2) {
                            return sameTime1 ? -1 : 1;
                        }

                        return 0;
                    })
                    .collect(Collectors.toList());

            for (RankingConfigVo rankingConfigVo : rankingConfigVos) {
                RankingConfig conRankingConfig = rankingConfigVo.getRankingConfig();
                JSONObject fansRank = new JSONObject();
                fansRank.put("rankId", conRankingConfig.getRankId());
                fansRank.put("rankName", conRankingConfig.getRankName());
                // fansRank.put("findSrcMember", "{key}");
                //fansRank.put("query", getRankQuery(conRankingConfig,phaseId)) ;
                rankFans.add(fansRank);
            }
            fansList.put("id_" + rankId, rankFans);
        }
        return Response.success(ImmutableMap.of("stages", phaseGroupInfos, "fans", fansList));
    }

    private List<RankingConfigVo> findContributionRankings(RankingConfigVo ranking, List<RankingConfigVo> samePhaseRanks, long phaseId) {
        RankingConfig rankingConfig = ranking.getRankingConfig();
        List<RankingConfigVo> contributionRankings = samePhaseRanks.stream().filter(
                samePhaseRank -> {
                    RankingConfig samePhaseRankConfig = samePhaseRank.getRankingConfig();

                    boolean same =
                            !rankingConfig.getRankId().equals(samePhaseRankConfig.getRankId())
                                    && rankingConfig.getItemKey().equals(samePhaseRankConfig.getItemKey())
                                    && rankingConfig.getValueType().equals(samePhaseRankConfig.getValueType())
                                    && rankingConfig.getTimeKey().equals(samePhaseRankConfig.getTimeKey())
                                    && rankingConfig.getTimeKeyBegin().equals(samePhaseRankConfig.getTimeKeyBegin())
                                    && rankingConfig.getTimeKeyEnd().equals(samePhaseRankConfig.getTimeKeyEnd())
                                    &&
                                    (
                                            phaseId > 0 ||
                                                    (rankingConfig.getCalcBeginTime().equals(samePhaseRankConfig.getCalcBeginTime())
                                                            && rankingConfig.getCalcEndTime().equals(samePhaseRankConfig.getCalcEndTime()))
                                    )
                                    && SetUtils.isEqualSet(Sets.newHashSet(ranking.getRankingItem().getItemIds().split( StrUtil.COMMA))
                                    , Sets.newHashSet(samePhaseRank.getRankingItem().getItemIds().split( StrUtil.COMMA)));

                    if (!same) {
                        return false;
                    }

                    return StringUtils.isNotBlank(samePhaseRankConfig.getMemberKey())
                            && CollectionUtils.isSubCollection(Sets.newHashSet(ranking.getRankingMember().getRoles().split("\\|")), Sets.newHashSet(samePhaseRankConfig.getMemberKey().split("\\|")));
                }

        ).collect(Collectors.toList());


        return contributionRankings;
    }

    private JSONObject getRankQuery(RankingConfigVo ranking, RankingPhase phase) {
        JSONObject query = new JSONObject();
        RankingConfig rankingConfig = ranking.getRankingConfig();
        long actId = rankingConfig.getActId();
        long rankId = rankingConfig.getRankId();
        long phaseId = phase == null ? 0L : phase.getPhaseId();
        query.put("useNewWay", "1");
        query.put("showType", "1");
        if (StringUtils.isNotBlank(rankingConfig.getMemberKey())) {
            String memberKey = rankingConfig.getMemberKey().split("|")[0];
            String[] memberKeys = memberKey.split("&");
            //query.put("findSrcMember", "{key}");
        }

        if (rankingConfig.getTimeKey() > 0) {
            String dateFormat = rankingConfig.getTimeKey() == 2 ? DateUtil.PATTERN_TYPE7 : DateUtil.PATTERN_TYPE2;
            List<Date> timePeriodList = getRankTimePeriodList(rankingConfig, phase);
            List<String> timePeriods = timePeriodList.stream().flatMap(Stream::of).map(time -> DateUtil.format(time, dateFormat)).distinct().collect(Collectors.toList());
            query.put("dateStr", timePeriods);
            query.put("dateFormat", dateFormat);
        }

        List<Integer> showTypes = Lists.newArrayList();


        RankingPhasePk pk = rankingPhasePkDao.selectOneBySrc(actId, rankId, phaseId);
        if (pk != null) {
            //pk展示
            showTypes.add(2);
            List<RankingPhasePkgroup> rankingPhasePkgroups = rankingPhasePkgroupDao.selectByRankPhase(actId, (long) pk.getDestRankId(), phaseId, rankId);


            Map<String, Set<String>> dayCodeMap = rankingPhasePkgroups.stream()
                    .collect(Collectors.groupingBy(RankingPhasePkgroup::getDay, Collectors.mapping(RankingPhasePkgroup::getCode, Collectors.toSet())));
            int pkGroupMax = dayCodeMap.values().stream().mapToInt(Collection::size).max()
                    .orElse(0);
            if (pkGroupMax > 1) {
                showTypes.add(3);
            }
        }
        List<RankingPhaseQualification> rankingPhaseQualifications = rankingPhaseQualificationDao.select(actId, rankId, phaseId);
        if (CollectionUtils.isNotEmpty(rankingPhaseQualifications)) {
            //是否能分组展示
            Integer qualificationGroupMax = rankingPhaseQualifications.stream()
                    .map(RankingPhaseQualification::getPkGroup)
                    .filter(StringUtils::isNotBlank)
                    .map(JSONArray::parseArray)
                    .mapToInt(JSONArray::size).max()
                    .orElse(0);
            if (qualificationGroupMax > 1) {
                showTypes.add(4);
            }
        }
        if (!showTypes.isEmpty()) {
            showTypes.add(0, 1);
            query.put("showType", showTypes);
        }

        return query;
    }

    private List<Date> getRankTimePeriodList(RankingConfig rankingConfig, RankingPhase phase) {

        List<Date> dates = Lists.newArrayList();
        long calcBeginTime = rankingConfig.getCalcBeginTime().getTime();
        long calcEndTime = rankingConfig.getCalcEndTime().getTime();

        if (phase != null) {
            calcBeginTime = Math.max(phase.getBeginTime().getTime(), calcBeginTime);
            calcEndTime = Math.min(phase.getEndTime().getTime(), calcEndTime);
        }

        Integer timeType = rankingConfig.getTimeKey();
        String timeKeyBegin = rankingConfig.getTimeKeyBegin();
        String timeKeyEnd = rankingConfig.getTimeKeyEnd();
        int timeKeyBeginHour = StringUtils.isNotBlank(timeKeyBegin) ? Integer.parseInt(timeKeyBegin.substring(0, 2)) : 0;
        int timeKeyEndHour = StringUtils.isNotBlank(timeKeyEnd) ? Integer.parseInt(timeKeyEnd.substring(0, 2)) : 23;


        for (Date date = new Date(calcBeginTime); date != null && date.getTime() <= calcEndTime; date = nextTimeFirstSecondByTimeType(date, timeType)) {
            int hour = DateUtil.getHours(date);
            if (timeType == 2 & (hour < timeKeyBeginHour || hour > timeKeyEndHour)) {
                continue;
            }
            dates.add(date);
        }

        return dates;
    }

    private Date nextTimeFirstSecondByTimeType(Date date, Integer timeType) {
        switch (timeType) {
            case 1:
                return DateUtil.getNextDayFirstSecond(date);
            case 2:
                return DateUtil.getNextHourFirstSecond(date);
            case 3:
                return DateUtil.getNextWeekFirstSecond(date);
            case 4:
                return DateUtil.getNextMonthFirstSecond(date);
            case 5:
                return DateUtil.getNextSeasonFirstSecond(date);
            case 6:
                return DateUtil.getNextYearFirstSecond(date);
            default:
                return null;
        }

    }
}
