package com.yy.manager.tools.consts;


/**
 * desc:活动中台榜单类型  对应中台：RankDefineType
 *
 * @createBy 曾文帜
 * @create 2020-07-20 12:11
 **/
public class HdztRankType {
    /**
     * 1-当前比拼的榜
     */
    public static final String CONTEST = "1";

    /**
     * 2-阶段晋级过来的初始名单（此时dateStr不生效）
     */
    public static final String PROMOT = "2";

    /**
     * 3- 榜单贡献来源
     */
    public static final String SRC = "3";

    /**
     * 4-过任务
     */
    public static final String TASK = "4";

    /**
     * 5-整个阶段所有排名（没有圈定晋级名单的所有人的榜单）
     */
    public static final String PHASE_ALL = "5";


    /**
     * 6-当前比拼的榜 积分来源汇总
     */
    public static final String CONTEST_PK = "6";

    /**
     * 7-PK失败,复活的名单
     */
    public static final String REVIVE = "7";


    public static final String CP_DAILY_RANK_STRING = "cp_daily_rank";

    public static final String CP_RANK_STRING = "cp_rank";

    public static final String NORMAL_DAILY_RANK_STRING = "normal_daily_rank";
    public static final String NORMAL_RANK_STRING = "normal_rank";

    public static final String CONTRIBUTE_DAILY_RANK_STRING = "contribute_daily_rank";

    public static final String CONTRIBUTE_RANK_STRING = "contribute_rank";





}
