package com.yy.manager.tools.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.manager.awardconfig.bean.AwardIssueLogReq;
import com.yy.manager.awardconfig.bean.AwardIssueLogVo;
import com.yy.manager.awardconfig.bean.RankTaskAwardIssueReq;
import com.yy.manager.awardconfig.bean.RankTaskAwardIssueVo;
import com.yy.manager.awardconfig.service.AwardIssueLogService;
import com.yy.manager.awardconfig.service.RankTaskAwardIssueService;
import com.yy.manager.hdzt.entity.*;
import com.yy.manager.hdzt.mapper.*;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;


/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2022/03/27 16:41
 * @Modified:
 */
@Service
public class ExportExcelService {

    @Resource
    AwardIssueVoDao awardIssueVoDao;
    @Resource
    private RankingItemTransformVoDao rankingItemTransformVoDao;
    @Resource
    private com.yy.manager.hdzt.mapper.ActorVoDao actorVoDao;
    @Resource
    private RankingPhasePkDao rankingPhasePkDao;
    @Resource
    private RankingPhaseGroupDao rankingPhaseGroupDao;
    @Resource
    private RankingPhaseDao rankingPhaseDao;
    @Resource
    private HdztActivityDao hdztActivityDao;
    @Resource
    private RankDtoDao rankDtoDao;
    @Resource
    private PhaseDtoDao phaseDtoDao;
    @Resource
    private AwardIssueLogService awardIssueLogService;
    @Resource
    private RankTaskAwardIssueService rankTaskAwardIssueService;

    /**
     * 角色ID -> 角色名
     */
    private Map<String, String> role2NameMap = new HashMap<>();


    /**
     * 导出 奖品发放excel
     *
     * @param response
     * @throws Exception
     */
    public void exportAwardIssue(HttpServletResponse response) throws Exception {
        ExcelWriter excelWriter = null;
        try {
            String fileName = "奖品发放" + System.currentTimeMillis();
            excelWriter = EasyExcel.write(setResponse(fileName, response)).registerWriteHandler(styleWrite()).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("奖品发放").needHead(Boolean.FALSE).build();
            WriteTable writeTable0 = EasyExcel.writerTable(0).needHead(Boolean.TRUE).build();
            writeTable0.setClazz(AwardIssueVo.class);
            List<AwardIssueVo> allAwardIssue = awardIssueVoDao.getAllAwardIssue();

            excelWriter.write(allAwardIssue, writeSheet, writeTable0);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 导出 奖品发放excel
     *
     * @param response
     * @throws Exception
     */
    public void exportAwardIssue(HttpServletResponse response, AwardIssueLogReq param) throws Exception {
        ExcelWriter excelWriter = null;
        try {
            String fileName = "奖品发放" + System.currentTimeMillis();
            excelWriter = EasyExcel.write(setResponse(fileName, response)).registerWriteHandler(styleWrite()).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("奖品发放").needHead(Boolean.FALSE).build();
            WriteTable writeTable0 = EasyExcel.writerTable(0).needHead(Boolean.TRUE).build();
            writeTable0.setClazz(AwardIssueLogVo.class);
            List<AwardIssueLogVo> allAwardIssue = awardIssueLogService.list(param);

            excelWriter.write(allAwardIssue, writeSheet, writeTable0);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 导出 奖品发放excel
     *
     * @param response
     * @throws Exception
     */
    public void exportRankTaskAwardIssue(HttpServletResponse response, RankTaskAwardIssueReq param) throws Exception {
        ExcelWriter excelWriter = null;
        try {
            String fileName = "榜单任务奖品发放" + System.currentTimeMillis();
            excelWriter = EasyExcel.write(setResponse(fileName, response)).registerWriteHandler(styleWrite()).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("榜单任务奖品发放").needHead(Boolean.FALSE).build();
            WriteTable writeTable0 = EasyExcel.writerTable(0).needHead(Boolean.TRUE).build();
            writeTable0.setClazz(RankTaskAwardIssueVo.class);
            List<RankTaskAwardIssueVo> allAwardIssue = rankTaskAwardIssueService.list(param);

            excelWriter.write(allAwardIssue, writeSheet, writeTable0);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }



    /**
     * 活动礼物sheet 导出
     *
     * @param excelWriter
     * @param actId
     */
    private void giftSheet(ExcelWriter excelWriter, long actId) {
        WriteSheet writeSheet = EasyExcel.writerSheet("礼物导入").needHead(Boolean.FALSE).build();
        WriteTable writeTable0 = EasyExcel.writerTable(0).needHead(Boolean.TRUE).build();
        List<com.yy.manager.hdzt.entity.RankingItemTransformVo> rankingItemTransformList = rankingItemTransformVoDao.getRankingItemTransform(actId);
        writeTable0.setClazz(com.yy.manager.hdzt.entity.RankingItemTransformVo.class);
        excelWriter.write(rankingItemTransformList, writeSheet, writeTable0);
    }

    /**
     * 角色配置sheet导出
     *
     * @param excelWriter
     */
    private void roleSheet(ExcelWriter excelWriter) {
        WriteSheet writeSheet = EasyExcel.writerSheet("角色配置").needHead(Boolean.FALSE).build();
        WriteTable writeTable0 = EasyExcel.writerTable(0).needHead(Boolean.TRUE).build();
        List<ActorVo> allActor = actorVoDao.getAllActor();

        for (ActorVo actorVo : allActor) {
            role2NameMap.put(String.valueOf(actorVo.getRole()), actorVo.getName());
        }

        writeTable0.setClazz(ActorVo.class);
        excelWriter.write(allActor, writeSheet, writeTable0);
    }

    /**
     * 创建excel流文件，填充活动榜单配置excel sheet数据
     *
     * @param actId
     * @param response
     * @throws Exception
     */

    @DS("#{@ad.getHdztDs(#actId)}")
    public void outputExcel(long actId, HttpServletResponse response) throws Exception {
        ExcelWriter excelWriter = null;
        try {
            List<HdztActivityVo> hdztActivityVoList = actData(actId);
            HdztActivityVo hdztActivityVo = hdztActivityVoList.get(0);
            String fileName = hdztActivityVo.getIdAndName() + System.currentTimeMillis();
            excelWriter = EasyExcel.write(setResponse(fileName, response)).registerWriteHandler(styleWrite()).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            //礼物导入
            giftSheet(excelWriter, actId);
            //角色配置
            roleSheet(excelWriter);

            WriteSheet writeSheet = EasyExcel.writerSheet("活动榜单配置").needHead(Boolean.FALSE).build();
            //Activity配置表
            WriteTable writeTable0 = EasyExcel.writerTable(0).needHead(Boolean.TRUE).build();
            writeTable0.setClazz(HdztActivityVo.class);
            excelWriter.write(hdztActivityVoList, writeSheet, writeTable0);

            //辅助榜
            List<RankingPhasePk> phasePkByActId = rankingPhasePkDao.getPhasePkByActId(actId);
            Map<Long, List<Long>> assistMap = Maps.newHashMap();
            for (RankingPhasePk rankingPhasePk : phasePkByActId) {
                boolean validPkAward = (rankingPhasePk.getPkAward() != null && !"".equals(rankingPhasePk.getPkAward()))
                        || rankingPhasePk.getRankAward() != null && !"".equals(rankingPhasePk.getRankAward());
                if (validPkAward) {
                    if (!assistMap.containsKey(rankingPhasePk.getSrcRankId())) {
                        List<Long> list = new ArrayList<>();
                        list.add(rankingPhasePk.getDestPhaseId().longValue());
                        assistMap.put(rankingPhasePk.getSrcRankId().longValue(), list);
                    } else {
                        assistMap.get(rankingPhasePk.getSrcRankId()).add(rankingPhasePk.getSrcRankId().longValue());
                    }
                }
            }

            //总体数据
            List<com.yy.manager.hdzt.entity.RankDto> rankDtos = rankDtoDao.getRankDto(actId);
            //用于计算多少个表
            Set<String> set = new HashSet<>();
            //映射关联表数据
            Map<String, List<com.yy.manager.hdzt.entity.RankDto>> rankDtoMap = Maps.newHashMap();
            for (com.yy.manager.hdzt.entity.RankDto rankDto : rankDtos) {
                String remark = rankDto.getRemark();
                String[] split = remark.split("-");
                //计算表数量
                //格式为 remark总榜名 - PhaseGroupCode
                String title = split[0] + "-" + rankDto.getPhaseGroupCode();
                set.add(title);
                //关联表数据
                String mapKey = split[0];
                if (!rankDtoMap.containsKey(mapKey)) {
                    List<com.yy.manager.hdzt.entity.RankDto> list4Map = new LinkedList<>();
                    rankDtoMap.put(mapKey, list4Map);
                }
                rankDtoMap.get(mapKey).add(rankDto);
            }

            //各rank 对应 phase 来源
            List<PhaseDto> allPhaseDto = phaseDtoDao.getPhaseDtos(actId);
            Map<String, List<PhaseDto>> phaseDtoMap = Maps.newHashMap();
            for (PhaseDto phaseDto : allPhaseDto) {
                long destRankId = phaseDto.getDestRankId();
                long destPhaseId = phaseDto.getDestPhaseId();
                if (!phaseDtoMap.containsKey(destRankId + "_" + destPhaseId)) {
                    List<PhaseDto> phaseDtoList = new LinkedList<>();
                    phaseDtoList.add(phaseDto);
                    phaseDtoMap.put(destRankId + "_" + destPhaseId, phaseDtoList);
                } else {
                    phaseDtoMap.get(destRankId + "_" + destPhaseId).add(phaseDto);
                }
            }
            //生成表
            int i = 1;
            for (String s : set) {
                List<List<Object>> data = getDatas(actId, s, rankDtoMap, assistMap, phaseDtoMap);
                WriteTable writeTable = getHeadWriteTable(actId, s, i++);
                excelWriter.write(data, writeSheet, writeTable);
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    private List<List<Object>> getDatas(long actId, String s, Map<String, List<RankDto>> rankDtoMap, Map<Long, List<Long>> assistMap, Map<String, List<PhaseDto>> phaseDtoMap) {

        String[] split = s.split("-");
        String phaseGroupCode = split[1];
        //空置一列
        List<RankingPhase> rankPhases = rankingPhaseDao.getRankPhase(actId, phaseGroupCode);

        //填写各表数据
        List<List<Object>> data = new LinkedList<>();
        List<com.yy.manager.hdzt.entity.RankDto> rankDtoTableDatas = rankDtoMap.get(split[0]);
        for (com.yy.manager.hdzt.entity.RankDto rankDtoTableData : rankDtoTableDatas) {
            List<Object> row = new ArrayList<>();
            //空置一列
            row.add(" ");
            Long rankId = rankDtoTableData.getRankId();
            //榜单id
            row.add(rankId);

            //判断辅助榜
            if (assistMap.containsKey(rankId)) {
                List<Long> phaseIdList = assistMap.get(rankId);
                List<RankingPhase> temptList = new LinkedList<>();
                for (RankingPhase rankPhase : rankPhases) {
                    for (Long phaseId : phaseIdList) {
                        if (rankPhase.getPhaseId().equals(phaseId)) {
                            temptList.add(rankPhase);
                        }
                    }
                }
                rankPhases = temptList;
            }

            String remark = rankDtoTableData.getRemark();
            int index = remark.indexOf("-", 0);
            //榜单名
            row.add(remark.substring(index + 1));

            for (RankingPhase rankPhase : rankPhases) {
                List<PhaseDto> phaseDtos = phaseDtoMap.get(rankDtoTableData.getRankId() + "_" + rankPhase.getPhaseId());
                if (phaseDtos != null && phaseDtos.size() > 0) {
                    row.add(getPhaseString(phaseDtos, rankPhase));
                } else {
                    row.add("");
                }
            }

            String rolesNum = rankDtoTableData.getRoles();
            String rolesString = roleId2Name(rolesNum);
            //角色配置
            row.add(rolesString);
            //礼物配置
            row.add(rankDtoTableData.getItemIds());

            String memberKeyNum = rankDtoTableData.getMemberKey();
            String memberKeyString = "";
            if (!"".equals(memberKeyNum)) {
                memberKeyString = roleId2Name(memberKeyNum);
            }
            //memberKey
            row.add(memberKeyString);

            String attrName = rankDtoTableData.getAttrname();
            if (attrName != null) {
                //榜单扩展属性键值
                row.add(attrName + "_" + rankDtoTableData.getAttrvalue());
            }

            data.add(row);
        }
        //每表的间隔
        data.add(new ArrayList<>());
        data.add(new ArrayList<>());
        data.add(new ArrayList<>());
        return data;

    }

    private String getPhaseString(List<PhaseDto> phaseDtos, RankingPhase rankPhase) {
        StringBuilder sb = new StringBuilder();
        for (int j = 0; j < phaseDtos.size(); j++) {
            if (j != 0) {
                sb.append("\n");
            }
            PhaseDto phaseDto = phaseDtos.get(j);
            if (j > 0) {
                PhaseDto lastPhase = phaseDtos.get(j - 1);
                if (lastPhase.getSrcRankId() == phaseDto.getSrcRankId() && lastPhase.getSrcPhaseId() == phaseDto.getSrcPhaseId()) {
                    continue;
                }
            }

            sb.append(phaseDto.getSrcRankId()).append("|").append(phaseDto.getSrcPhaseId()).append( StrUtil.COMMA);
            sb.append("前").append(phaseDto.getPassCount());
            if (phaseDto.getPkReviveCount() != 0) {
                sb.append("败").append(phaseDto.getPkReviveCount());
            }
            String rankrs = phaseDto.getRankrs();
            if (rankrs != null && !"".equals(rankrs)) {
                int indexOf = rankrs.indexOf( StrUtil.COMMA, 0);
                int distance = rankrs.charAt(indexOf + 1) - rankrs.charAt(indexOf - 1);
                sb.append("\n").append("*PK,近").append(distance);
                if ("00000000".equals(phaseDto.getDay())) {
                    sb.append(",全");
                } else {
                    sb.append(",日");
                }
                String pkAward = phaseDto.getPkAward();
                Long ppSrcRank = phaseDto.getPpSrcRank();
                if (pkAward != null && !"".equals(pkAward) && ppSrcRank != null) {
                    sb.append( StrUtil.COMMA).append(ppSrcRank).append("|").append(rankPhase.getPhaseId());
                    String replacePkAward = pkAward.replaceAll( StrUtil.COMMA, ";");
                    sb.append(replacePkAward);

                    String rankAward = phaseDto.getRankAward();
                    if (rankAward != null && !"".equals(rankAward)) {
                        String replaceRankAward = rankAward.replaceAll( StrUtil.COMMA, ";");
                        sb.append(replaceRankAward);
                    }
                }
            }
        }
        return sb.toString();
    }
    private WriteTable getHeadWriteTable(long actId, String s,int index) {

        List<List<String>> headList = new ArrayList<>();
        String[] split = s.split("-");
        String phaseGroupCode = split[1];
        //空置一列
        headList.add(Arrays.asList("", " ", "", ""));
        headList.add(Arrays.asList(split[0], " ", "", "榜单ID"));
        headList.add(Arrays.asList("", "", " ", ""));
        String phaseGroupNameAndCode = rankingPhaseGroupDao.getPhaseGroupName(actId, phaseGroupCode) + "(" + phaseGroupCode + ")";
        List<RankingPhase> rankPhases = rankingPhaseDao.getRankPhase(actId, phaseGroupCode);

        //表头
        for (RankingPhase rankPhase : rankPhases) {
            String phaseIdAndName = rankPhase.getPhaseId() + ":" + rankPhase.getPhaseName();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(rankPhase.getBeginTime());
            int beginMonth = calendar.get(Calendar.MONTH) + 1;
            int beginDate = calendar.get(Calendar.DATE);
            int beginHour = calendar.get(Calendar.HOUR_OF_DAY);
            calendar.setTime(rankPhase.getEndTime());
            int endMonth = calendar.get(Calendar.MONTH) + 1;
            int endDate = calendar.get(Calendar.DATE);
            String time = "";
            if (beginHour == 0) {
                time = beginMonth + "." + beginDate + "-" + endMonth + "." + endDate;
            } else {
                time = beginMonth + "." + beginDate + "." + beginHour + "-" + endMonth + "." + endDate;
            }

            headList.add(Arrays.asList("", phaseGroupNameAndCode, phaseIdAndName, time));
        }
        headList.add(Arrays.asList("", "", "", "角色配置:."));
        headList.add(Arrays.asList("", "", "", "礼物配置"));
        headList.add(Arrays.asList("", "", "", "贡献榜MEMBER_KEY"));
        headList.add(Arrays.asList("", "", "", "榜单扩展属性键值"));

        WriteTable writeTable = EasyExcel.writerTable(index).needHead(Boolean.TRUE).build();
        writeTable.setAutomaticMergeHead(true);

        writeTable.setHead(headList);
        writeTable.setCustomWriteHandlerList(getCustomWriteHandlerList());

        return writeTable;
    }


    /**
     * 角色Id转换角色名称
     *
     * @param roles
     * @return
     */
    public String roleId2Name(String roles) {
        boolean contains = roles.contains("|");
        String regex = contains ? "\\|" : "&";
        String[] rolesString = roles.split(regex);
        StringBuilder sb = new StringBuilder();
        int len = rolesString.length;
        for (int i = 0; i < len - 1; i++) {
            String s = role2NameMap.get(rolesString[i]);
            sb.append(s);
            if (contains) {
                sb.append("|");
            } else {
                sb.append("&");
            }
        }
        sb.append(role2NameMap.get(rolesString[len - 1]));
        return sb.toString();
    }

    public List<com.yy.manager.hdzt.entity.HdztActivity> getEffectiveActivity() {
        return hdztActivityDao.getEffectiveActivities();
    }

    public com.yy.manager.hdzt.entity.HdztActivity getExactActivity(long actId) throws Exception {
        com.yy.manager.hdzt.entity.HdztActivity exactActivity = hdztActivityDao.getExactActivity(actId);
        if (exactActivity == null) {
            throw new Exception("没有该活动Id");
        }
        return exactActivity;
    }

    /**
     * activity数据
     *
     * @param actId
     * @return
     * @throws Exception
     */
    private List<HdztActivityVo> actData(long actId) throws Exception {
        com.yy.manager.hdzt.entity.HdztActivity exactActivity = getExactActivity(actId);
        HdztActivityVo hdztActivityVo = new HdztActivityVo();

        hdztActivityVo.setIdAndName(actId + "(" + exactActivity.getActName() + ")");
        hdztActivityVo.setActGroup(exactActivity.getActGroup().longValue());
        hdztActivityVo.setScale("大");
        hdztActivityVo.setBusiId(exactActivity.getBusiId().longValue());

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(exactActivity.getBeginTime());
        int beginMonth = calendar.get(Calendar.MONTH) + 1;
        int beginDate = calendar.get(Calendar.DATE);
        int beginHour = calendar.get(Calendar.HOUR_OF_DAY);
        calendar.setTime(exactActivity.getEndTime());
        int endMonth = calendar.get(Calendar.MONTH) + 1;
        int endDate = calendar.get(Calendar.DATE);
        hdztActivityVo.setActTime(beginMonth + "." + beginDate + "." + beginHour + "-" + endMonth + "." + endDate);

        List<HdztActivityVo> list = new LinkedList<>();
        list.add(hdztActivityVo);
        list.add(new HdztActivityVo());
        list.add(new HdztActivityVo());
        list.add(new HdztActivityVo());
        return list;
    }

    /**
     * 响应返回excel文件
     *
     * @param fileName
     * @param response
     * @return
     * @throws Exception
     */
    private OutputStream setResponse(String fileName, HttpServletResponse response) throws Exception {
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            return response.getOutputStream();
        } catch (IOException e) {
            throw new Exception("创建文件失败！");
        }
    }

    /**
     * 样式策略
     *
     * @return
     */
    private HorizontalCellStyleStrategy styleWrite() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置
        headWriteCellStyle.setFillForegroundColor(IndexedColors.YELLOW1.getIndex());
        WriteFont headWriteFont = new WriteFont();

        headWriteFont.setFontHeightInPoints((short) 14);

        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // 背景
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        // 设置单元格上下左右边框为细边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //自动换行
        contentWriteCellStyle.setWrapped(true);
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 创建默认单元格样式，行高，列高
     *
     * @return
     */
    private List<WriteHandler> getCustomWriteHandlerList() {
        List<WriteHandler> list = Lists.newLinkedList();
        // 默认高18
        list.add(new SimpleRowHeightStyleStrategy((short) 18, (short) 18));
        return list;
    }
}
