package com.yy.manager.tools.service;


import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.manager.utils.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/10/27 10:57
 * @Modified:
 */
@Service
public class ExcelService {
    private static final Logger log = LoggerFactory.getLogger(com.yy.manager.tools.service.ExcelService.class);
    @Autowired
    private com.yy.manager.tools.service.GeActivityHttpService geActivityHttpService;

    public void transmitExport(HttpServletRequest req, HttpServletResponse resp) throws Exception {

        Long actId = Long.parseLong(req.getParameter("actId"));

        Response<JSONObject> response = geActivityHttpService.queryRank(req, actId, req.getQueryString());

        String columns = req.getParameter("columns");
        List<String> columnList = Lists.newArrayList();
        if (StringUtils.isNotBlank(columns)) {
            columnList = Lists.newArrayList(columns.split( StrUtil.COMMA));
        }
        Set<String> allColumnSet = Sets.newHashSet();
        if (response.getResult() == Response.OK) {
            JSONObject rankData = response.getData();
            String name = rankData.getString("rankName");
            JSONArray array = rankData.getJSONArray("list");
            List<Map<String, Object>> resultArray = Lists.newArrayList();

            for (int i = 0; i < array.size(); i++) {
                Map<String, Object> resultObject = Maps.newHashMap();
                flattening("", array.get(i), resultObject, columnList, allColumnSet);
                if (!resultObject.isEmpty()) {
                    resultArray.add(resultObject);
                }
            }
            if (columnList.isEmpty()) {
                columnList = Lists.newArrayList(allColumnSet);
            }
            writeExcel(resp, name, "", resultArray, columnList);
        } else {
            resp.setContentType(MediaType.APPLICATION_JSON_VALUE);
            resp.setCharacterEncoding(StandardCharsets.UTF_8.name());
            resp.getWriter().print(JSONObject.toJSONString(response));
        }
    }

    /**
     * 扁平化
     *
     * @param preKey
     * @param sourceObject
     * @param targetObject
     * @param filterColumns
     * @param allColumns
     */
    private void flattening(String preKey, Object sourceObject, Map<String, Object> targetObject, final List<String> filterColumns, Set<String> allColumns) {
        String separator = "";
        if (StringUtils.isNotBlank(preKey)) {
            separator = ".";
        }
        if (sourceObject instanceof JSONObject) {
            JSONObject object = (JSONObject) sourceObject;
            for (String a : object.keySet()) {
                String key = preKey + separator + a;
                flattening(key, object.get(a), targetObject, filterColumns, allColumns);
            }
        } else if (sourceObject instanceof JSONArray) {
            JSONArray array = (JSONArray) sourceObject;
            for (int i = 0; i < array.size(); i++) {
                String key = preKey + separator + "$" + i;
                flattening(key, array.get(i), targetObject, filterColumns, allColumns);
            }
        } else if (CollectionUtils.isEmpty(filterColumns) || filterColumns.contains(preKey)) {
            targetObject.put(preKey, sourceObject);
            allColumns.add(preKey);
        }
    }


    /**
     * 导出 Excel ：一个 sheet，带表头.
     *
     * @param response  HttpServletResponse
     * @param fileName
     * @param sheetName
     * @param dataMaps
     * @param columns
     * @throws Exception
     */
    public static void writeExcel(HttpServletResponse response,
                                  String fileName, String sheetName, List<Map<String, Object>> dataMaps, List<String> columns) throws Exception {


        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(getOutputStream(fileName, response));
        ExcelWriter writer = excelWriterBuilder.build();

        List<List<String>> head = columns.stream().map(Lists::newArrayList).collect(Collectors.toList());
        List<List<Object>> dataList = Lists.newArrayList();
        for (Map<String, Object> dataMap : dataMaps) {
            List<Object> dataItem = Lists.newArrayList();
            for (String column : columns) {
                Object data = dataMap.getOrDefault(column, "");
                dataItem.add(data);
            }
            dataList.add(dataItem);
        }
        WriteSheet sheet = EasyExcel.writerSheet(sheetName).head(head).build();


        writer.write(dataList, sheet);
        writer.finish();
    }

    /**
     * 导出文件时为Writer生成OutputStream.
     *
     * @param fileName 文件名
     * @param response response
     * @return ""
     */
    private static OutputStream getOutputStream(String fileName,
                                                HttpServletResponse response) throws Exception {
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");
            return response.getOutputStream();
        } catch (IOException e) {
            throw new Exception("导出excel表格失败!", e);
        }
    }

    private int getActGroup(long actId) {
        long num1000000000 = 1000000000;
        if (actId / num1000000000 == 0) {
            return 1;
        } else {
            return ((int) (actId / 1000)) % 10;
        }
    }
}
