package com.yy.manager.tools.controller;

import com.alibaba.fastjson.JSONObject;
import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.common.utils.SecurityUtils;
import com.yy.manager.hdztmanager.service.ISysParameterService;
import com.yy.manager.tools.bean.RoleAdjustResult;
import com.yy.manager.tools.service.ChangeRoleService;
import com.yy.manager.utils.Convert;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021.09.27 10:20
 */
@RestController
@RequestMapping("/changeRole")
public class ChangeRoleController {

    @Autowired
    private ChangeRoleService changeRoleService;

    @Autowired
    private ISysParameterService sysParameterService;

    private String forceExeUid = "force_exe_uid";

    //@ApiOperation("分析角色变化信息")
    //@PreAuthorize("@ss.hasPermi('activity:roleChange:cal')")
    @PostMapping(value = "/calculateRoleChange")
    public Response<RoleAdjustResult> calculateRoleChange(@RequestBody JSONObject data) throws Exception {

        long loginUid = SecurityUtils.getLoginUid();
        Boolean forceExe = data.getBoolean("forceExe");
        //强制执行,验证权限
        if (forceExe) {
            String uids = sysParameterService.queryParameterValue(forceExeUid);
            if (!uids.contains(loginUid + "")) {
                return Response.fail(501, "强制执行仅限开发人员使用,请联系开发!!!");
            }
        }

        long actId = Convert.toLong(data.get("actId"));
        String memberId = data.getString("memberId").trim();
        //String srcRole = data.getString("srcRole").trim();
        String targetRole = data.getString("targetRole").trim();
        int roleType = data.getInteger("roleType");
        RoleAdjustResult result = changeRoleService.calculateRoleChange(actId, memberId, targetRole, roleType, forceExe);
        return Response.success(result);

    }

    @ApiOperation("查询角色")
    @PreAuthorize("@ss.hasPermi('activity:roleChange:query')")
    @RequestMapping("/queryUserRole")
    public Response<JSONObject> queryUserRole(long actId, String memberId, int roleType) throws Exception {
        JSONObject memberRole = changeRoleService.getMemberRole(actId, memberId, roleType);
        return Response.success(memberRole);

    }

    @ApiOperation("执行角色更改")
    @Log(title = "执行角色更改", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('activity:roleChange:exc')")
    @PostMapping(value = "/executeChangeRole")
    public Response<JSONObject> executeChangeRole(@RequestBody RoleAdjustResult result) throws Exception {
        JSONObject jsonObject = changeRoleService.executeChangeRole(result);
        jsonObject.put("allList", result.getRankMoveList());
        return Response.success(jsonObject);
    }

}
