package com.yy.manager.tools.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.manager.ecology.entity.GeMethodLog;
import com.yy.manager.exception.Code;
import com.yy.manager.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;


/**
 * @Author: CXZ
 * @Desciption: 中控活动进程http服务
 * @Date: 2021/7/26 11:52
 * @Modified:
 */
@Service
public class GeActivityHttpService {
    private static final Logger logger = LoggerFactory.getLogger(com.yy.manager.tools.service.GeActivityHttpService.class);
    @Value("${http-host.gameecology.activity}")
    private String host;

    @Value("${http-host.gameecology.activityonline}")
    private String onlineHost;


    private static HttpClientHelper httpClientHelper = new HttpClientHelper(Const.UTF_8);

    public String getOnlineHostByActId(long actId) {
        return onlineHost + "/" + actId;
    }

    public String getHostByActId(long actId) {
        int group = ActGroupHelper.getActGroup(actId);
        return getHostByGroup(group, actId);
    }

    private String getHostByGroup(int group, long actId) {
        return host + "/" + actId;
    }

    /**
     * 榜单查询
     *
     * @param request
     * @param geMethodLog
     * @return
     */
    public Response<JSONObject> retryMethod(HttpServletRequest request, GeMethodLog geMethodLog) {

        if (geMethodLog == null || StringUtils.isBlank(geMethodLog.getGroupCode())) {
            return new Response(Code.E_DATA_ERROR, "请求数据异常");
        }
        long opUid = AccountUtil.getLoginUid(request);


        String path = "/testTool/manualRetry";
        //没有活动id暂时不支持路由到具体套数
        String host = getHostByGroup(Convert.toInt(geMethodLog.getGroupCode()), -1);
        String url = host + path;
        String responseString = "";

        responseString = HttpClientHelper.executePostWithCookie(url, request.getCookies(), JSON.toJSONString(geMethodLog));
        Response<JSONObject> httpResp = JSON.parseObject(responseString, Response.class);
        logger.info("retryMethod info url:{} result:{} opUid:{}", url, httpResp.getResult(), opUid);
        if (httpResp.getResult() == 0) {
            httpResp.setResult(Response.OK);
            return httpResp;
        } else {
            return Response.fail(Code.E_DATA.getCode(), httpResp.getReason());
        }

    }

    /**
     * 榜单查询
     *
     * @param request
     * @param actId
     * @param queryString
     * @return
     */
    public Response<JSONObject> queryRank(HttpServletRequest request, Long actId, String queryString) {

        if (actId == null || actId <= 0) {
            return new Response(Code.E_DATA_ERROR, "活动id输入错误");
        }
        long opUid = AccountUtil.getLoginUid(request);

        if (StringUtils.isBlank(queryString)) {
            // 获取参数，这里只能拿到查询参数和以表单形式提交的参数，requestBody 的拿不到
            Map<String, String[]> params = request.getParameterMap();
            if (params != null && !params.isEmpty()) {
                StringBuilder builder = new StringBuilder();
                params.forEach((k, v) -> {
                    builder.append("&").append(k).append("=").append(v.length == 0 ? "" : v[0]);
                });
                queryString = builder.substring(1);
            }
        }

        Assert.isTrue(StringUtils.isNotBlank(queryString), "查询错误");

        String path = "/hdzt_rank/queryRank?" + queryString;
        String host = getHostByActId(actId);
        String url = host + path;
        String responseString = "";
        try {
            responseString = httpClientHelper.excuteGet(url, request.getHeader("Cookie"));
            Response<JSONObject> httpResp = JSON.parseObject(responseString, Response.class);
            logger.info("queryRank info url:{} result:{} opUid:{}", url, httpResp.getResult(), opUid);
            if (httpResp.getResult() == 0) {
                httpResp.setResult(Response.OK);
                return httpResp;
            } else {
                return Response.fail(Code.E_DATA.getCode(), httpResp.getReason());
            }

        } catch (Exception e) {
            logger.error("queryRank error@actId:{} url:{} result:{}  opUid:{} {}", actId, url, responseString, opUid, e.getMessage(), e);
        }
        return new Response(Code.E_DATA);
    }

    /**
     * 奖品发放
     *
     * @param request
     * @param actId
     * @param packageId
     * @param itemId
     * @param uid
     * @return
     */
    public Response releasePackageItem(HttpServletRequest request, Long actId, Long packageId, Long itemId, Long uid) {
        if (actId == null || actId <= 0) {
            return new Response(Code.E_DATA_ERROR, "活动id输入错误");
        }

        Assert.isTrue(actId != null && actId > 0, "actId错误");
        Assert.isTrue(packageId != null && packageId > 0, "packageId错误");
        Assert.isTrue(itemId != null && itemId > 0, "itemId错误");
        Assert.isTrue(uid != null && uid > 0, "uid错误");

        long opUid = AccountUtil.getLoginUid(request);
        String seq = UUID.randomUUID().toString();
        String path = "/testTool/releasePackageItem?actId=" + actId + "&packageId=" + packageId + "&itemId=" + itemId + "&uid=" + uid + "&seq=" + seq;
        String host = getHostByActId(actId);
        String url = host + path;
        String responseString = "";
        try {
            responseString = httpClientHelper.excuteGet(url, request.getHeader("Cookie"));
            Response httpResp = JSON.parseObject(responseString, Response.class);
            logger.info("releasePackageItem info url:{} result:{}  opUid:{}", url, responseString, opUid);
            if (httpResp.getResult() == 1) {
                return Response.ok();
            } else {
                return Response.fail(Code.E_DATA.getCode(), httpResp.getReason());
            }

        } catch (IOException e) {
            logger.error("releasePackageItem error@actId:{} url:{} responseString:{} opUid:{}  {}", actId, url, responseString, opUid, e.getMessage(), e);
        }
        return new Response(Code.E_DATA);
    }

    /**
     * 奖品发放
     *
     * @param request
     * @param actId
     * @param taskId
     * @param uid
     * @return
     */
    public Response releaseTaskItem(HttpServletRequest request, Long actId, Long taskId, Long uid) {
        if (actId == null || actId <= 0) {
            return new Response(Code.E_DATA_ERROR, "活动id输入错误");
        }

        Assert.isTrue(actId != null && actId > 0, "actId错误");
        Assert.isTrue(taskId != null && taskId > 0, "taskId错误");

        Assert.isTrue(uid != null && uid > 0, "uid错误");

        long opUid = AccountUtil.getLoginUid(request);
        String seq = UUID.randomUUID().toString();
        String path = "/testTool/releaseTaskItem?actId=" + actId + "&taskId=" + taskId + "&uid=" + uid + "&seq=" + seq;
        String host = getHostByActId(actId);
        String url = host + path;
        String responseString = "";
        try {
            responseString = httpClientHelper.excuteGet(url, request.getHeader("Cookie"));
            logger.info("releasePackageItem info url:{} result:{}  opUid:{}", url, responseString, opUid);
            return JSON.parseObject(responseString, Response.class);
        } catch (IOException e) {
            logger.error("releaseTaskItem error@actId:{} url:{} responseString:{} opUid:{}  {}", actId, url, responseString, opUid, e.getMessage(), e);
        }
        return new Response(Code.E_DATA);
    }

    /**
     * 复制中控redis数据
     */
    public Response invokeHdzkInnerActAction(boolean fixOnline, String actionName, Long opUid, Long actId) {
        if (actId == null || actId <= 0) {
            return new Response(Code.E_DATA_ERROR, "活动id输入错误");
        }

        String seq = UUID.randomUUID().toString();
        long time = System.currentTimeMillis();
        String sign = Const.buildProxySign(time, actionName, actId);
        String paraTemplate = "actId=%s&seq=%s&uid=%s&sign=%s&time=%s&action=%s";
        String para = String.format(paraTemplate, actId, seq, opUid, sign, time, actionName);
        String path = "/inner/act/" + actionName + "?" + para;
        String host = fixOnline ? getOnlineHostByActId(actId) : getHostByActId(actId);
        String url = host + path;
        String responseString = "";
        try {
            responseString = httpClientHelper.excuteGet(url);
            logger.info("invokeHdzkInnerActAction info url:{} result:{}  opUid:{}", url, responseString, opUid);
            return JSON.parseObject(responseString, Response.class);
        } catch (IOException e) {
            logger.error("invokeHdzkInnerActAction error@actId:{} url:{} responseString:{} opUid:{}  {}", actId, url, responseString, opUid, e.getMessage(), e);
        }
        return new Response(Code.E_DATA);
    }


    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }
}
