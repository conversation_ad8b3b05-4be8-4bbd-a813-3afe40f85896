package com.yy.manager.tools.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.yy.manager.hdzt.entity.RankingPhasePkgroup;
import com.yy.manager.tools.bean.LoginInfoBean;
import com.yy.manager.tools.bean.QualificationPkGroupBean;
import com.yy.manager.tools.service.PkConfigOprService;
import com.yy.manager.tools.service.TanabataService;
import com.yy.manager.utils.IEAFConst;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/18
 * @功能说明
 * @版本更新列表
 */
@Controller
@RequestMapping("/pkConfig")
public class PkConfigOprController {
    @Autowired
    PkConfigOprService pkConfigOprService;

    @Autowired
    TanabataService tanabataService;

    private Logger log = LoggerFactory.getLogger(com.yy.manager.tools.controller.PkConfigOprController.class);



    @RequestMapping("/updatePkConfig4Tree.do")
    @ResponseBody
    public Map<String, String> updatePkConfig4Tree(Long actId, Long rankId, String pkMembers, HttpServletRequest request){
        log.info(getUserInfo(request)+"updatePkConfigTree -> {} {}",rankId, pkMembers);
        Map<String, String> res = new HashMap<>(1);
        int result = tanabataService.intPkmembers(actId, rankId, pkMembers);
        if (result==0){
            res.put("result", "设置成功");
        }else{
            res.put("result", "设置失败");
        }
        return res;
    }

    /**
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param rankrs [ [ [1,1],[2,2] ], [ [..],...] ]
     * 注意：pkgroup表对应存在多个分组时理论上不会更新qualification表
     */
    @RequestMapping("/updatePkConfig4PK.do")
    @ResponseBody
    public Map<String, String> updatePkConfig4PK(Long actId, Long rankId, Long phaseId, String rankrs, String day, HttpServletRequest request){
        Map<String, String> res = new HashMap<>(1);
        int[][][] rankrsArrays = null;
        try{
//            String s = rankrs.substring(rankrs.indexOf("[[[")+3,rankrs.indexOf("]]]"));
//            String[] r1 = s.split("]],\\[\\[");
//            for (int i = 0; i < r1.length; i++) {
//                String[] r2 = r1[i].split("],\\[");
//                for (int j = 0; j < r2.length; j++) {
//                    String[] r3 = r2[j].split( StrUtil.COMMA);
//                    for (int k = 0; k < r3.length; k++) {
//                        if (rankrsArrays == null) {
//                            rankrsArrays = new int[r1.length][r2.length][r3.length];
//                        }
//                        rankrsArrays[i][j][k] = Integer.parseInt(r3[k]);
//                    }
//                }
//            }
            List<List<List<Integer>>> list_group = JSONArray.parseArray(rankrs).toJavaObject(new TypeReference<List<List<List<Integer>>>>() {});
            for (int i = 0; i < list_group.size(); i++) {
                List<List<Integer>> list_verse = list_group.get(i);
                for (int j = 0; j < list_verse.size(); j++) {
                    List<Integer> list_member = list_verse.get(j);
                    for (int k = 0; k < list_member.size(); k++) {
                        if (rankrsArrays == null) {
                            rankrsArrays = new int[list_group.size()][list_verse.size()][list_member.size()];
                        }
                        rankrsArrays[i][j][k] = list_member.get(k);
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
            res.put("result", "rankrs data format incorrect");
            log.error("rankrs fail to transform to array",rankrs);
            return res;
        }

        try {
            List<RankingPhasePkgroup> phasePkGroupBaseList = pkConfigOprService.selectPhasePkGroup(null, actId, rankId, phaseId, day);
            if (phasePkGroupBaseList.size()!=rankrsArrays.length){
                res.put("result", "UnMatch rankrs Size Compare "+rankrsArrays.length+" to "+phasePkGroupBaseList.size()+" with data: "+actId+"-"+rankId+"-"+phaseId+"-"+rankrs+"-"+day);
                log.error("UnMatch rankrs Size Compare "+rankrsArrays.length+" to "+phasePkGroupBaseList.size()+" with data: "+actId+"-"+rankId+"-"+phaseId+"-"+rankrs+"-"+day);
                return res;
            }else{
                Collections.sort(phasePkGroupBaseList, (groupA,groupB)->groupA.getCode().compareTo(groupB.getCode()));
            }

            for (int i = 0; i < rankrsArrays.length; i++) {
//                final char code = (char) ('A'+i);
                String code = phasePkGroupBaseList.get(i).getCode();
                if (pkConfigOprService.updatePhasePkGroup(actId, rankId, phaseId, JSON.toJSONString(rankrsArrays[i]), String.valueOf(code), day) == 0) {
                    res.put("result", "updatePhasePkGroup fail while update "+actId+"-"+rankId+"-"+phaseId+"-"+rankrs+"-"+code+"-"+day);
                    log.error("updatePhasePkGroup fail while update {}",actId+"-"+rankId+"-"+phaseId+"-"+rankrs+"-"+code+"-"+day);
                    return res;
                }
            }

            //单分组->pk排名对战不分组，更新qualification
            String defaultDay = "00000000";
            if (rankrsArrays.length==1 && defaultDay.equals(day)) {
                String qualificationPkGroup = pkConfigOprService.selectQualificationPkGroup(actId,rankId,phaseId);
                List<QualificationPkGroupBean> list = JSON.parseArray(qualificationPkGroup, QualificationPkGroupBean.class);
                for (int i = 0; i < rankrsArrays[0].length; i++) {
                    QualificationPkGroupBean qualificationPkGroup_new = list.get(i);
                    qualificationPkGroup_new.setRanks(Arrays.stream(rankrsArrays[0][i]).boxed().collect(Collectors.toList()));
                    Collections.replaceAll(list,list.get(i),qualificationPkGroup_new);
                }
                if (pkConfigOprService.updateQualification(actId, rankId, phaseId, JSON.toJSONString(list)) == 0) {
                    res.put("result", "updatePhaseGroupQualification fail");
                    return res;
                }
            }

        }catch (Exception e){
            e.printStackTrace();
            res.put("result", "update fail");
            log.error("updatePhasePkGroup fail with {} while update {} ",e.toString(),actId+"-"+rankId+"-"+phaseId+"-"+rankrs+"-"+day);
            return res;
        }
        log.info(getUserInfo(request)+"updatePhasePkGroup success -> {}",actId+"-"+rankId+"-"+phaseId+"-"+rankrs+"-"+day);
        res.put("result", "success");
        return res;
    }

    @RequestMapping("/getPkGroupRankrs4PK.do")
    @ResponseBody
    public List<List<List<Integer>>> getPkGroupConfig4PK(Long actId, Long rankId, Long phaseId, String day){
        List<RankingPhasePkgroup> pkgroups = pkConfigOprService.selectPhasePkGroup(rankId, actId, rankId, phaseId, day);
        List<List<List<Integer>>> rankrs = new ArrayList<>();
        try {
            for (RankingPhasePkgroup pkgroup : pkgroups) {
                List<List<Integer>> pkMemberRanks = JSONArray.parseArray(pkgroup.getRankrs()).toJavaObject(new TypeReference<List<List<Integer>>>() {});
                rankrs.add(pkMemberRanks);
            }
        }catch (NullPointerException e){
            e.printStackTrace();
            return new ArrayList<>();
        }
        log.info("获取pk分组配置 {}", ArrayUtils.toString(rankrs));
        return rankrs;
    }

    @RequestMapping("/markAccomplished.do")
    @ResponseBody
    public Map<String, String> markAccomplished(Long actId, Long rankId, Long phaseId){
        Map<String, String> res = new HashMap<>(1);
        try{
            pkConfigOprService.markAccomplished(actId, rankId, phaseId);
            res.put("result","success");
        }catch (Exception e){
            e.printStackTrace();
            log.info("标记出错-> {}",e.toString());
            res.put("result",e.toString());
        }
        return res;
    }

    @RequestMapping("/unmarkAccomplished.do")
    @ResponseBody
    public Map<String, String> unmarkAccomplished(Long actId, Long rankId, Long phaseId){
        Map<String, String> res = new HashMap<>(1);
        try{
            pkConfigOprService.unmarkAccomplished(actId, rankId, phaseId);
            res.put("result","success");
        }catch (Exception e){
            e.printStackTrace();
            log.info("标记出错-> {}",e.toString());
            res.put("result",e.toString());
        }
        return res;
    }

    @RequestMapping("/fetchQualPkGroup.do")
    @ResponseBody
    public String fetchQualPkGroup( Long srcActId, Long srcRankId, Long srcPhaseId){
        return pkConfigOprService.selectQualificationPkGroup(srcActId, srcRankId, srcPhaseId);
    }

    @RequestMapping(value = "/updateQualPkGroup.do", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, String> updateQualPkGroup(Long srcActId, Long srcRankId, Long srcPhaseId, String pkgroup_json){
        Map<String, String> res = new HashMap<>(1);
        int ret = pkConfigOprService.updateQualificationPkGroup(srcActId, srcRankId, srcPhaseId, pkgroup_json);
        if (ret<=0){
            res.put("result","update fail, cannot find matched qualification data");
            return res;
        }
        res.put("result","success");
        return res;
    }

    private String getUserInfo(HttpServletRequest request){
        Long uid = Long.valueOf(-1);
        String uname = "";
        LoginInfoBean li = (LoginInfoBean) request.getSession().getAttribute(IEAFConst.LOGIN_INFO);
        if (li!=null) {
            uid = li.getUsrid();
            uname = li.getUname();
        }
        return "{UID:"+uid+" UNAME:"+uname+"} ";
    }
}
