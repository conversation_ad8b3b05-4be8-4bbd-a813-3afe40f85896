package com.yy.manager.tools.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yy.common.utils.StringUtils;
import com.yy.manager.commonservice.CommonService;
import com.yy.manager.factory.RedisGroupFactory;
import com.yy.manager.hdzt.entity.*;
import com.yy.manager.hdzt.service.*;
import com.yy.manager.tools.bean.RankRemove;
import com.yy.manager.tools.service.remove.CPRankMemberRemover;
import com.yy.manager.tools.service.remove.ContributeRankMemberRemover;
import com.yy.manager.tools.service.remove.NormalRankMemberRemover;
import com.yy.manager.utils.DateUtil;
import com.yy.manager.utils.Response;
import com.yy.manager.utils.TimeKeyHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RemoveRoleService {

    @Autowired
    private RedisGroupFactory redisGroupFactory;

    @Autowired
    private IHdztActivityService hdztActivityService;

    @Autowired
    private IHdztActorService hdztActorService;

    @Autowired
    private IRankingEnrollmentService rankingEnrollmentService;

    @Autowired
    private IRankingMemberService rankingMemberService;

    @Autowired
    private IRankingConfigService rankingConfigService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IRankingConfigAttrService rankingConfigAttrService;

    @Autowired
    private CPRankMemberRemover cpRankMemberRemover;

    @Autowired
    private NormalRankMemberRemover normalRankMemberRemover;

    @Autowired
    private ContributeRankMemberRemover contributeRankMemberRemover;

    public Response<List<Integer>> queryRoleRanks(long actId, int roleId) {
        List<RankingMember> rankingMembers = rankingMemberService.queryRankByRoleId(actId, String.valueOf(roleId));
        List<Integer> rankIds = rankingMembers.stream().map(RankingMember::getRankId).toList();
        return Response.success(rankIds);
    }

    public Response<Integer> removeRole(long actId, String member, int roleId, List<Integer> rankIds) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity == null) {
            return Response.fail(400, "活动不存在");
        }

        int redisGroup = activity.getActGroup();
        // 删除报名信息
        RedisTemplate redisTemplate = redisGroupFactory.getHdztRedisByGroup(redisGroup);
        HdztActor actor = hdztActorService.getHdztActor(actId, roleId);
        if (actor != null) {
            int rs = rankingEnrollmentService.deleteMemberEnrollment(actId, member, roleId);
            log.info("removeMember with actId:{} member:{} roleId:{} rs:{}", actId, member, roleId, rs);

            String enrollKey = "act:" + actId + ":enroll";
            String enrollHashKey = member + StringUtils.VERTICAL_BAR + actor.getType();
            redisTemplate.opsForHash().delete(enrollKey, enrollHashKey);
        }

        if (CollectionUtils.isEmpty(rankIds)) {
            return Response.success(0);
        }

        // 删除相关榜单
        List<RankingMember> rankingMembers = rankingMemberService.queryRankByRoleId(actId, String.valueOf(roleId));

        rankingMembers = rankingMembers.stream().filter(rankingMember -> rankIds.contains(rankingMember.getRankId())).toList();
        if (CollectionUtils.isEmpty(rankingMembers)) {
            return Response.success(0);
        }

        Date now = commonService.getNow(actId);
        String nowStr = DateFormatUtils.format(now, DateUtil.DEFAULT_PATTERN);

        String rankIdStr = rankingMembers.stream().map(RankingMember::getRankId).map(String::valueOf).collect(Collectors.joining(","));
        List<RankingConfigVO> rankingConfigVOs = rankingConfigService.selectRankingConfigVo(actId, rankIdStr, nowStr);
        if (CollectionUtils.isEmpty(rankingConfigVOs)) {
            return Response.success(0);
        }

        Map<Integer, RankingConfigVO> rankingConfigMap = rankingConfigVOs.stream().collect(Collectors.toMap(RankingConfigVO::getRankId, Function.identity(), (k1, k2) -> k1));

        int result = 0;
        for (RankingMember rankingMember : rankingMembers) {
            int rankId = rankingMember.getRankId();
            RankingConfigVO rankingConfig = rankingConfigMap.get(rankId);
            if (StringUtils.isNotBlank(rankingConfig.getMemberKey())) {
                //TODO: 贡献榜需要scan key，暂时不处理
                continue;
            }

            result++;

            List<RankingConfigAttr> rankingConfigAttrs = rankingConfigAttrService.getAllConfigAttr(actId, rankId);
            Map<String, String> configAttr = rankingConfigAttrs.stream().collect(Collectors.toMap(RankingConfigAttr::getAttrname, RankingConfigAttr::getAttrvalue));

            String role = getRole(rankingMember.getRoles(), roleId);
            RankRemove remove = new RankRemove();
            remove.setRankId(rankId);
            remove.setPhaseId(rankingConfig.getPhaseId());
            remove.setRedisGroup(redisGroup);
            String dateStr = TimeKeyHelper.getTimeCode(rankingConfig.getTimeKey(), now);
            remove.setDateStr(dateStr);

            if (role.contains(StringUtils.AND)) { // cp榜单
                // 最高cp辅助榜单
                String cp_uniq_rank_ids = configAttr.get("cp_uniq_rank_ids");
                if (StringUtils.startsWith(cp_uniq_rank_ids, StringUtils.OPEN_BRACKET)) {
                    List<Long> uniqueRankIds = JSON.parseArray(cp_uniq_rank_ids, Long.class);
                    if (!uniqueRankIds.isEmpty()) {
                        remove.setUniqueRankId1(uniqueRankIds.get(0));
                    }

                    if (uniqueRankIds.size() > 1) {
                        remove.setUniqueRankId2(uniqueRankIds.get(1));
                    }
                }

                cpRankMemberRemover.remove(actId, remove, member);

                continue;
            }

            normalRankMemberRemover.remove(actId, remove, member);

            String contributeRanks = configAttr.get("contributeRanks");
            if (!StringUtils.startsWith(contributeRanks, StringUtils.OPEN_BRACKET)) {
                continue;
            }

            JSONArray jsonArray = JSON.parseArray(contributeRanks);
            if (CollectionUtils.isEmpty(jsonArray)) {
                continue;
            }

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                long contributeRankId = jsonObject.getLongValue("rankId");
                if (contributeRankId > 0) {
                    remove.setRankId(contributeRankId);
                    contributeRankMemberRemover.remove(actId, remove, member);
                }
            }
        }

        return Response.success(result);
    }

    private static String getRole(String roles, int roleId) {
        String[] roleList = StringUtils.split(roles, StringUtils.VERTICAL_BAR);
        String roleIdStr = String.valueOf(roleId);
        for (String role : roleList) {
            if (StringUtils.contains(role, roleIdStr)) {
                return role;
            }
        }

        throw new IllegalStateException("cannot find role");
    }
}
