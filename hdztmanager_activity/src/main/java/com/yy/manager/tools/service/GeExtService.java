package com.yy.manager.tools.service;

import com.yy.manager.tools.dao.GeRedisDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/11/26
 * @功能说明
 * @版本更新列表
 */
@Service
public class GeExtService {

    @Autowired
    GeRedisDao geRedisDao;

    public void setSettleStatus(Long actId){
        final String key = "act:"+actId+":settle_status";
        geRedisDao.set(key, String.valueOf(1));
    }

    public void delSettleStatus(Long actId){
        final String key = "act:"+actId+":settle_status";
        geRedisDao.del(key);
    }
}
