package com.yy.manager.tools.service;

import com.yy.manager.hdzt.entity.RankingPhasePkgroup;
import com.yy.manager.hdzt.entity.RankingPhaseQualification;
import com.yy.manager.hdzt.mapper.HdztActivityDao;
import com.yy.manager.hdzt.service.IRankingPhasePkgroupService;
import com.yy.manager.hdzt.service.IRankingPhaseQualificationService;
import com.yy.manager.tools.dao.MgrRedisDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/18
 * @功能说明
 * @版本更新列表
 */
@Service
public class PkConfigOprService {
    @Autowired
    MgrRedisDao mgrRedisDao;
    @Resource
    private HdztActivityDao hdztActivityDao;
    @Autowired
    private IRankingPhaseQualificationService rankingPhaseQualificationService;

    @Autowired
    private IRankingPhasePkgroupService rankingPhasePkgroupService;


    public int updateQualification(Long srcActId, Long srcRankId, Long srcPhaseId, String pkGroup){
        RankingPhaseQualification qualification = new RankingPhaseQualification();
        qualification.setSrcActId(srcActId);
        qualification.setSrcRankId(srcRankId);
        qualification.setSrcPhaseId(srcPhaseId);
        RankingPhaseQualification to_qualification = new RankingPhaseQualification();
        to_qualification.setPkGroup(pkGroup);
        to_qualification.setUtime(new Date());
        return rankingPhaseQualificationService.update(qualification, to_qualification, null);
    }

    public int updatePhasePkGroup(Long destActId, Long destRankId, Long destPhaseId, String rankrs, String code, String day){
        RankingPhasePkgroup pkgroup_me = new RankingPhasePkgroup();
        RankingPhasePkgroup pkgroup_to = new RankingPhasePkgroup();
        pkgroup_me.setDestActId(destActId);
        pkgroup_me.setDestRankId(destRankId);
        pkgroup_me.setDestPhaseId(destPhaseId);
        pkgroup_me.setCode(code);
        pkgroup_me.setDay(day);
        pkgroup_to.setRankrs(rankrs);
        pkgroup_to.setUtime(new Date());

        return rankingPhasePkgroupService.update(pkgroup_me,pkgroup_to,null);
    }

    public List<RankingPhasePkgroup> selectPhasePkGroup(Long srcRankId, Long destActId, Long destRankId, Long destPhaseId, String day){
        RankingPhasePkgroup pkgroup = new RankingPhasePkgroup();
//        pkgroup.setSrcRankId(srcRankId);
        pkgroup.setDestActId(destActId);
        pkgroup.setDestRankId(destRankId);
        pkgroup.setDestPhaseId(destPhaseId);
        pkgroup.setDay(day);

        return rankingPhasePkgroupService.select(pkgroup, null, null);
    }

    public int markAccomplished(Long actId, Long rankId, Long phaseId){
        final String accomplishAdjustKey = "act:" + actId + ":accomplishAdjustKey:" + rankId + ":" + phaseId;
        final String eventAccomplishAdjust = "act:" + actId + ":eventAccomplishAdjust:" + rankId + ":" + phaseId;
        mgrRedisDao.set(hdztActivityDao.getGroup(actId), accomplishAdjustKey,String.valueOf(1));
        mgrRedisDao.set(hdztActivityDao.getGroup(actId), eventAccomplishAdjust,String.valueOf(1));
        return 1;
    }

    public int unmarkAccomplished(Long actId, Long rankId, Long phaseId){
        final String accomplishAdjustKey = "act:" + actId + ":accomplishAdjustKey:" + rankId + ":" + phaseId;
        final String eventAccomplishAdjust = "act:" + actId + ":eventAccomplishAdjust:" + rankId + ":" + phaseId;
        mgrRedisDao.del(hdztActivityDao.getGroup(actId), accomplishAdjustKey);
        mgrRedisDao.del(hdztActivityDao.getGroup(actId), eventAccomplishAdjust);
        return 1;
    }

    public String selectQualificationPkGroup(Long srcActId, Long srcRankId, Long srcPhaseId) {
        RankingPhaseQualification rankingPhaseQualification = new RankingPhaseQualification();
        rankingPhaseQualification.setSrcActId(srcActId);
        rankingPhaseQualification.setSrcRankId(srcRankId);
        rankingPhaseQualification.setSrcPhaseId(srcPhaseId);
        List<RankingPhaseQualification> list = rankingPhaseQualificationService.select(rankingPhaseQualification, null, null);
        if (list.isEmpty()){
            return "";
        }else{
            return list.get(0).getPkGroup();
        }
    }

    public int updateQualificationPkGroup(Long srcActId, Long srcRankId, Long srcPhaseId, String pkgroup){
        RankingPhaseQualification rankingPhaseQualification_me = new RankingPhaseQualification();
        RankingPhaseQualification rankingPhaseQualification_to = new RankingPhaseQualification();
        rankingPhaseQualification_me.setSrcActId(srcActId);
        rankingPhaseQualification_me.setSrcRankId(srcRankId);
        rankingPhaseQualification_me.setSrcPhaseId(srcPhaseId);
        rankingPhaseQualification_to.setPkGroup(pkgroup);
        rankingPhaseQualification_to.setUtime(new Date());
        return rankingPhaseQualificationService.update(rankingPhaseQualification_me, rankingPhaseQualification_to, null);
    }
}
