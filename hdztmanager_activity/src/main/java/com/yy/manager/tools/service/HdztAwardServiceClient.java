package com.yy.manager.tools.service;

import com.google.common.collect.Maps;
import com.yy.manager.thrift.hdzt.award.BatchWelfareRequest;
import com.yy.manager.thrift.hdzt.award.BatchWelfareResult;
import com.yy.manager.thrift.hdzt.award.HdztAwardService;
import com.yy.manager.utils.ActGroupHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class HdztAwardServiceClient {

    @Reference(protocol = "attach_nythrift", registry = "reg-yrpc", owner = "${thrift.client.hdztaward.s2sname}")
    private HdztAwardService.Iface proxy;

    @Reference(protocol = "attach_nythrift", registry = "reg-yrpc", owner = "${thrift.client.hdztaward.s2sname2}")
    private HdztAwardService.Iface proxy2;

    @Reference(protocol = "attach_nythrift", registry = "reg-yrpc", owner = "${thrift.client.hdztaward.s2sname3}")
    private HdztAwardService.Iface proxy3;

    @Reference(protocol = "attach_nythrift", registry = "reg-yrpc", owner = "${thrift.client.hdztaward.s2sname4}")
    private HdztAwardService.Iface proxy4;

    @Reference(protocol = "attach_nythrift", registry = "reg-yrpc", owner = "${thrift.client.hdztaward.s2sname5}")
    private HdztAwardService.Iface proxy5;

    @Reference(protocol = "attach_nythrift", registry = "reg-yrpc", owner = "${thrift.client.hdztaward.s2sname6}")
    private HdztAwardService.Iface proxy6;

    @Reference(protocol = "attach_nythrift", registry = "reg-yrpc", owner = "${thrift.client.hdztaward.s2sname7}")
    private HdztAwardService.Iface proxy7;


    public HdztAwardService.Iface getProxy(String key) {
        return switch (key) {
            case "s2s_2" -> proxy2;
            case "s2s_3" -> proxy3;
            case "s2s_4" -> proxy4;
            case "s2s_5" -> proxy5;
            case "s2s_6" -> proxy6;
            case "s2s_7" -> proxy7;
            default -> proxy;
        };
    }

    public String chooseMapKey(long actId) {
        int group = ActGroupHelper.getActGroup(actId);
        return switch (group) {
            case 2 -> "s2s_2";
            case 3 -> "s2s_3";
            case 4 -> "s2s_4";
            case 5 -> "s2s_5";
            case 6 -> "s2s_6";
            case 7 -> "s2s_7";
            default -> "s2s_1";
        };
    }

    public BatchWelfareResult doWelfare(long actId, String time, long busiId, long uid, long taskId, int count, long packageId, String seq, long retry) {
        while (retry-- > 0) {
            try {
                BatchWelfareResult result = this.doWelfare(actId, time, busiId, uid, taskId, count, packageId, seq);
                if (result != null && result.getCode() == 0) {
                    log.info("doBatchWelfare with retry ok, seq:{}", seq);
                    return result;
                } else {
                    log.error("doWelfare error,time:{},busiId:{},uid:{},taskId:{},count:{},packageId:{},seq:{},retry:{},rsp:{}", time, busiId, uid, taskId, count, packageId, seq, retry, result);
                }
                if (retry > 0) {
                    Thread.sleep(300);
                }
            } catch (Exception e) {
                log.warn("doBatchWelfare exception:" + e, e);
            }
        }
        return null;
    }

    public BatchWelfareResult doWelfare(long actId, String time, long busiId, long uid, long taskId, int count, long packageId, String seq) {
        try {
            BatchWelfareRequest request = new BatchWelfareRequest();
            request.setBusiId(busiId);
            request.setUid(uid);
            request.setTaskId(taskId);
            request.setSeq(seq);
            Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
            Map<Long, Integer> packageIdCount = Maps.newHashMap();
            packageIdCount.put(packageId, count);
            taskPackageIds.put(taskId, packageIdCount);
            request.setTaskPackageIds(taskPackageIds);

            request.setIp("0");
            request.setTimestamp(time);
            Map<String, String> extMap = new HashMap<>();
            extMap.put("test", "test");
            request.setExtData(extMap);

            BatchWelfareResult ret = getProxy(chooseMapKey(actId)).batchWelfare(request);
            log.info("doWelfare busi:{} uid:{} task:{} count:{} packageId:{} seq:{} ret:{} ", busiId, uid, taskId, count, packageId, seq, ret);
            return ret;
        } catch (Exception e) {
            log.error("doWelfare busi:{} uid:{} task:{} count:{} packageId:{} seq:{} err:{}", busiId, uid, taskId, count, packageId, seq, e.getMessage(), e);
            return null;
        }
    }


}
