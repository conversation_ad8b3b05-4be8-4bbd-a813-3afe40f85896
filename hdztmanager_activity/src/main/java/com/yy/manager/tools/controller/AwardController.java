package com.yy.manager.tools.controller;


import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.manager.tools.service.GeActivityHttpService;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("/award")
public class AwardController {

    private Logger logger = LoggerFactory.getLogger(com.yy.manager.tools.controller.AwardController.class);

    @Autowired
    private GeActivityHttpService geActivityHttpService;

    @ApiOperation("发放奖项")
    @Log(title = "发放奖项", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('activity:award:rel','prod')")
    @RequestMapping(value = "/verify", method = RequestMethod.GET)
    @ResponseBody
    public Response awardVerify(HttpServletRequest request,
                                @RequestParam(value = "actId")  Long actId, Long packageId, Long itemId, Long uid) {
        return geActivityHttpService.releasePackageItem(request, actId, packageId, itemId, uid);
    }

    @ApiOperation("奖池验证")
    @Log(title = "奖池验证", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('activity:award:rel','prod')")
    @RequestMapping(value = "/taskVerify", method = RequestMethod.GET)
    @ResponseBody
    public Response taskVerify(HttpServletRequest request,
                                @RequestParam(value = "actId")  Long actId, Long taskId, Long uid) {
        return geActivityHttpService.releaseTaskItem(request, actId, taskId, uid);
    }
}
