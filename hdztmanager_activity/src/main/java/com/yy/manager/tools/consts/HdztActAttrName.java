package com.yy.manager.tools.consts;

import java.util.Arrays;
import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-09-28 14:41
 **/
public class HdztActAttrName {


   public static final List<String> ATTR_APPEND_FIELD = Arrays.asList(HdztActAttrName.FIELD_PRODUCTOWNER, HdztActAttrName.FIELD_TECHNICAL);
   /**
    * 技术负责人
    */
   public static final String FIELD_TECHNICAL = "technical";

   /**
    * 产品负责人
    */
   public static final String FIELD_PRODUCTOWNER = "productOwner";

   public static final String OFFLINE_PERIOD_DAYS = "act_offline_period_days";
}
