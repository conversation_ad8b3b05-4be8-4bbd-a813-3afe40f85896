package com.yy.manager.tools.controller;

import com.yy.manager.tools.service.GeExtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/26
 * @功能说明
 * @版本更新列表
 */
@Controller
@RequestMapping("/GeExt")
public class GeExtController {

    @Autowired
    GeExtService geExtService;

    private static Logger log = LoggerFactory.getLogger(com.yy.manager.tools.controller.GeExtController.class);

    @RequestMapping("/setSettleStatus.do")
    @ResponseBody
    public Map<String, String> setSettleStatus(Long actId){
        geExtService.setSettleStatus(actId);
        log.info("set act:{}:settle_status", actId);
        Map<String, String> res = new HashMap<>(1);
        res.put("result","success");
        return res;
    }

    @RequestMapping("/delSettleStatus.do")
    @ResponseBody
    public Map<String, String> delSettleStatus(Long actId){
        geExtService.delSettleStatus(actId);
        log.info("del act:{}:settle_status", actId);
        Map<String, String> res = new HashMap<>(1);
        res.put("result","success");
        return res;
    }

}
