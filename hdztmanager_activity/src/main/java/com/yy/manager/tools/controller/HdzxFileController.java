package com.yy.manager.tools.controller;

import com.google.common.collect.ImmutableMap;
import com.yy.manager.utils.Convert;
import com.yy.manager.utils.DateUtil;
import com.yy.manager.utils.Response;
import com.yy.manager.utils.bce.BceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.Map;

/**
 * 活动中心文件上传
 *
 * <AUTHOR>
 * @date 2018年5月4日 下午4:34:32
 */
@Slf4j
@RestController
@RequestMapping("/hdzxfile")
public class HdzxFileController {

    private static final int MAX_FILE_SIZE = 16 * 1024 * 1024;

    @Autowired
    private BceService bceService;

    @PostMapping("uploadFiles")
    public Response<Map<String, String>> upload(@RequestParam("file") MultipartFile file,
                                                @RequestParam(required = false) String name,
                                                @RequestParam(required = false, defaultValue = "1") Integer addTimeTag) {

        String fileName = StringUtils.trim(name);
        if (StringUtils.isBlank(fileName)) {
            fileName = getFileNameNoEx(file.getOriginalFilename());
        }
        String suffix = getExtensionName(file.getOriginalFilename());
        fileName += ("." + suffix);
        try {
            checkFiles(file);
            boolean timeTag = Convert.toInt(addTimeTag) == 1;
            fileName = addTimestamp4Filename(fileName, timeTag);
            if (bceService.isExists(fileName)) {
                return Response.fail(500, "文件已经存在");
            }
            String url = bceService.uploadMinFileData(file.getBytes(), fileName);

            return Response.success(ImmutableMap.of("downloadUrl", url));

        } catch (Exception e) {

            log.error("upload error@fileName:{} {}", file, e.getMessage(), e);
            return Response.fail(500, "系统异常");
        }


    }

    /**
     * 为文件名增加时间戳（yyyyMMddHHmmss）（文件后缀会保留）
     *
     * @param filename
     * @param add_timestamp
     * @return
     * <AUTHOR>
     * @date 2018年7月19日 上午10:49:13
     */
    private String addTimestamp4Filename(String filename, boolean add_timestamp) {
        if (!add_timestamp) {
            return filename;
        }

        int inx = filename.lastIndexOf(".");
        String ts = DateUtil.today("yyyyMMddHHmmss");
        if (inx == -1) {
            return filename + "_" + ts;
        } else {
            return filename.substring(0, inx) + "_" + ts + filename.substring(inx);
        }
    }

    /**
     * 对上传文件情况进行检查
     *
     * <AUTHOR>
     * @date 2018年5月29日 下午6:05:07
     */
    private void checkFiles(MultipartHttpServletRequest multiReq, String[] params) throws Exception {
        for (int i = 0; i < params.length; i++) {
            MultipartFile file = multiReq.getFile(params[i]);
            if (file != null && file.getSize() > MAX_FILE_SIZE) {
                throw new Exception("文件大小不能超过16M");
            }
        }
    }

    /**
     * 获取文件扩展名，不带 .
     */
    public static String getExtensionName(String filename) {
        if (StringUtils.isNotBlank(filename)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1).toLowerCase();
            }
        }
        return filename;
    }

    /**
     * Java文件操作 获取不带扩展名的文件名
     */
    public static String getFileNameNoEx(String filename) {
        if (StringUtils.isNotBlank(filename)) {
            int dot = filename.lastIndexOf('.');
            if (dot > -1) {
                return filename.substring(0, dot);
            }
        }
        return filename;
    }

    /**
     * 对上传文件情况进行检查
     *
     * <AUTHOR>
     * @date 2018年5月29日 下午6:05:07
     */
    private void checkFiles(MultipartFile file) throws Exception {
        if (file != null && file.getSize() > MAX_FILE_SIZE) {
            throw new Exception("文件大小不能超过16M");
        }
    }

}
