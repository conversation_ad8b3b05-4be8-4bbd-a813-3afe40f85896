package com.yy.manager.acttemplate.controller;

import com.yy.manager.acttemplate.service.ActSkinService;
import com.yy.manager.acttemplate.vo.SaveSkinResourceVo;
import com.yy.manager.utils.Response;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * desc:皮肤模块
 *
 * <AUTHOR>
 * @date 2024-04-02 17:13
 **/
@Slf4j
@RestController
@RequestMapping("/activity/template")
public class ActSkinController {

    @Autowired
    private ActSkinService actSkinService;

    /**
     * 换肤
     */
    @PostMapping("/saveSkinResource")
    public Response<String> saveSkinResource(@RequestBody SaveSkinResourceVo param) {

        actSkinService.saveActSkin(param);
        return Response.ok();
    }
}
