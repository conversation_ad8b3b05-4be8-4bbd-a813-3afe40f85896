package com.yy.manager.acttemplate.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/16 11:28
 **/
public class PackageValueChecker {
    public static boolean isIntegerType(String typeName) {
        return Integer.class.getName().equals(typeName) || Long.class.getName().equals(typeName) || Number.class.getName().equals(typeName);
    }

    public static boolean isDoubleType(String typeName) {
        return Double.class.getName().equals(typeName);
    }

    public static void checkEmptyField(JSONArray array, String fieldName) {
//        for (int index = 0; index < array.size(); index++) {
//            JSONObject jsonObject = array.getJSONObject(index);
//            for (String key : jsonObject.keySet()) {
//                if (StringUtils.isEmpty(jsonObject.getString(key).trim())) {
//                    throw new SuperException(fieldName + " 存在空白输入,请检查");
//                }
//            }
//        }
    }

    public static JSONArray removeEmpty(Object data) {
        JSONArray array = JSON.parseArray(JSON.toJSONString(data));
        List<JSONObject> emptyRow = new ArrayList<>();
        for (int index = 0; index < array.size(); index++) {
            JSONObject jsonObject = array.getJSONObject(index);
            // 这是前端用于控制的额外字段,需要移除
            jsonObject.remove("isSet");

            boolean notEmpty = false;
            for (String key : jsonObject.keySet()) {
                if (StringUtils.isNotEmpty(jsonObject.getString(key).trim())) {
                    notEmpty = true;
                    break;
                }
            }
            if (notEmpty) {
                continue;
            }
            emptyRow.add(jsonObject);
        }

        if (!emptyRow.isEmpty()) {
            emptyRow.forEach(array::remove);
        }

        return array;
    }
}
