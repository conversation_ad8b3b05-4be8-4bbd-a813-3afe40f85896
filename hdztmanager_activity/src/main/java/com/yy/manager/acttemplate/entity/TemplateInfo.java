package com.yy.manager.acttemplate.entity;


import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TemplateInfo {
    protected String id;

    protected String name;

    protected String baseActId;

    protected Integer state;

    protected String remark;

    protected String extJson;

    protected String baseConfig;

    protected Date createTime;

    protected Date modifyTime;
}