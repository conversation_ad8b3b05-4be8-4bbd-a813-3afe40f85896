package com.yy.manager.acttemplate.service;

import com.alibaba.fastjson.JSON;

import com.yy.common.utils.StringUtils;
import com.yy.manager.acttemplate.entity.TemplateActInfo;
import com.yy.manager.acttemplate.entity.TemplateInfo;
import com.yy.manager.acttemplate.mapper.TemplateActInfoMapper;
import com.yy.manager.acttemplate.mapper.TemplateInfoMapper;
import com.yy.manager.acttemplate.vo.TemplateActInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.Date;
import java.util.List;



@Slf4j
@Service
public class ActivityTemplateService {


    @Resource
    private TemplateInfoMapper templateInfoMapper;

    @Resource
    private TemplateActInfoMapper templateActInfoMapper;


    public List<TemplateInfo> getTemplateList() {

        List<TemplateInfo> list = templateInfoMapper.selectList(null);
        return list;

    }

    public List<TemplateActInfo> getQuickActList() {
        List<TemplateActInfo> list = templateActInfoMapper.selectList(null);
        list.stream().forEach(v->{
            v.setPhaseTimeConfig(null);
            v.setActGiftInfo(null);
        });

        return list;

    }



    public boolean quickAddAct(TemplateActInfoVo param ) {

        if(StringUtils.isEmpty(param.getActId())){
            return false;
        }
        TemplateActInfo oldRecord = templateActInfoMapper.selectByPrimaryKey(param.getActId());
        if(oldRecord!=null){
            return false;
        }

        TemplateActInfo record = new TemplateActInfo();

        BeanUtils.copyProperties(param,record);
        record.setPhaseTimeConfig(JSON.toJSONString(param.getPhaseTimeInfo()));
        record.setActGiftInfo(JSON.toJSONString(param.getActGiftVo()));
        record.setCtime(new Date());
        record.setUtime(new Date());
        templateActInfoMapper.insert(record);

        return true;
    }


    public boolean quickUpdateAct(TemplateActInfoVo param) {
        if(StringUtils.isEmpty(param.getActId())){
            return false;
        }
        TemplateActInfo oldRecord = templateActInfoMapper.selectByPrimaryKey(param.getActId());
        if(oldRecord==null){
            return false;
        }

        TemplateActInfo record = new TemplateActInfo();
        BeanUtils.copyProperties(param,record);
        record.setPhaseTimeConfig(JSON.toJSONString(param.getPhaseTimeInfo()));
        record.setActGiftInfo(JSON.toJSONString(param.getActGiftVo()));
        record.setUtime(new Date());
        templateActInfoMapper.updateByPrimaryKeySelective(record);
        return true;
    }



}
