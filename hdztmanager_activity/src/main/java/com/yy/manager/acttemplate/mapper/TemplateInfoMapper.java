package com.yy.manager.acttemplate.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.acttemplate.entity.TemplateInfo;

import java.util.List;

@DS("hdpttemplate")
public interface TemplateInfoMapper extends BaseMapper<TemplateInfo> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table template_info
     *
     * @mbg.generated Fri Mar 15 16:23:26 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table template_info
     *
     * @mbg.generated Fri Mar 15 16:23:26 CST 2024
     */
    TemplateInfo selectByPrimaryKey(String id);
}