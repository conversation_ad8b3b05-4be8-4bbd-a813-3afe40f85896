package com.yy.manager.wzry.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yy.manager.wzry.entity.WzryGameTeam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@DS("hdzk7")
public interface WzryGameTeamMapper {

    List<Long> queryUserGames(@Param("uid") long uid, @Param("startTime")Date startTime, @Param("endTime") Date endTime);

    List<Long> queryUserHistoryGames(@Param("uid") long uid, @Param("startTime")Date startTime, @Param("endTime") Date endTime);

    List<WzryGameTeam> getGameTeamsByGameIds(@Param("gameIds")Collection<Long> gameIds);
}
