package com.yy.manager.wzry.service;

import cn.hutool.core.lang.id.NanoId;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.manager.thrift.gameecology_bridge.GameecologyBridgeService;
import com.yy.manager.thrift.gameecology_bridge.StringRequest;
import com.yy.manager.thrift.gameecology_bridge.StringResponse;
import com.yy.manager.wzry.entity.WzryGame;
import com.yy.manager.wzry.entity.WzryGameTeam;
import com.yy.manager.wzry.mapper.WzryGameMapper;
import com.yy.manager.wzry.mapper.WzryGameTeamMapper;
import com.yy.manager.wzry.vo.GameResult;
import com.yy.manager.wzry.vo.WzryGameTeamVo;
import com.yy.manager.wzry.vo.WzryGameVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class WzryGameService {

    @Resource
    private WzryGameMapper wzryGameMapper;

    @Resource
    private WzryGameTeamMapper wzryGameTeamMapper;

    @Value("${bridge-thrift.hdzk.encrypt-key}")
    private String encryptKey;

    @Reference(protocol = "attach_nythrift", registry = "yrpc-reg", owner = "${bridge-thrift.hdzk7.s2s-name}")
    private GameecologyBridgeService.Iface proxy;

    public Map<String, GameResult> batchGetGameResult(long actId, List<String> childIds) {
        StringRequest request = new StringRequest();
        request.setSeq(NanoId.randomNanoId());
        request.setRid(1001);
        request.setFid(1003);
        request.setActId(actId);
        request.setData(JSON.toJSONString(childIds));
        request.setIp(StringUtils.EMPTY);
        request.setTs(System.currentTimeMillis());
        request.setSource(5000);
        request.setExtjson(StringUtils.EMPTY);
        String src = src(request);
        String sha256 = DigestUtils.sha256Hex(encryptKey + '|' + src);
        request.setSha256(sha256);

        try {
            StringResponse response = proxy.read(request);
            if (response.getResult() == 0) {
                return JSON.parseObject(response.data, new TypeReference<HashMap<String, GameResult>>(){});
            }
        } catch (Exception e) {
            log.error("batchGetGameResult exception:", e);
        }

        return Collections.emptyMap();
    }

    public List<WzryGameVo> queryUserGames(long actId, Long uid, Long gameId, Integer gameState, Date startTime, Date endTime) {
        if (gameId != null && gameId > 0) {
            return getWzryGameVoByGameIds(actId, Collections.singleton(gameId), gameState);
        }

        List<Long> gameIds1 = wzryGameTeamMapper.queryUserGames(uid, startTime, endTime);
        List<Long> gameIds2 = wzryGameTeamMapper.queryUserHistoryGames(uid, startTime, endTime);
        if (CollectionUtils.isEmpty(gameIds1) && CollectionUtils.isEmpty(gameIds2)) {
            return Collections.emptyList();
        }

        Set<Long> gameIds = new HashSet<>(gameIds1.size() + gameIds2.size());
        gameIds.addAll(gameIds1);
        gameIds.addAll(gameIds2);

        return getWzryGameVoByGameIds(actId, gameIds, gameState);
    }

    private List<WzryGameVo> getWzryGameVoByGameIds(long actId, Collection<Long> gameIds, Integer gameState) {
        QueryWrapper<WzryGame> wrapper = new QueryWrapper<>();
        wrapper.eq("act_id", actId);
        wrapper.in("id", gameIds);
        if (gameState != null) {
            wrapper.eq("state", gameState);
        }

        List<WzryGame> games = wzryGameMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(games)) {
            return Collections.emptyList();
        }

        List<String> childIds = new ArrayList<>(games.size());
        List<Long> ids = new ArrayList<>(games.size());

        for (WzryGame game : games) {
            if (StringUtils.isNotEmpty(game.getChildid())) {
                childIds.add(game.getChildid());
            }
            ids.add(game.getId());
        }

        Map<String, GameResult> gameResults = batchGetGameResult(actId, childIds);

        List<WzryGameTeam> gameTeams = wzryGameTeamMapper.getGameTeamsByGameIds(ids);
        Map<Long, List<WzryGameTeamVo>> teamMap = new HashMap<>(games.size());
        for (WzryGameTeam gameTeam : gameTeams) {
            teamMap.compute(gameTeam.getGameId(), (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>(10);
                }

                WzryGameTeamVo teamVo = new WzryGameTeamVo(gameTeam);
                v.add(teamVo);

                return v;
            });
        }

        List<WzryGameVo> result = new ArrayList<>(games.size());
        for (WzryGame game : games) {
            WzryGameVo gameVo = new WzryGameVo();
            gameVo.setGameId(game.getId());
            gameVo.setChildId(game.getChildid());
            gameVo.setBattleMode(game.getBattleMode());
            gameVo.setGameState(game.getState());
            gameVo.setGameStartTime(game.getStartTime());
            List<WzryGameTeamVo> teams = teamMap.get(game.getId());
            gameVo.setGameTeams(teams);
            if (CollectionUtils.isNotEmpty(teams)) {
                GameResult gameResult = gameResults.get(game.getChildid());
                if (gameResult != null) {
                    for (WzryGameTeamVo team : teams) {
                        team.setSaibaoInGame(gameResult.getInGameSeat().contains(team.getSeatId() + "_" + team.getLimitTeam()));
                        if (gameResult.getWinnerTeam() != null) {
                            team.setSaibaoWinTeam(gameResult.getWinnerTeam());
                        }
                    }
                }
            }


            result.add(gameVo);
        }

        return result;
    }

    public static String src(StringRequest request) {
        return String.format("%s|%s|%s|%s|%s|%s|%s|%s", request.getSeq(), request.getRid(), request.getFid(),
                request.getData(), request.getIp(), request.getTs(), request.getSource(), request.getExtjson());
    }
}
