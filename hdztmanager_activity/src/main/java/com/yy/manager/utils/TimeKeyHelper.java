package com.yy.manager.utils;


import com.yy.manager.hdzt.entity.RankingConfig;
import com.yy.manager.hdzt.entity.RankingPhase;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 时间分榜集中处理类
 */
public class TimeKeyHelper {

    // 时间分榜: 0-不按时间再分, 1-按日再分，2-按小时再分, 3-按周分，4-按月分, 5-按季，6-按年
    public static final long TIME_KEY_NO = 0;
    public static final long TIME_KEY_BY_DAY = 1;
    public static final long TIME_KEY_BY_HOUR = 2;
    public static final long TIME_KEY_BY_WEEK = 3;
    public static final long TIME_KEY_BY_MONTH = 4;
    public static final long TIME_KEY_BY_SEASON = 5;
    public static final long TIME_KEY_BY_YEAR = 6;

    /**
     * 获取时间分榜子Key
     */
    public static String getTimeSubKey(long timeKey, Date date) {
        String timeCode = getTimeCode(timeKey, date);
        return StringUtils.isBlank(timeCode) ? "ALL" : timeCode;
    }

    /**
     * 获取分时相关的时间编码（yyyyMMdd 或者 yyyyMMddHH）
     */
    public static String getTimeCode(long timeKey, Date date) {
        String timeCode = "";
        if (isSplitByDay(timeKey)) {
            timeCode = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        } else if(isSplitByHour(timeKey)) {
            timeCode = DateUtil.format(date, DateUtil.PATTERN_TYPE7);
        } else if(isSplitByWeek(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getWeekFirstSecond(date), DateUtil.PATTERN_TYPE2);
        } else if(isSplitByMonth(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getMonthFirstSecond(date), DateUtil.PATTERN_TYPE2);
        } else if(isSplitBySeason(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getSeasonFirstSecond(date), DateUtil.PATTERN_TYPE2);
        } else if(isSplitByYear(timeKey)) {
            timeCode = DateUtil.format(DateUtil.getYearFirstSecond(date), DateUtil.PATTERN_TYPE2);
        }
        return StringUtils.trim(timeCode);
    }

    /**
     * 获取当前分时边界时间对象
     */
    public static Date getCurrTimeValue(boolean bBegin, long timeKeyFlag, Date now) {
        Date date = null;
        if(isSplitByHour(timeKeyFlag)) {
            date = bBegin ? DateUtil.getHourFirstSecond(now) : DateUtil.getHourLastSecond(now);
        } else if(isSplitByDay(timeKeyFlag)) {
            date = bBegin ? DateUtil.getDayFirstSecond(now) : DateUtil.getDayLastSecond(now);
        } else if(isSplitByWeek(timeKeyFlag)) {
            date = bBegin ? DateUtil.getWeekFirstSecond(now) : DateUtil.getWeekLastSecond(now);
        } else if(isSplitByMonth(timeKeyFlag)) {
            date = bBegin ? DateUtil.getMonthFirstSecond(now) : DateUtil.getMonthLastSecond(now);
        } else if(isSplitBySeason(timeKeyFlag)) {
            date = bBegin ? DateUtil.getSeasonFirstSecond(now) : DateUtil.getSeasonLastSecond(now);
        } else if(isSplitByYear(timeKeyFlag)) {
            date = bBegin ? DateUtil.getYearFirstSecond(now) : DateUtil.getYearLastSecond(now);
        }
        return date;
    }

    /**
     * 获取上个分时周期边界时间对象
     */
    public static Date getPrevTimeValue(boolean bBegin, long timeKeyFlag, Date now) {
        Date date = null;
        if(isSplitByHour(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevHourFirstSecond(now) : DateUtil.getPrevHourLastSecond(now);
        } else if(isSplitByDay(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevDayFirstSecond(now) : DateUtil.getPrevDayLastSecond(now);
        } else if(isSplitByWeek(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevWeekFirstSecond(now) : DateUtil.getPrevWeekLastSecond(now);
        } else if(isSplitByMonth(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevMonthFirstSecond(now) : DateUtil.getPrevMonthLastSecond(now);
        } else if(isSplitBySeason(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevSeasonFirstSecond(now) : DateUtil.getPrevSeasonLastSecond(now);
        } else if(isSplitByYear(timeKeyFlag)) {
            date = bBegin ? DateUtil.getPrevYearFirstSecond(now) : DateUtil.getPrevYearLastSecond(now);
        }
        return date;
    }

    /**
     * 获取下个分时周期边界时间对象
     */
    public static Date getNextTimeValue(boolean bBegin, long timeKeyFlag, Date now) {
        Date date = null;
        if(isSplitByHour(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextHourFirstSecond(now) : DateUtil.getNextHourLastSecond(now);
        } else if(isSplitByDay(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextDayFirstSecond(now) : DateUtil.getNextDayLastSecond(now);
        } else if(isSplitByWeek(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextWeekFirstSecond(now) : DateUtil.getNextWeekLastSecond(now);
        } else if(isSplitByMonth(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextMonthFirstSecond(now) : DateUtil.getNextMonthLastSecond(now);
        } else if(isSplitBySeason(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextSeasonFirstSecond(now) : DateUtil.getNextSeasonLastSecond(now);
        } else if(isSplitByYear(timeKeyFlag)) {
            date = bBegin ? DateUtil.getNextYearFirstSecond(now) : DateUtil.getNextYearLastSecond(now);
        }
        return date;
    }

    /**
     * 选取合适的时间：开始取晚者，结束取早者
     */
    public static String adjustTime(String flagTime, boolean bBegin, String time) {
        if (bBegin) {
            if (time.compareTo(flagTime) > 0) {
                flagTime = time;
            }
        } else {
            if (time.compareTo(flagTime) < 0) {
                flagTime = time;
            }
        }
        return flagTime;
    }
    /**
     * 计算开始时间：取更晚者
     */
    public static Date getCalcBeginTime(RankingConfig ranking, RankingPhase phase) {
        Date calcBeginTime = ranking.getCalcBeginTime();
        if(phase != null) {
            if(phase.getBeginTime().after(calcBeginTime)) {
                calcBeginTime = phase.getBeginTime();
            }
        }
        return calcBeginTime;
    }

    /**
     * 判断是否同一天
     */
    public static boolean isSameDay(Date calcTime, Date now) {
        return DateUtil.format(calcTime, DateUtil.PATTERN_TYPE2).equals(DateUtil.format(now, DateUtil.PATTERN_TYPE2));
    }

    /**
     * 判断是否同一小时
     */
    public static boolean isSameHour(Date calcTime, Date now) {
        return DateUtil.format(calcTime, DateUtil.PATTERN_TYPE7).equals(DateUtil.format(now, DateUtil.PATTERN_TYPE7));
    }

    public static boolean isSplitByDay(long timeKey) {
        return timeKey == TIME_KEY_BY_DAY;
    }

    public static boolean isSplitByHour(long timeKey) {
        return timeKey == TIME_KEY_BY_HOUR;
    }

    public static boolean isSplitByWeek(long timeKey) {
        return timeKey == TIME_KEY_BY_WEEK;
    }

    public static boolean isSplitByMonth(long timeKey) {
        return timeKey == TIME_KEY_BY_MONTH;
    }

    public static boolean isSplitBySeason(long timeKey) {
        return timeKey == TIME_KEY_BY_SEASON;
    }

    public static boolean isSplitByYear(long timeKey) {
        return timeKey == TIME_KEY_BY_YEAR;
    }

    /**
     * 判断非时间分榜标记
     */
    public static boolean notTimeSplit(long timeKey) {
        return !isTimeSplit(timeKey);
    }

    /**
     * 判断是时间分榜标记
     */
    public static boolean isTimeSplit(long timeKey) {
        return timeKey == TIME_KEY_BY_HOUR
                || timeKey == TIME_KEY_BY_DAY
                || timeKey == TIME_KEY_BY_WEEK
                || timeKey == TIME_KEY_BY_MONTH
                || timeKey == TIME_KEY_BY_SEASON
                || timeKey == TIME_KEY_BY_YEAR;
    }

}
