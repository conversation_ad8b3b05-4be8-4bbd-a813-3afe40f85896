package com.yy.manager.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.MybatisParameterHandler;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.ParameterMode;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.SqlSessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.text.DateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.mybatis.spring.SqlSessionUtils.closeSqlSession;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2021/12/1 19:34
 * @Modified:
 */
@Component
public class MySqlHelper {

    private static SqlSessionFactory sqlSessionFactory;

    private static final Map<Class<?>, List<Method>> CLASS_METHOD_CACHE = Maps.newConcurrentMap();

    @Autowired
    public void setSqlSessionFactory(SqlSessionFactory sqlSessionFactory) {
        if (MySqlHelper.sqlSessionFactory == null) {
            MySqlHelper.sqlSessionFactory = sqlSessionFactory;
        }
    }

    private final static Map<Class<?>, List<Class<?>>> BASE_CLASS_CHANGE_MAP = new ImmutableMap.Builder<Class<?>, List<Class<?>>>()
            .put(boolean.class, Lists.newArrayList(Boolean.class))
            .put(byte.class, Lists.newArrayList(Byte.class))
            .put(char.class, Lists.newArrayList(Character.class))
            .put(double.class, Lists.newArrayList(Double.class, Float.class, Long.class, Integer.class, Short.class))
            .put(float.class, Lists.newArrayList(Float.class, Integer.class, Short.class))
            .put(int.class, Lists.newArrayList(Integer.class, Short.class))
            .put(long.class, Lists.newArrayList(Long.class, Integer.class, Short.class))
            .put(short.class, Lists.newArrayList(Short.class))
            .build();

    public static String getSql(Class<?> clazz, String methodNam, Object... parameter) {
        SqlSession sqlSession = null;
        try {
            sqlSession = SqlSessionUtils.getSqlSession(sqlSessionFactory);
            Configuration configuration = sqlSession.getConfiguration();


            List<Method> methods = CLASS_METHOD_CACHE.computeIfAbsent(clazz, (k) -> ImmutableList.copyOf(k.getDeclaredMethods()));
            methods = methods.stream().filter(method -> method.getName().equals(methodNam))
                    .collect(Collectors.toList());

            Class<?>[] parameterTypes = Stream.of(parameter).map(Object::getClass).toArray(Class<?>[]::new);
            Optional<Method> method = methods.stream().filter(m -> checkParamType(m.getParameterTypes(), parameterTypes)).findFirst();
            if (!method.isPresent()) {
                throw new RuntimeException("not find method," + clazz.getName() + "." + methodNam + argumentTypesToString(parameterTypes));
            }

            MapperMethod.MethodSignature methodSignature = new MapperMethod.MethodSignature(configuration, clazz, method.get());
            Object arg = methodSignature.convertArgsToSqlCommandParam(parameter);

            String mappedStatementId = clazz.getTypeName() + "." + methodNam;
            MappedStatement ms = configuration.getMappedStatement(mappedStatementId);
            BoundSql boundSql = ms.getBoundSql(arg);
            MybatisParameterHandler handler = new MybatisParameterHandler(ms, arg, boundSql);
            List<Object> parameters = getParameters(ms, boundSql, handler);
            return buildSql(boundSql.getSql(), parameters);
        } finally {
            if (sqlSession != null) {
                closeSqlSession(sqlSession, sqlSessionFactory);
            }
        }


    }

    /**
     * 构建sql，替换参数
     *
     * @param sql
     * @param params
     * @return
     */
    private static String buildSql(String sql, List<Object> params) {

        //去除不可见字符
        sql = sql.replaceAll("[\\s]+", " ");
        List<String> sqlFragments = Splitter.on("?").splitToList(sql);

        if (sqlFragments.size() != params.size() + 1) {
            throw new RuntimeException("sql参数异常,sql=" + sql + ",param =" + JSON.toJSONString(params));
        }
        Integer paramNum = params.size();
        StringBuilder sqlBuilder = new StringBuilder(sqlFragments.get(0));
        for (int i = 0; i < paramNum; ++i) {
            String parameterValue = getParameterValue(params.get(i));
            sqlBuilder.append(parameterValue).append(sqlFragments.get(i + 1));
        }
        return sqlBuilder.toString();
    }

    /**
     * 获取sql需要的参数，改自 MybatisParameterHandler.setParameters
     *
     * @param ms
     * @param boundSql
     * @param handler
     * @return
     */
    public static List<Object> getParameters(MappedStatement ms, BoundSql boundSql, MybatisParameterHandler handler) {
        Configuration configuration = ms.getConfiguration();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        Object parameterObject = handler.getParameterObject();
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
        List<Object> parameters = Lists.newArrayList();
        if (parameterMappings != null) {
            for (int i = 0; i < parameterMappings.size(); i++) {
                ParameterMapping parameterMapping = parameterMappings.get(i);
                if (parameterMapping.getMode() != ParameterMode.OUT) {
                    Object value;
                    String propertyName = parameterMapping.getProperty();
                    if (boundSql.hasAdditionalParameter(propertyName)) {
                        //如果是动态参数近这里
                        value = boundSql.getAdditionalParameter(propertyName);
                    } else if (parameterObject == null) {
                        //如果参数为空value就为空
                        value = null;
                    } else if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                        value = parameterObject;
                    } else {
                        //普通赋值，MetaObject是参数的元信息，里面有参数值和参数名对应
                        MetaObject metaObject = configuration.newMetaObject(parameterObject);
                        value = metaObject.getValue(propertyName);
                    }
                    parameters.add(value);
                }
            }
        }
        return parameters;
    }


    private static String getParameterValue(Object obj) {
        String value;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            value = "'" + formatter.format(obj) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "null";
            }
        }
        return value;
    }

    private static String argumentTypesToString(Class<?>[] argTypes) {
        StringBuilder buf = new StringBuilder();
        buf.append("(");
        if (argTypes != null) {
            for (int i = 0; i < argTypes.length; i++) {
                if (i > 0) {
                    buf.append(", ");
                }
                Class<?> c = argTypes[i];
                buf.append((c == null) ? "null" : c.getName());
            }
        }
        buf.append(")");
        return buf.toString();
    }

    /**
     * 比对调用方法的参数类型
     *
     * @param a1
     * @param a2
     * @return
     */
    private static boolean checkParamType(Class<?>[] a1, Class<?>[] a2) {
        if (a1 == null) {
            return a2 == null || a2.length == 0;
        }

        if (a2 == null) {
            return a1.length == 0;
        }

        if (a1.length != a2.length) {
            return false;
        }


        for (int i = 0; i < a1.length; i++) {
            //基本类型
            List<Class<?>> convertibleList = BASE_CLASS_CHANGE_MAP.get(a1[i]);
            if (CollectionUtils.isNotEmpty(convertibleList)) {
                if (!convertibleList.contains(a2[i])) {
                    return false;
                }
                continue;
            }
            if (a1[i] != a2[i] && !a1[i].isAssignableFrom(a2[i])) {
                return false;
            }

        }

        return true;
    }

}
