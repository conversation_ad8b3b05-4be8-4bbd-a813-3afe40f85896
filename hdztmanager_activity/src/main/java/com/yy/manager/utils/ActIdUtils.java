package com.yy.manager.utils;

/**
 * <AUTHOR>
 * @since 2025/5/26 16:43
 */
public class ActIdUtils {

    public static int getActGroup(Long actId) {
        return Math.toIntExact(actId % 10000) / 1000;
    }

    public static Integer getSvcAppIDByActID(Long actId) {
        return getSvcAppID(getActGroup(actId), SysEnvHelper.isDeploy());
    }

    public static Integer getSvcAppIDByActID(Long actId, boolean isDeploy) {
        return getSvcAppID(getActGroup(actId), isDeploy);
    }

    public static Integer getSvcAppID(int actGroup) {
        return getSvcAppID(actGroup, SysEnvHelper.isDeploy());
    }

    public static Integer getSvcAppID(int actGroup, boolean isDeploy) {
        return switch (actGroup) {
            case 1 -> isDeploy ? 10739 : 60425;
            case 2 -> isDeploy ? 15447 : 62446;
            case 3 -> isDeploy ? 15448 : 62447;
            case 4 -> isDeploy ? 15449 : 62448;
            case 5 -> isDeploy ? 15551 : 60124;
            case 6 -> isDeploy ? 15450 : 60440;
            case 7 -> isDeploy ? 15451 : 60448;
            default -> null;
        };
    }
}
