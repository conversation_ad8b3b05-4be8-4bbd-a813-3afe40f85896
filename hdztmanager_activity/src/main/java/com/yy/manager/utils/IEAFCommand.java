package com.yy.manager.utils;


/**
 * @功能说明
 * <pre>
 * 请求参数封装类
 * </pre>
 */
public class IEAFCommand {

	/** 更新的目标值 */
	private String to;

	/** 操作中所需要的  me 条件或 插入值 */
	private String me;

	/** 操作中所需要的 child 条件 */
	private String child;

	/** 操作中所需要的 grandchild 条件 */
	private String grandchild;

	/** 分页查询信息 */
	private String pageInfo;

	private String extraOrderColumns;

	private String extraCondition;

	private String extraSets;

	public String getTo() {
		return to;
	}

	public void setTo(String to) {
		this.to = to;
	}

	public String getMe() {
		return me;
	}

	public void setMe(String me) {
		this.me = me;
	}

	public String getChild() {
		return child;
	}

	public void setChild(String child) {
		this.child = child;
	}

	public String getGrandchild() {
		return grandchild;
	}

	public void setGrandchild(String grandchild) {
		this.grandchild = grandchild;
	}

	public String getPageInfo() {
		return pageInfo;
	}

	public void setPageInfo(String pageInfo) {
		this.pageInfo = pageInfo;
	}

	public String getExtraOrderColumns() {
		return extraOrderColumns;
	}

	public void setExtraOrderColumns(String extraOrderColumns) {
		this.extraOrderColumns = extraOrderColumns;
	}

	public String getExtraCondition() {
		return extraCondition;
	}

	public void setExtraCondition(String extraCondition) {
		this.extraCondition = extraCondition;
	}

	public String getExtraSets() {
		return extraSets;
	}

	public void setExtraSets(String extraSets) {
		this.extraSets = extraSets;
	}
}
