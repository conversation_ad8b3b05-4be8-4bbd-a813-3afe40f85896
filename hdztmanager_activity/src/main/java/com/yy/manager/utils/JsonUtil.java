/*
 * @(#)JsonUtil.java 2011-10-13
 *
 * Copy Right@ 广东云石科技有限公司
 */
package com.yy.manager.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 提供和 json 相关的操作
 * @Author: 郭利平
 * @Desciption: http请求切面
 * @Date: 1.0.0 / 2011-10-13
 * @Modified:
 * 这个类即将废弃，使用com.yy.manager.utils.Response 或者 com.yy.common.core.domain.AjaxResult  返回
 */
public class JsonUtil {
    /*
     * 以类方法的方式实现一些单一、独立的功能
     */

    public static final String ATTR_NAME_RESULT = "result";
    public static final String ATTR_NAME_DATA = "data";
    public static final String ATTR_NAME_OTHER = "other";
    public static final String RESULT_SUCCESS = "success";
    public static final String RESULT_FAIL = "fail";
    public static final String RESULT_UNKNOWN = "unknown";
    public static final String DEFAULT_DATE_TPL = "yyyy-MM-dd";
    public static final String DEFAULT_DATETIME_TPL = "yyyy-MM-dd HH:mm:ss";
    public static final String JAVA_UTIL_DATE_NAME = Date.class.getCanonicalName();

    private static final Log logger = LogFactory.getLog(com.yy.manager.utils.JsonUtil.class);

    private static final String[] DATE_FORMATS = new String[]{DEFAULT_DATETIME_TPL, DEFAULT_DATE_TPL};

    /**
     * 返回json数据结构的结果，实践表明，只要3个字段就可清晰描述返回的结果
     *
     * @param result - 结果是成功还是失败，或者未知
     * @param data   - 结果的数据
     * @param other  - 其他数据
     * @return
     */
    public static String makeJsonResult(String result, Object data, Object other) {
        data = sqlInjectFilter(data);
        Map<String, Object> map = new HashMap<String, Object>(3);
        map.put(ATTR_NAME_RESULT, result);
        map.put(ATTR_NAME_DATA, data);
        map.put(ATTR_NAME_OTHER, other);
        return JSON.toJSONString(map);
    }


    public static String makeDefaultSuccess() {
        Map<String, Object> map = new HashMap<>(3);
        map.put(ATTR_NAME_RESULT, "0");
        map.put(ATTR_NAME_DATA, "操作成功");
        map.put(ATTR_NAME_OTHER, null);
        return JSON.toJSONString(map);
    }

    /**
     * 返回json数据结构的结果，实践表明，只要3个字段就可清晰描述返回的结果
     *
     * @param result - 结果是成功还是失败，或者未知
     * @param data   - 结果的数据
     * @return
     */
    public static String makeJsonResultWithCallback(String result, Object data, String callback) {
        Map<String, Object> map = new HashMap<String, Object>(2);
        map.put(ATTR_NAME_RESULT, result);
        map.put(ATTR_NAME_DATA, data);
        if (callback != null) {
            return callback + "(" + JSON.toJSONString(data) + ")";
        }
        return JSON.toJSONString(map);
    }

    /**
     * IBM AppSacn 安全扫描到 SQLException 异常消息会报警，故滤之
     *
     * @param value
     * @return
     */
    public static Object sqlInjectFilter(Object value) {
        String content = value == null ? "" : String.valueOf(value).trim();
        boolean b = content.contains("SQLException");
        return b ? "数据库操作异常！" : value;
    }


    /**
     * 获取JSONObject中特定名字的键值对
     *
     * @param jo
     * @param name
     * @param whole
     * @param start
     * @return
     */
    public static Map<String, String> getEntriesByName(JSONObject jo, String name, boolean whole, boolean start) {
        Map<String, String> map = new LinkedHashMap<String, String>();
        boolean flag = name == null || name.trim().length() == 0;
        for (Iterator<?> itr = jo.keySet().iterator(); itr.hasNext(); ) {
            String key = (String) itr.next();
            if (flag) {
                map.put(key, jo.getString(key));
                continue;
            }

            if (whole) {
                if (!key.equals(name)) {
                    continue;
                }
                map.put(key, jo.getString(key));
            } else {
                if (start) {
                    if (!key.startsWith(name)) {
                        continue;
                    }
                    map.put(key, jo.getString(key));
                } else {
                    if (!key.endsWith(name)) {
                        continue;
                    }
                    map.put(key, jo.getString(key));
                }
            }

        }
        return map;
    }

    /**
     * 设置 json 对象
     *
     * @param jo
     * @param entries
     */
    public static void setEntries(JSONObject jo, Map<String, String> entries) {
        for (Iterator<String> itr = entries.keySet().iterator(); itr.hasNext(); ) {
            String key = itr.next();
            jo.remove(key);
            jo.put(key, entries.get(key));
        }
    }


    /**
     * 获取JSONObject中特定取值的键值对
     *
     * @param jo
     * @param value
     * @param whole
     * @param start
     * @return
     */
    public static Map<String, String> getEntriesByValue(JSONObject jo, String value, boolean whole, boolean start) {
        Map<String, String> map = new LinkedHashMap<String, String>();
        for (Iterator<?> itr = jo.keySet().iterator(); itr.hasNext(); ) {
            String key = (String) itr.next();
            String val = jo.getString(key);

            if (whole) {
                if (!val.equals(value)) {
                    continue;
                }
                map.put(key, val);
            } else {
                if (start) {
                    if (!val.startsWith(value)) {
                        continue;
                    }
                    map.put(key, val);
                } else {
                    if (!val.endsWith(value)) {
                        continue;
                    }
                    map.put(key, val);
                }
            }
        }
        return map;
    }

    /**
     * 获取对象的 json 表示串，若有异常，则返回异常消息串
     * 用于打印对象的 json 表示
     *
     * @param obj
     * @return
     */
    public static String toString(Object obj) {
        try {
            return obj == null ? "" : JSON.toJSONString(obj);
        } catch (Exception e) {
            logger.error(e);
            return e.getMessage();
        }
    }

    /**
     * 去除 JSONObject 中属于 java.util.Date 类型但json值为空的属性
     *
     * @param jo
     * @param beanClass
     */
    public static void removeEmptyDateAttr(JSONObject jo, Class<?> beanClass) {
        Field[] fields = beanClass.getDeclaredFields();
        for (Field field : fields) {
            String type = field.getType().getCanonicalName();
            if (!JAVA_UTIL_DATE_NAME.equals(type)) {
                continue;
            }
            String attr = field.getName();
            if (!jo.containsKey(attr)) {
                continue;
            }
            String ov = Optional.ofNullable(jo.getString(attr)).orElse("").trim();
            if (!ov.isEmpty()) {
                continue;
            }
            jo.remove(attr);
        }
    }

    /**
     * 将属性值为 'null' 串的的属性置为 null
     *
     * @param jo
     * @param beanClass
     */
    public static void setToNull(JSONObject jo, Class<?> beanClass) {
        Field[] fields = beanClass.getDeclaredFields();
        for (Field field : fields) {
            String attr = field.getName();
			if (!jo.containsKey(attr)) {
				continue;
			}
			String ov = Optional.ofNullable(jo.getString(attr)).orElse("").trim();
			if ("null".equals(ov) || "'null'".equals(ov) || "\"null\"".equals(ov)) {
				jo.put(attr, null);
			}
        }
    }
}
