package com.yy.manager.utils.rankingexcelgen;

import com.google.common.collect.Maps;
import com.yy.manager.utils.ExcelAnalyzeSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/11
 * @功能说明
 * @版本更新列表
 */
@Service
@Scope("prototype")
public class RankingExcelProcess {
    Logger log = LoggerFactory.getLogger(RankingExcelProcess.class);

    @Autowired
    ExcelAnalyzeSupport excelSup;

    @Autowired
    RankingExcelWorker rankingExcelWorker;

    public Map<String,String> genConfig(String filename, String sheetName){
        Map<String, String> result = Maps.newHashMap();
        try{
            log.info("filename:" + filename + " | sheetName:" + sheetName);
            init(filename, sheetName);
            //analyzeItemTransForm();
            RankingExcelDataPrototype entrance = new RankingExcelData();
            startAnalyze(entrance);
            result.put("result","config generation success ");

        }catch (Exception e){
            log.error(e.getMessage(), e);
            result.put("result","config generation failed => "+e.getMessage()+"<br><br>log:<br>"+rankingExcelWorker.logInfo);
        }finally {
            rankingExcelWorker.logInfo = new StringBuffer();
        }
        return result;
    }

    public String genConfigWithStream(MultipartFile filename, String sheetName) throws Exception {
        //try{
            log.info("filename:" + filename + " | sheetName:" + sheetName);
            initWithStream(filename, sheetName);

            RankingExcelDataPrototype entrance = new RankingExcelData();
            startAnalyze(entrance);
            return "config generation success ";

        /*}catch (Exception e){
            log.error(e.getMessage(), e);
            return "config generation failed => " + e.getMessage() + "<br><br>log:<br>" + rankingExcelWorker.logInfo;
        }finally {
            rankingExcelWorker.logInfo = new StringBuffer();
        }*/

    }


    private void startAnalyze(RankingExcelDataPrototype entrance) throws Exception {
        long actId = entrance.analyze(excelSup);
        entrance.notifyOriginAttrs(Maps.newHashMap());
        entrance.update(rankingExcelWorker);
        rankingExcelWorker.insertData(actId);
    }

    public void init(String fileFullName, String sheetName){
        try {
            excelSup.initExcel(fileFullName, sheetName);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void initWithStream(MultipartFile fileFullName, String sheetName){
        try {
            excelSup.initExcelWithStream(fileFullName, sheetName);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
