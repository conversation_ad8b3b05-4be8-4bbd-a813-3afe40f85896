/*
 * @(#)HdztActor.java 2020-08-31
 *
 * Copy Right@ 欢聚时代
 *
 * 代码生成: hdzt_actor 表的数据模型类  HdztActor
 */

package com.yy.manager.utils.rankingexcelgen;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * @功能说明
 * <pre>
 * hdzt_actor 表的数据模型类  HdztActor
 * </pre>
 *
 * @版本更新
 * <pre>
 * 修改版本: 1.0.0 / 2020-08-31 / cgc
 * 修改说明：形成初始版本
 * 复审人：
 * </pre>
 */
@SuppressWarnings("serial")
public class HdztActor implements java.io.Serializable , Cloneable {
	private static final Logger log = LoggerFactory.getLogger(HdztActor.class);

	public static String TABLE_NAME = "hdzt_actor";

	// java 属性字段 和 库表字段对应关系， key-java属性， val-表字段名
	public static Map<String, String> TABLE_FIELDS = new HashMap<String, String>();
	static {
		TABLE_FIELDS.put("role", "role");
		TABLE_FIELDS.put("utime", "utime");
		TABLE_FIELDS.put("ctime", "ctime");
		TABLE_FIELDS.put("extjson", "extjson");
		TABLE_FIELDS.put("remark", "remark");
		TABLE_FIELDS.put("type", "type");
		TABLE_FIELDS.put("name", "name");
		TABLE_FIELDS.put("busiId", "busi_id");
	};

	public static RowMapper<HdztActor> ROW_MAPPER = new RowMapper<HdztActor>() {
		@Override
		public HdztActor mapRow(ResultSet rs, int rowNum) throws SQLException {
			HdztActor entity = new HdztActor();
			entity.setRole(rs.getLong("role"));
			entity.setUtime(rs.getTimestamp("utime"));
			entity.setCtime(rs.getTimestamp("ctime"));
			entity.setExtjson(rs.getString("extjson"));
			entity.setRemark(rs.getString("remark"));
			entity.setType(rs.getLong("type"));
			entity.setName(rs.getString("name"));
			entity.setBusiId(rs.getLong("busi_id"));
			return entity;
		}
	};

	// 表主键属性集合
	public static final String[] TABLE_PKS = new String[]{
		 "role"
	};

	private Long role;
	private Date utime;
	private Date ctime;
	private String extjson;
	private String remark;
	private Long type;
	private String name;
	private Long busiId;

	/** 无参数构造函数  */
	public HdztActor(){
	}

	/** 主键属性构造函数 */
	public HdztActor(Long role){
		this.role = role;
	}

	/** 全属性构造函数 - 自动生成参数顺序可能会变而函数签名不变，使用时可能参数错乱，若确定不再重新生成，可去掉注释！ */
	/*public HdztActor(Long role, Date utime, Date ctime, String extjson, String remark, Long type, String name, Long busiId){
		this.role = role;
		this.utime = utime;
		this.ctime = ctime;
		this.extjson = extjson;
		this.remark = remark;
		this.type = type;
		this.name = name;
		this.busiId = busiId;
	}*/


	public void setRole(Long role){
		this.role = role;
	}

	public Long getRole(){
		return role;
	}

	public void setUtime(Date utime){
		this.utime = utime;
	}

	public Date getUtime(){
		return utime;
	}

	public void setCtime(Date ctime){
		this.ctime = ctime;
	}

	public Date getCtime(){
		return ctime;
	}

	public void setExtjson(String extjson){
		this.extjson = extjson;
	}

	public String getExtjson(){
		return extjson;
	}

	public void setRemark(String remark){
		this.remark = remark;
	}

	public String getRemark(){
		return remark;
	}

	public void setType(Long type){
		this.type = type;
	}

	public Long getType(){
		return type;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return name;
	}

	public void setBusiId(Long busiId){
		this.busiId = busiId;
	}

	public Long getBusiId(){
		return busiId;
	}

	public HdztActor clone() {
		try {
			return (HdztActor) super.clone();
		} catch (CloneNotSupportedException e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}

	public String toString() {
		return JSON.toJSONString(this);
	}
}
