package com.yy.manager.utils.bce;


import com.alibaba.fastjson.JSON;
import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.yy.common.utils.SecurityUtils;
import com.yy.manager.exception.SuperException;
import com.yy.manager.thrift.bos_sentry_gate.UploadTokenResp;
import com.yy.manager.thrift.client.BosSentryGateClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * https://cloud.baidu.com/doc/BOS/index.html
 * ak和sk找谭裕林
 *
 * @Author: CXZ
 * @Desciption:
 * @Date: 2022/12/02 14:08
 * @Modified:
 */
@Slf4j
@Service
public class BceService {
    @Autowired
    private BosSentryGateClient client;

    public String uploadMinFileData(byte[] binaryData, String fileName) {
        return uploadMinFileData(binaryData, fileName, SecurityUtils.getLoginUid());
    }

    public String uploadMinFileData(byte[] binaryData, String fileName, long uid) {
        UploadTokenResp tokenResp = client.getTokenFromRpc(fileName, uid);
        if (tokenResp == null) {
            throw new SuperException("获取Bos客户端失败", 500);
        }

        log.info("tokenResp={}", JSON.toJSONString(tokenResp));
        BosClient bosClient = buildTemporaryBosClient(tokenResp.getEndPoint(), tokenResp.getAk(), tokenResp.getSk(), tokenResp.getToken());
        bosClient.putObject(tokenResp.getBucket(), tokenResp.getFileName(), binaryData);
        bosClient.shutdown();
        return tokenResp.bs2Url + tokenResp.getFileName();
    }

    public boolean isExists(String fileName) {
        return false;
    }


    private BosClient buildTemporaryBosClient(String endPoint, String ak, String sk, String token) {
        return new BosClient(new BosClientConfiguration()
                .withEndpoint(endPoint)
                .withCredentials(new DefaultBceSessionCredentials(ak, sk, token)));
    }
}
