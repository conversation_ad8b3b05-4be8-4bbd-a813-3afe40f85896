package com.yy.manager.utils;

import com.yy.common.utils.MDCUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @since 2025/5/26 18:15
 */
public class MdcExecutorService implements ExecutorService {

    private final ExecutorService target;

    public MdcExecutorService(ExecutorService target) {
        this.target = target;
    }

    @Override
    public void shutdown() {
        target.shutdown();
    }

    @NotNull
    @Override
    public List<Runnable> shutdownNow() {
        return target.shutdownNow();
    }

    @Override
    public boolean isShutdown() {
        return target.isShutdown();
    }

    @Override
    public boolean isTerminated() {
        return target.isTerminated();
    }

    @Override
    public boolean awaitTermination(long timeout, @NotNull TimeUnit unit) throws InterruptedException {
        return target.awaitTermination(timeout, unit);
    }

    @NotNull
    @Override
    public <T> Future<T> submit(@NotNull Callable<T> task) {
        Map<String, String> mdcContext = MDCUtils.getCopyOfContextMap();
        return target.submit(() -> {
            MDCUtils.setContextMap(mdcContext);
            try {
                return task.call();
            } finally {
                MDCUtils.clearContext();
            }
        });
    }

    @NotNull
    @Override
    public <T> Future<T> submit(@NotNull Runnable task, T result) {
        Map<String, String> mdcContext = MDCUtils.getCopyOfContextMap();
        return target.submit(() -> this.runTask(task, mdcContext), result);
    }

    @NotNull
    @Override
    public Future<?> submit(@NotNull Runnable task) {
        Map<String, String> mdcContext = MDCUtils.getCopyOfContextMap();
        return target.submit(() -> this.runTask(task, mdcContext));
    }

    @NotNull
    @Override
    public <T> List<Future<T>> invokeAll(@NotNull Collection<? extends Callable<T>> tasks) throws InterruptedException {
        return target.invokeAll(tasks);
    }

    @NotNull
    @Override
    public <T> List<Future<T>> invokeAll(@NotNull Collection<? extends Callable<T>> tasks, long timeout, @NotNull TimeUnit unit) throws InterruptedException {
        return target.invokeAll(tasks, timeout, unit);
    }

    @NotNull
    @Override
    public <T> T invokeAny(@NotNull Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
        return target.invokeAny(tasks);
    }

    @Override
    public <T> T invokeAny(@NotNull Collection<? extends Callable<T>> tasks, long timeout, @NotNull TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return target.invokeAny(tasks, timeout, unit);
    }

    @Override
    public void close() {
        target.close();
    }

    @Override
    public void execute(@NotNull Runnable command) {
        this.runTask(command, MDCUtils.getCopyOfContextMap());
    }

    private void runTask(@NotNull Runnable command, Map<String, String> mdcContext) {
        target.execute(() -> {
            MDCUtils.setContextMap(mdcContext);
            try {
                command.run();
            } finally {
                MDCUtils.clearContext();
            }
        });
    }
}
