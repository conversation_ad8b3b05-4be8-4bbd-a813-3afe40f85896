/*
* @(#)ResourceUtil.java  2008/06/20
*
* Copyright (c) 广州亿迅科技有限公司
*
*/
package com.yy.manager.utils;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Properties;

/**
 * <pre>
 * @date 2008-06-20
 * <AUTHOR>
 *
 * @功能说明：
 * 资源相关的工具类. 方便资源的读取等操作，提供 properties文件、xml文件、jar包中资源等的读取函数，
 * 读取出的资源直接以 Document、Reader、File 等方式返回，能满足几乎所有文件资源的读取要求；另外本
 * 函数还提供了一个路径定位函数 getCurrentPath()，方便路径定位。
 *
 * @版本更新列表
 * 修改版本: 1.0.1
 * 修改日期：2008-12-21
 * 修改人 : guolp
 * 修改说明：提供 getCurrentPath() 函数，进行相对路径定位
 * 复审人：
 *
 * 修改版本: 1.0.0
 * 修改日期：2008-06-20
 * 修改人 : yinty
 * 修改说明：形成初始版本
 * 复审人：
 *</pre>
 */

public abstract class ResourceUtil {
	private static final Log logger = LogFactory.getLog(com.yy.manager.utils.ResourceUtil.class);

	/**
	 * 获取指定资源的 URL
	 * @param resource - 指定的资源名
	 * @return
	 */
	public static URL getResourceURL(String resource){
		String stripped = resource.startsWith("/") ? resource.substring(1) : resource;
		URL url = null;
		ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
		if(classLoader != null) {
			url = classLoader.getResource(stripped );
		}
		if(url == null ) {
			url = com.yy.manager.utils.ResourceUtil.class.getResource( resource );
		}
		if(url == null ) {
			url = com.yy.manager.utils.ResourceUtil.class.getClassLoader().getResource(stripped );
		}
		if(url == null){
			throw new NullPointerException("resource ["+resource+"] not found");
		}
		return url;
	}

	/**
	 * 获取指定资源的输入流对象
	 * @param resource - 指定的资源名字
	 * @return
	 */
	public static InputStream getResourceAsStream(String resource) {
		URL url = getResourceURL(resource);
		try {
			return url.openStream();
		} catch (IOException e) {
			throw new RuntimeException("open resource ["+resource+"] error", e);
		}
	}

	/**
	 * 读取属性文件内容，构造属性对象返回
	 * @param propertiesFile - 属性文件名
	 * @return
	 */
	public static Properties getResourceAsProperties(String propertiesFile) {
		InputStream in = null;
		try{
			in = getResourceAsStream(propertiesFile);
			Properties props = new Properties();
			props.load(in);
			return props;
		}catch(IOException e){
			throw new RuntimeException("load Properties file ["+propertiesFile+"] error", e);
		}finally{
			closeResource(in);
		}
	}

	/**
	 * 获取指定资源的文件对象
	 * @param resource - 指定的文件资源名
	 * @return
	 */
	public static File getResourceAsFile(String resource){
		return new File(getResourceURL(resource).getFile());
	}

	/**
	 * 获取指定文件资源的 字符 输入对象
	 * @param resource - 指定的文件资源
	 * @param charset - 字符编码集
	 * @return
	 */
	public static Reader getResourceAsReader(String resource, String charset){
		InputStream stream = getResourceAsStream(resource);
		if(charset != null){
			try {
				return new InputStreamReader(stream, charset);
			} catch (UnsupportedEncodingException ingore) {}
		}
		return new InputStreamReader(stream);
	}

	/**
	 * 通过 url ， 读取Internet上的网络资源，返回字节输入流对象
	 * @param urlString - url 地址
	 * @return
	 */
	public static InputStream getURLAsStream(String urlString){
		URL url = null;
		try {
			url = new URL(urlString);
		} catch (MalformedURLException e) {
			throw new RuntimeException(urlString + "is not legal ", e);
		}
		URLConnection conn;
		try {
			conn = url.openConnection();
			return conn.getInputStream();
		} catch (IOException e) {
			throw new RuntimeException("open url ["+urlString+"] error", e);
		}
	}

	/**
	 * 通过 url ， 读取Internet上的网络资源，返回字符输入流对象
	 * @param urlString - url 地址
	 * @return
	 */
	public static Reader getURLAsReader(String urlString){
		return new InputStreamReader(getURLAsStream(urlString));
	}

	/**
	 * 通过 url ， 读取Internet上的网络资源，返回字属性对象
	 * @param urlString - url 地址
	 * @return
	 */
	public static Properties getUrlAsProperties(String urlString){
		InputStream in = null;
		try{
			in = getURLAsStream(urlString);
			Properties props = new Properties();
			props.load(in);
			return props;
		}catch(IOException e){
			throw new RuntimeException("load Properties file from ["+urlString+"] error", e);
		}finally{
			closeResource(in);
		}
	}

	/**
	 * 关闭打开的资源
	 * @param stream
	 */
	public static void closeResource(Closeable stream){
		if(stream == null){
			return;
		}
		try {
			stream.close();
		} catch (Exception e) {
			if(logger.isErrorEnabled()){
				logger.error("close stream error!", e);
			}
		}
	}


	/**
	 * 以subpath为参考，获取截止到 subpath 的完整路径（以分隔符号结束），本函数可用来定位工作目录
	 * @param subpath
	 * @return
	 */
	public static String getCurrentPath(String subpath) {
		String path = null;
		try{
			subpath = subpath==null ? "classes" : subpath.trim();
			subpath = subpath.length()==0 ? "" : subpath;
			subpath = subpath.replace('/', File.separatorChar);
			subpath = subpath.replace('\\', File.separatorChar);
			subpath += subpath.endsWith(File.separator) ? "" : File.separator;
			subpath = (subpath.startsWith(File.separator)? "" : File.separator) + subpath;

			path = com.yy.manager.utils.ResourceUtil.class.getProtectionDomain().getCodeSource().getLocation().getPath()+"/";
			path = path.replace('/', File.separatorChar);
			path = path.replace('\\', File.separatorChar);
			path += path.endsWith(File.separator) ? "" : File.separator;
			String classString = ".class";
			int inx = path.indexOf(subpath);
			if (path.endsWith(classString) || inx != -1) {
				path = inx == -1 ? path : path.substring(0, inx);
				path += subpath;
			}
		}catch(Exception e){
			if(logger.isErrorEnabled()){
				logger.error(e, e);
			}
		}
		if(logger.isInfoEnabled()){
			if(path==null){
				logger.info("fail to find path with subpath: " + subpath);
			}else{
				logger.info("find path is: " + path);
			}
		}
		return path;
	}
}
