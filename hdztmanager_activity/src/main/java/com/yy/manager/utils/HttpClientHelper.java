
package com.yy.manager.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.ClientCookie;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.Cookie;
import java.io.IOException;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Title: Description: Create Time: 2014-04-01 下午4:20 author: wangyan version: 1.0
 */
public class HttpClientHelper {
    private static final Logger log = LoggerFactory.getLogger(HttpClientHelper.class);

    private static final String YY_COM = "yy.com";

    private HttpClient httpClient;

    private Charset charset;

    public HttpClientHelper(Charset charset) {
        httpClient = HttpClients.createDefault();
        this.charset = charset;
    }

    public String excutePost(String targetUrl, Map<String, String> params) throws IOException {
        HttpPost httpost = new HttpPost(targetUrl);

        if (params != null && !params.isEmpty()) {
            List<NameValuePair> data = new ArrayList<NameValuePair>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                NameValuePair pair = new BasicNameValuePair(entry.getKey(), entry.getValue());
                data.add(pair);
            }
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(data, charset.name());
            httpost.setEntity(entity);
        }

        HttpResponse response = httpClient.execute(httpost);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }


    /**
     *
     * <p>Description: post 报文使用string</p>
     * <AUTHOR>
     * @date 2019年1月3日
     *
     *  @param targetUrl
     *  @param param
     *  @return
     *  @throws IOException
     *  String
     * @throws
     */
    public String excutePostString(String targetUrl, String param) throws IOException {
        HttpPost httpost = new HttpPost(targetUrl);

        if (StringUtils.isNotBlank(param)) {
            StringEntity requestEntity = new StringEntity(param, "utf-8");
            httpost.setEntity(requestEntity);
        }

        HttpResponse response = httpClient.execute(httpost);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }

    public String excuteGet(String targetUrl) throws IOException {
        HttpGet httpGet = new HttpGet(targetUrl);
        HttpResponse response = httpClient.execute(httpGet);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }
    public String excuteGet(String targetUrl, String cookie) throws IOException {
        HttpGet httpGet = new HttpGet(targetUrl);
        httpGet.addHeader("Cookie", cookie);
        HttpResponse response = httpClient.execute(httpGet);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }

    public static String executePostWithCookie(String url, Cookie[] cookies, String requestBody) {
        Clock clock = new Clock();
        String data = "";
        HttpPost httpget = new HttpPost(url);
        CloseableHttpResponse response = null;
        HttpEntity entity = null;
        //获取cookies
        CookieStore cookieStore = new BasicCookieStore();

        if (cookies != null) {
            for (Cookie cookie : cookies) {
                BasicClientCookie cookie_ = new BasicClientCookie(cookie.getName(), cookie.getValue());
                String domain = StringUtils.isBlank(cookie.getDomain()) ? YY_COM : cookie.getDomain();
                cookie_.setPath(cookie.getPath());
                cookie_.setComment(cookie.getComment());
                cookie_.setDomain(domain);
                cookie_.setSecure(cookie.getSecure());
                cookie_.setAttribute(ClientCookie.DOMAIN_ATTR, "true");
                cookie_.setAttribute("domain", domain);
                cookieStore.addCookie(cookie_);
            }
        }

        RequestConfig globalConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD)
                .setSocketTimeout(50000)
                .setConnectionRequestTimeout(50000)
                .setConnectTimeout(50000)
                .build();

        CloseableHttpClient httpClient_ = HttpClientBuilder.create().setDefaultRequestConfig(globalConfig)
                .setDefaultCookieStore(cookieStore).build();

        try {
            clock.tag();
            httpget.setEntity(new StringEntity(requestBody,"utf-8"));
            response = (CloseableHttpResponse) httpClient_.execute(httpget);
            entity = response.getEntity();
            data = EntityUtils.toString(entity, "UTF-8");
            log.info("excutePostGet url:{}   ,clock:{}, data:{}",url, clock.tag(),  data);

        } catch (Exception e) {
            log.warn("excutePostGet error, url:{}, clock:{}, e:{}", url, clock.tag(), e);
        }
        return data;
    }

    public static String excutePostGet(String url, Cookie[] cookies) {
        Clock clock = new Clock();
        String data = "";
        HttpGet httpget = new HttpGet(url);
        CloseableHttpResponse response = null;
        HttpEntity entity = null;

        //获取cookies
        CookieStore cookieStore = new BasicCookieStore();

        if (cookies != null) {
            for (Cookie cookie : cookies) {
                BasicClientCookie cookie_ = new BasicClientCookie(cookie.getName(), cookie.getValue());
                String domain = StringUtils.isBlank(cookie.getDomain()) ? YY_COM : cookie.getDomain();
                cookie_.setPath(cookie.getPath());
                cookie_.setComment(cookie.getComment());
                cookie_.setDomain(domain);
                cookie_.setSecure(cookie.getSecure());
                cookie_.setAttribute(ClientCookie.DOMAIN_ATTR, "true");
                cookie_.setAttribute("domain", domain);
                cookieStore.addCookie(cookie_);
            }
        }

        RequestConfig globalConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD)
                .setSocketTimeout(5000)
                .setConnectionRequestTimeout(5000)
                .setConnectTimeout(5000).build();
        CloseableHttpClient httpClient_ = HttpClientBuilder.create().setDefaultRequestConfig(globalConfig)
                .setDefaultCookieStore(cookieStore).build();
        try {
            clock.tag();
            response = (CloseableHttpResponse) httpClient_.execute(httpget);
            entity = response.getEntity();
            data = EntityUtils.toString(entity, "UTF-8");
            log.info("excutePostGet url:{}   ,clock:{}, data:{}",url, clock.tag(),  data);

        } catch (Exception e) {
            log.warn("excutePostGet error, url:{}, clock:{}, e:{}", url, clock.tag(), e);
        }
        return data;
    }

    public <T> T getFormWithCookie(String targetUrl, TypeReference valueTypeRef, Cookie[] cookies) throws IOException {
        ObjectMapper om = createObjectMapper();
        String responseString = excutePostGet(targetUrl, cookies);
        return (T) om.readValue(responseString, valueTypeRef);
    }

    public <T> T getForm(String targetUrl, TypeReference valueTypeRef) throws IOException {
        ObjectMapper om = createObjectMapper();
        String responseString = excuteGet(targetUrl);
        return (T) om.readValue(responseString, valueTypeRef);
    }

    private ObjectMapper createObjectMapper(){
        return new ObjectMapper()
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }

    public String postJson(String targetUrl, Object o) throws IOException {
        HttpPost httpost = new HttpPost(targetUrl);
        httpost.setHeader("content-type", "application/json");
        httpost.setHeader("charset", "utf-8");
        ObjectMapper om = createObjectMapper();
        if (o != null ) {
            StringEntity entity = new StringEntity(om.writeValueAsString(o) );
            httpost.setEntity(entity);
        }
        HttpResponse response = httpClient.execute(httpost);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }

    public <T> T postJson(String targetUrl, Object o, TypeReference valueTypeRef) throws IOException {
        ObjectMapper om = createObjectMapper();
        String responseString = postJson(targetUrl, o);
        return (T) om.readValue(responseString, valueTypeRef);
    }

    public static void main(String[] args) {
        com.yy.manager.utils.HttpClientHelper httpClientHelper = new com.yy.manager.utils.HttpClientHelper(com.yy.manager.utils.Const.UTF_8);
        try {
            /*
             * Map<String, String> map = Maps.newHashMap(); map.put("type", "3"); map.put("expect",
             * "20140401"); map.put("status", "2"); map.put("vsid", "426766");
             */
            String rep = httpClientHelper.excutePost("http://zjgh.g.yy.com/zijinrank/zijinow", null);
            System.out.println(rep);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }


}
