package com.yy.manager.utils;

//import com.yy.ent.commons.yypclient.exception.BusinessException;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/10/21
 * @功能说明 解析活動配置excel，要求符號全英
 * @版本更新列表
 */
@Component
@Scope("prototype")
public class ExcelAnalyzeSupport {

    @Getter
    private Workbook workbook = null;
    @Getter
    private Sheet sheet = null;
    private String fileSource;

    private static final int TITLE_START_POSITION = 0;
    private static final int DATEHEAD_START_POSITION =1;
    private static final int HEAD_START_POSITION = 2;
    private static final int CONTENT_START_POSITION = 3;

    private static final String FILE_TYPE_XLSX = "xlsx";

    private static final String FILE_TYPE_XLS = "xls";

    /*******************read*********************/


    private Workbook getReadWorkBook(String filePath) throws Exception, IOException {
        //xls-2003, xlsx-2007
        FileInputStream is = null;
        try {
            is = new FileInputStream(filePath);
            if (filePath.toLowerCase().endsWith(FILE_TYPE_XLSX)) {
                return new XSSFWorkbook(is);
            } else if (filePath.toLowerCase().endsWith(FILE_TYPE_XLS)) {
                return new HSSFWorkbook(is);
            } else {
                //  抛出业务异常
                throw new Exception("excel格式文件错误");
            }
        } catch (Exception e) {
            //  抛出自定义的业务异常
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(is);
        }
        return null;
    }

    private Workbook getReadWorkBookWithStream(MultipartFile filePath) throws Exception, IOException {
        //xls-2003, xlsx-2007
        InputStream inputStream = null;

        try {

            String originalFilename = filePath.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));

           // File file = new File(filePath.getInputStream());
            inputStream = filePath.getInputStream();

            if (extension.toLowerCase().endsWith(FILE_TYPE_XLSX)) {
                return new XSSFWorkbook(inputStream);
            } else if (extension.toLowerCase().endsWith(FILE_TYPE_XLS)) {
                return new HSSFWorkbook(inputStream);
            } else {
                //  抛出业务异常
                throw new Exception("excel格式文件错误");
            }
        } catch (Exception e) {
            //  抛出自定义的业务异常
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        return null;
    }

    public void initExcel(String sourceFilePath, String sheetName)  {
        try {
            this.fileSource = sourceFilePath;
            workbook = getReadWorkBook(sourceFilePath);
            //获取sheet
            sheet = workbook.getSheet(sheetName);

        } catch (Exception e) {
            throw new RuntimeException("initExcel failed",e);
        }

    }

    public void initExcelWithStream(MultipartFile sourceFilePath, String sheetName)  {
        try {
            //this.fileSource = sourceFilePath;
            workbook = getReadWorkBookWithStream(sourceFilePath);
            //获取sheet
            sheet = workbook.getSheet(sheetName);

        } catch (Exception e) {
            throw new RuntimeException("initExcel failed",e);
        }

    }


    public String readExcel(int row, int col) {

        List<String> contents = readExcel(row, row, col, col);
        if (contents!=null && !contents.isEmpty()){
            return contents.get(0);
        }
        return "";
    }

    public List<String> readExcel(int rowStart, int rowEnd, int columnStart, int columnEnd) {

        try {

            List<String> contents = new ArrayList();

            //第0行是表名，忽略，从第二行开始读取
            for (int rowNum = rowStart; rowNum <= rowEnd; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    return null;
                }
                for (int colNum = columnStart; colNum <= columnEnd; colNum++) {
                    Cell cell = row.getCell(colNum);
                    if (cell != null) {
                        contents.add(getCellStringVal(cell).trim());
                    }
                }
            }
            return contents;
        } catch (Exception e){
            new RuntimeException("readExcel fail", e);
        }finally {
            IOUtils.closeQuietly(workbook);
        }
        return null;
    }

    private String getCellStringVal(Cell cell) {
        CellType cellType = cell.getCellType();
        switch (cellType) {
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case STRING:
                return cell.getStringCellValue();
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            case ERROR:
                return String.valueOf(cell.getErrorCellValue());
            default:
                return StringUtils.EMPTY;
        }
    }

    public void setSheet(int sheetIndex){
        sheet = workbook.getSheetAt(sheetIndex);
    }

    public void setSheet(String sheetName) {
        sheet = workbook.getSheet(sheetName);
    }

    public int getSheetIndex(Sheet sheet){
        return workbook.getSheetIndex(sheet);
    }

    /*******************write*********************/


    public byte[] excelExport(List<?> dataList, Map<String, String> titleMap, String sheetName) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 初始化workbook
        initXSSFWorkbook(sheetName);
        // 标题行
        createTitleRow(titleMap, sheetName);
        // 时间行
        createDateHeadRow(titleMap);
        // 表头行
        createHeadRow(titleMap);
        // 文本行
        createContentRow(dataList, titleMap);
        //设置自动伸缩
        //autoSizeColumn(titleMap.size());
        // 写入处理结果
        try {
            //生成UUID文件名称
            //UUID是指在一台机器上生成的数字，它保证对在同一时空中的所有机器都是唯一的。
            UUID uuid = UUID.randomUUID();
            String filedisplay = uuid+".xls";
            workbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void initXSSFWorkbook(String sheetName) {
        workbook = new XSSFWorkbook();
        sheet = workbook.createSheet(sheetName);
    }

    /**
     * 生成标题（第零行创建）
     * @param titleMap 对象属性名称->表头显示名称
     * @param sheetName sheet名称
     */
    private void createTitleRow(Map<String, String> titleMap, String sheetName) {
        CellRangeAddress titleRange = new CellRangeAddress(0, 0, 0, titleMap.size() - 1);
        sheet.addMergedRegion(titleRange);
        XSSFRow titleRow = (XSSFRow) sheet.createRow(TITLE_START_POSITION);
        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(sheetName);
    }

    /**
     * 创建时间行（第一行创建）
     * @param titleMap 对象属性名称->表头显示名称
     */
    private void createDateHeadRow(Map<String, String> titleMap) {
        CellRangeAddress dateRange = new CellRangeAddress(1, 1, 0, titleMap.size() - 1);
        sheet.addMergedRegion(dateRange);
        XSSFRow dateRow = (XSSFRow) sheet.createRow(DATEHEAD_START_POSITION);
        XSSFCell dateCell = dateRow.createCell(0);
        dateCell.setCellValue(new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
    }

    /**
     * 创建表头行（第二行创建）
     * @param titleMap 对象属性名称->表头显示名称
     */
    private void createHeadRow(Map<String, String> titleMap) {
        // 第1行创建
        XSSFRow headRow = (XSSFRow) sheet.createRow(HEAD_START_POSITION);
        int i = 0;
        for (String entry : titleMap.keySet()) {
            XSSFCell headCell = headRow.createCell(i);
            headCell.setCellValue(titleMap.get(entry));
            i++;
        }
    }

    /**
     *
     * @param dataList 对象数据集合
     * @param titleMap 表头信息
     */
    private void createContentRow(List<?> dataList, Map<String, String> titleMap) {
        try {
            int i=0;
            for (Object obj : dataList) {
                XSSFRow textRow = (XSSFRow) sheet.createRow(CONTENT_START_POSITION + i);
                int j = 0;
                for (String entry : titleMap.keySet()) {
                    String method = "get" + entry.substring(0, 1).toUpperCase() + entry.substring(1);
                    Method m = obj.getClass().getMethod(method, null);
                    String value =   m.invoke(obj, null).toString();
                    XSSFCell textcell = textRow.createCell(j);
                    textcell.setCellValue(value);
                    j++;
                }
                i++;
            }

        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 自动伸缩列（如非必要，请勿打开此方法，耗内存）
     * @param size 列数
     */
    private void autoSizeColumn(Integer size) {
        for (int j = 0; j < size; j++) {
            sheet.autoSizeColumn(j);
        }
    }


}
