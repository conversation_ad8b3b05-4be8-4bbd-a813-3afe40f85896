package com.yy.manager.utils.rankingexcelgen;

//import com.yunshee.hdzt.model.RankingPhaseGroup;
import com.yy.manager.hdzt.entity.RankingPhaseGroup;
import com.yy.manager.utils.ExcelAnalyzeSupport;
import lombok.Data;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.yy.manager.utils.rankingexcelgen.RankingExcelUtil.*;
import static com.yy.manager.utils.rankingexcelgen.RankingExcelUtil.convertDate;

/**
 * <AUTHOR>
 * @date 2020/12/11
 * @功能说明
 * @版本更新列表
 */
@Data
public class RankingExcelPhaseGroup extends RankingPhaseGroup implements RankingExcelDataPrototype {
    private final static Logger log = LoggerFactory.getLogger(RankingExcelPhaseGroup.class);

    private int row;
    private int beginCol;
    private int endCol;
    private Date beginTime;
    private Date endTime;

    private List<RankingExcelPhase> rankingExcelPhases;

    public RankingExcelPhaseGroup(String groupName, String groupCode,int row, int beginCol, int endCol) {
//        this.setActId(actId);
        this.setPhaseGroupName(groupName);
        this.setPhaseGroupCode(groupCode);
        this.setStatus(1);
        this.setCtime(new Date());
        this.setUtime(new Date());
        this.setExtjson("");
        this.setRemark("");


        this.row = row;
        this.beginCol = beginCol;
        this.endCol = endCol;
        rankingExcelPhases = new ArrayList<>();
    }

    @Override
    public long analyze(ExcelAnalyzeSupport excelSup) throws ParseException {
        log.info("当前数据："+this.toString());
        analyzePhase(excelSup, row+1);
        if (rankingExcelPhases!=null){
            beginTime = rankingExcelPhases.get(0).getBeginTime();
            endTime = rankingExcelPhases.get(rankingExcelPhases.size()-1).getEndTime();
        }
        for (RankingExcelPhase rankingExcelPhase : rankingExcelPhases) {
            rankingExcelPhase.analyze(excelSup);
        }
        return 0L;
    }

    @Override
    public void notifyOriginAttrs(Map<String, String> originMap) {
        this.setActId(Integer.parseInt(originMap.get(KEY_ACT_ID)));
        originMap.put(KEY_PHASE_GROUP_NAME,getPhaseGroupName());
        originMap.put(KEY_PHASE_GROUP_CODE,getPhaseGroupCode());
        for (RankingExcelPhase rankingExcelPhase : rankingExcelPhases) {
            rankingExcelPhase.notifyOriginAttrs(originMap);
        }
    }

    @Override
    public void update(RankingExcelWorker worker) {
        for (RankingExcelPhase rankingExcelPhase : rankingExcelPhases) {
            rankingExcelPhase.update(worker);
        }
//        for (RankingPhase rankingPhaseInsert : worker.rankingPhaseInsertList) {
//            rankingPhaseInsert.setPhaseGroupCode(this.getPhaseGroupCode());
//        }
        worker.rankingPhaseGroupInsertList.add(this);
    }

    /**
     *
     *
     * @param excelSup
     * @param unitRow
     * @return  {phase_id, phase_name, begin_time, end_time}
     */
    private void analyzePhase(ExcelAnalyzeSupport excelSup, int unitRow) throws ParseException {
        for (int unitCol = beginCol; unitCol <= endCol; unitCol++) {
            String phaseRawData = readExcel(unitRow,unitCol,excelSup);
            //Phase信息可能為空
            if (phaseRawData!=null && !phaseRawData.isEmpty()){
                phaseRawData.trim();
                String[] paramsPhase = ArrayUtils.addAll(phaseRawData.split(":"),getDates(readExcel(unitRow-RANKING_PHASE_ROW_OFFSET+TIME_ROW_OFFSET,unitCol,excelSup)));
                rankingExcelPhases.add(new RankingExcelPhase(unitCol, Integer.valueOf(paramsPhase[0]), paramsPhase[1], convertDate(paramsPhase[2]), convertDate(paramsPhase[3])));
            }
        }
    }

    @Override
    public String toString() {
        return "RankingExcelPhaseGroup{" +
                "row=" + row +
                ", beginCol=" + beginCol +
                ", endCol=" + endCol +
                ", beginTime=" + beginTime +
                ", endTime=" + endTime +
                ", actId=" + getActId() +
                ", phaseGroupCode='" + getPhaseGroupCode() + '\'' +
                ", phaseGroupName='" + getPhaseGroupName() + '\'' +
                "} " + super.toString();
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public int getBeginCol() {
        return beginCol;
    }

    public void setBeginCol(int beginCol) {
        this.beginCol = beginCol;
    }

    public int getEndCol() {
        return endCol;
    }

    public void setEndCol(int endCol) {
        this.endCol = endCol;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<RankingExcelPhase> getRankingExcelPhases() {
        return rankingExcelPhases;
    }

    public void setRankingExcelPhases(List<RankingExcelPhase> rankingExcelPhases) {
        this.rankingExcelPhases = rankingExcelPhases;
    }
}
