package com.yy.manager.utils.rankingexcelgen;

//import com.yunshee.hdzt.model.RankingItem;
import com.yy.manager.hdzt.entity.RankingItem;
import com.yy.manager.utils.ExcelAnalyzeSupport;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.Date;
import java.util.Map;

import static com.yy.manager.utils.rankingexcelgen.RankingExcelUtil.*;

/**
 * <AUTHOR>
 * @date 2020/12/14
 * @功能说明
 * @版本更新列表
 */
@Data
public class RankingExcelItem extends RankingItem implements RankingExcelDataPrototype {
    private final static Logger log = LoggerFactory.getLogger(RankingExcelItem.class);

    public RankingExcelItem(String itemIds) {
//        this.setActId(actId);
//        this.setRankId(Double.valueOf(paramsRank[0]).longValue());
        this.setItemIds(itemIds);
        this.setItemAlias("");
        this.setExtjson("");
//        this.setRemark(remark);
        this.setCtime(new Date());
        this.setUtime(new Date());
    }

    @Override
    public long analyze(ExcelAnalyzeSupport excelSup) throws ParseException {
        log.info("当前数据："+this.toString());
        return 0L;
    }

    @Override
    public void notifyOriginAttrs(Map<String, String> originMap) {
        this.setActId(Integer.parseInt(originMap.get(KEY_ACT_ID)));
        this.setRankId(Integer.parseInt(originMap.get(KEY_RANK_ID)));
        this.setRemark(originMap.get(KEY_DATA_GROUP_NAME)+"-"+originMap.get(KEY_RANK_NAME));
    }

    @Override
    public void update(RankingExcelWorker worker) {
        worker.rankingItemInsertList.add(this);
    }

    @Override
    public String toString() {
        return "RankingItem{" +
                "actId=" + getActId() +
                ", rankId=" + getRankId()+
                ", itemIds='" + getItemIds() + '\'' +
                ", itemAlias='" + getItemAlias() + '\'' +
                ", remark='" + getRemark() + '\'' +
                '}';
    }
}
