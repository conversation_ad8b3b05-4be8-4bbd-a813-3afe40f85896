package com.yy.manager.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public final class ActCfgUtils {

    public static Object toObject(String value, Object original) {
        if (original == null) {
            return value;
        }

        if (original instanceof Integer) {
            return cn.hutool.core.convert.Convert.toInt(value);
        }

        if (original instanceof Long) {
            return cn.hutool.core.convert.Convert.toLong(value);
        }

        if (original instanceof Float) {
            return cn.hutool.core.convert.Convert.toFloat(value);
        }

        if (original instanceof Double) {
            return cn.hutool.core.convert.Convert.toDouble(value);
        }

        if (original instanceof Date) {
            return cn.hutool.core.convert.Convert.toDate(value);
        }

        if (original instanceof LocalDateTime) {
            return cn.hutool.core.convert.Convert.toLocalDateTime(value);
        }

        if (original instanceof Boolean) {
            return cn.hutool.core.convert.Convert.toBool(value);
        }

        if (original instanceof Byte) {
            return cn.hutool.core.convert.Convert.toByte(original);
        }

        if (original instanceof BigDecimal) {
            return cn.hutool.core.convert.Convert.toBigDecimal(value);
        }

        if (original instanceof BigInteger) {
            return Convert.toBigInteger(value);
        }

        return value;
    }

    public static String toStr(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Date) {
            return DateFormatUtils.format((Date) value, DateUtil.DEFAULT_PATTERN);
        }

        if (value instanceof LocalDateTime) {
            return LocalDateTimeUtil.format((LocalDateTime) value, DateUtil.DEFAULT_PATTERN);
        }

        return StrUtil.str(value, StandardCharsets.UTF_8);

    }

    /**
     * 将string值更新到rowConfig中
     * @param rowConfig
     * @param key
     * @param value
     */
    public static void setValue(Map<String, Object> rowConfig, String key, String value) {
        if (MapUtils.isEmpty(rowConfig) || !rowConfig.containsKey(key)) {
            return;
        }

        rowConfig.compute(key, (k, v) -> toObject(value, v));
    }

    public static void setValue(Map<String, Object> rowConfig, String key, Object value) {
        if (MapUtils.isEmpty(rowConfig) || !rowConfig.containsKey(key)) {
            return;
        }

        rowConfig.put(key, value);
    }

    public static void setLocalDateTime(Map<String, Object> rowConfig, String key, LocalDateTime value) {
        if (MapUtils.isEmpty(rowConfig) || !rowConfig.containsKey(key)) {
            return;
        }

        rowConfig.compute(key, (k, v) -> value);
    }

    public static LinkedHashMap<String, Object> findByCondition(List<LinkedHashMap<String, Object>> tableConfig, Map<String, Object> condition) {
        if (CollectionUtils.isEmpty(tableConfig)) {
            return null;
        }

        Map<String, String> conditionMap = condition.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> toStr(entry.getValue())));
        outer: for (LinkedHashMap<String, Object> rowConfig : tableConfig) {
            for (Map.Entry<String, String> entry : conditionMap.entrySet()) {
                String value = toStr(rowConfig.get(entry.getKey()));
                if (!StringUtils.equals(value, entry.getValue())) {
                    continue outer;
                }
            }

            return rowConfig;
        }

        return null;
    }

    public static LinkedHashMap<String, Object> getKey(LinkedHashMap<String, Object> baseRow, List<String> keyColumns) {
        LinkedHashMap<String, Object> result = new LinkedHashMap<>(keyColumns.size());
        for (String keyColumn : keyColumns) {
            result.put(keyColumn, baseRow.get(keyColumn));
        }

        return result;
    }

    public static String toStrKey(LinkedHashMap<String, Object> rowKey) {
        return rowKey.entrySet().stream().map(entry -> entry.getKey() + "=" + toStr(entry.getValue())).collect(Collectors.joining("&"));
    }

    public static boolean dataEqual(Object baseData, Object remoteData) {
        return StringUtils.equals(toStr(baseData), toStr(remoteData));
    }

    public static Map<String, Object> getDiffColumns(LinkedHashMap<String, Object> baseRow, LinkedHashMap<String, Object> remoteRow, List<String> ignoreColumns) {
        Assert.notNull(baseRow, "baseRow cannot be null");
        Assert.notNull(remoteRow, "remoteRow cannot be null");

        Map<String, Object> result = new HashMap<>(baseRow.size());
        for (Map.Entry<String, Object> baseEntry : baseRow.entrySet()) {
            String columnName = baseEntry.getKey();
            if (ignoreColumns != null && ignoreColumns.contains(columnName)) {
                continue;
            }
            Object baseData = baseEntry.getValue();
            Object remoteData = remoteRow.get(columnName);
            if (!dataEqual(baseData, remoteData)) {
                result.put(columnName, remoteData);
            }
        }

        return result;
    }
}
