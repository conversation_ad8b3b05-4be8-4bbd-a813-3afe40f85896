package com.yy.manager.hdztmanager.mapper;

import com.yy.manager.hdztmanager.entity.RankAdjust;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface RankAdjustDao extends BaseMapper<RankAdjust> {
    /**
     *
     * @param actId
     * @return
     */
    @Select("SELECT json_config FROM rank_adjust WHERE act_id = ${actId} ")
    String getConfig(long actId);
}
