package com.yy.manager.hdztmanager.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yy.manager.hdztmanager.entity.RoleAdjustRecord;
import com.yy.manager.hdztmanager.mapper.RoleAdjustRecordDao;
import com.yy.manager.hdztmanager.service.IRoleAdjustRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 角色调整记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
@Service
@DS("hdztmanager")
public class RoleAdjustRecordServiceImpl extends ServiceImpl<RoleAdjustRecordDao, RoleAdjustRecord> implements IRoleAdjustRecordService {
    @Resource
    private RoleAdjustRecordDao roleAdjustRecordDao;

    @Override
    public void insertOrUpdate(RoleAdjustRecord roleAdjustRecord) {
        //this.saveOrUpdate(roleAdjustRecord);
        roleAdjustRecordDao.insertOrUpdate(roleAdjustRecord);
    }
}
