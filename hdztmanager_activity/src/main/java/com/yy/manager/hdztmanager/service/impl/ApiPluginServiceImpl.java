package com.yy.manager.hdztmanager.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.manager.hdztmanager.bean.SqlFieldPropEnum;
import com.yy.manager.hdztmanager.service.IApiPluginService;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2022/11/23 11:08
 * @Modified:补充Author
 */
public class ApiPluginServiceImpl {
    @Component("ActTimeShowPlugin")
    public static class ActTimeShowPluginImpl implements IApiPluginService {

        @Override
        public Object mergeData(Object apiData, Object param) {
            JSONObject actTimeDataJson = (JSONObject) apiData;

            List<LinkedHashMap<String, Object>> phaseData = (List<LinkedHashMap<String, Object>>) actTimeDataJson.get("ranking_phase");
            List<LinkedHashMap<String, Object>> rankData = (List<LinkedHashMap<String, Object>>) actTimeDataJson.get("ranking_config");

            Map<String, LinkedHashMap<String, Object>> phaseGroupMap = Maps.newHashMap();
            LinkedHashMap<String, Object> rankPhaseMap;
            for(LinkedHashMap<String, Object> phaseDataElement:phaseData){
                String phaseGroupCode = (String) phaseDataElement.get("phase_group_code");
                if(phaseGroupCode==null){
                    phaseGroupCode = "";
                }
                rankPhaseMap = phaseGroupMap.getOrDefault(phaseGroupCode, Maps.newLinkedHashMap());
                if(phaseGroupMap.containsKey(phaseGroupCode)){
                    List<LinkedHashMap<String, Object>> phaseList = (List<LinkedHashMap<String, Object>>) rankPhaseMap.get("phase_list");
                    phaseList.add(phaseDataElement);
                    rankPhaseMap.put("phase_list", phaseList);
                } else{
                    rankPhaseMap.put("phase_group_code", phaseGroupCode);
                    rankPhaseMap.put("phase_list", Lists.newArrayList(phaseDataElement));
                    rankPhaseMap.put("rank_list", Lists.newArrayList());
                }
                phaseGroupMap.put(phaseGroupCode, rankPhaseMap);
            }
            List<LinkedHashMap<String, Object>> rankList;
            for(LinkedHashMap<String, Object> rankDataElement:rankData){
                String phaseGroupCode = (String) rankDataElement.get("phase_group_code");
                if(phaseGroupCode==null){
                    phaseGroupCode = "";
                }
                rankPhaseMap = phaseGroupMap.getOrDefault(phaseGroupCode, new LinkedHashMap<>());
                if(phaseGroupMap.containsKey(phaseGroupCode)){
                    rankList = (List<LinkedHashMap<String, Object>>) rankPhaseMap.get("rank_list");
                    rankList.add(rankDataElement);
                    rankPhaseMap.put("rank_list", rankList);
                } else{
                    rankPhaseMap.put("phase_group_code", phaseGroupCode);
                    rankPhaseMap.put("phase_list", Lists.newArrayList());
                    rankPhaseMap.put("rank_list", Lists.newArrayList(rankDataElement));
                }
                phaseGroupMap.put(phaseGroupCode, rankPhaseMap);
            }
            actTimeDataJson.remove("ranking_phase");
            actTimeDataJson.remove("ranking_config");
            for(Map.Entry<String, LinkedHashMap<String, Object>> entry : phaseGroupMap.entrySet()){
                LinkedHashMap<String, Object> value = entry.getValue();
                ((ArrayList<LinkedHashMap<String, Object>>)value.get("phase_list")).sort((a, b)->
                        ((Timestamp)a.get("begin_time")).compareTo((Timestamp) b.get("begin_time")));
                ((ArrayList<LinkedHashMap<String, Object>>)value.get("rank_list")).sort((a, b)->{
                    Timestamp time1 = (Timestamp) a.get("calc_begin_time");
                    Timestamp time2 = (Timestamp) b.get("calc_begin_time");
                    int result = time1.compareTo(time2);
                    if(result>0){
                        return 1;
                    } else if(result<0){
                        return -1;
                    } else{
                        Timestamp time3 = (Timestamp) a.get("calc_end_time");
                        Timestamp time4 = (Timestamp) b.get("calc_end_time");
                        return time3.compareTo(time4);
                    }
                }
                );
            }
            List<LinkedHashMap<String, Object>> rankPhaseList = Lists.newArrayList(phaseGroupMap.values());
            rankPhaseList.sort((a, b)-> {
                List<LinkedHashMap<String, Object>> listA = (ArrayList<LinkedHashMap<String, Object>>)a.get("phase_list");
                List<LinkedHashMap<String, Object>> listB = (ArrayList<LinkedHashMap<String, Object>>)b.get("phase_list");
                Timestamp time1 = (Timestamp)((LinkedHashMap<String, Object>)(listA.get(0))).get("begin_time");
                Timestamp time2 = (Timestamp)((LinkedHashMap<String, Object>)(listB.get(0))).get("begin_time");
                int result1 = time1.compareTo(time2);
                if(result1 > 0){
                    return 1;
                } else if(result1 < 0){
                    return -1;
                } else{
                    Timestamp time3 = (Timestamp)((LinkedHashMap<String, Object>)(listA.get(listA.size()-1))).get("end_time");
                    Timestamp time4 = (Timestamp)((LinkedHashMap<String, Object>)(listB.get(listB.size()-1))).get("end_time");
                    return time3.compareTo(time4);
                }
            }
            );
            actTimeDataJson.put("rank_phase", rankPhaseList);

            List<LinkedHashMap<String, Object>> awardData = (List<LinkedHashMap<String, Object>>) actTimeDataJson.get("award_task");
            awardData.sort((a, b)->{
                Timestamp time1 = (Timestamp) a.get("opentime");
                Timestamp time2 = (Timestamp) b.get("opentime");
                int result = time1.compareTo(time2);
                if(result>0){
                    return 1;
                } else if(result<0){
                    return -1;
                } else{
                    Timestamp time3 = (Timestamp) a.get("endtime");
                    Timestamp time4 = (Timestamp) b.get("endtime");
                    return time3.compareTo(time4);
                }
            });

            List<LinkedHashMap<String, Object>> hdztComponentAttr = (List<LinkedHashMap<String, Object>>) actTimeDataJson.get("hdzj_component_attr");

            Map<String, List<LinkedHashMap<String, Object>>> map = Maps.newHashMap();
            for(LinkedHashMap<String, Object> hdztComponentAttrElement : hdztComponentAttr){
                String key = (String) hdztComponentAttrElement.get("cmpt_id_index");
                if(map.containsKey(key)){
                    List<LinkedHashMap<String, Object>> value = map.get(key);
                    value.add(hdztComponentAttrElement);
                    map.put(key, value);
                } else{
                    map.put(key, Lists.newArrayList(hdztComponentAttrElement));
                }
            }
            actTimeDataJson.put("hdzj_component_attr", Lists.newArrayList(map.values()));
            return actTimeDataJson;
        }

        @Override
        public Object filterData(Object apiData, Object param) {
            List<HashMap<String, Object>> sqlFieldPropList = (List<HashMap<String, Object>>) param;
            List<LinkedHashMap<String, Object>> actTimeData = (List<LinkedHashMap<String, Object>>) apiData;

            List<LinkedHashMap<String, Object>> actTimefilteredData = actTimeData.stream().filter(dataMap -> isValid(dataMap, sqlFieldPropList))
                    .collect(Collectors.toList());

            return actTimefilteredData;
        }

        private boolean isValid(LinkedHashMap<String, Object> dataMap, List<HashMap<String, Object>> sqlFieldPropList) {
            for(HashMap<String, Object> sqlFieldProp:sqlFieldPropList){
                String name = (String) sqlFieldProp.get("name");
                Integer type = (Integer) sqlFieldProp.get("type");
                for(Map.Entry<String, Object> dataMapEntry:dataMap.entrySet()){
                    if(!name.equals(dataMapEntry.getKey())){
                        continue;
                    }
                    if(type.equals(SqlFieldPropEnum.hdzj_component_attr__value.getType())){
                        Pattern pattern = SqlFieldPropEnum.hdzj_component_attr__value.getPattern();
                        Boolean isValid = pattern.matcher(((String) dataMapEntry.getValue()).trim()).find();
                        if(!isValid){
                            return false;
                        }
                    }
                    break;
                }
            }
            return true;
        }

    }
}
