package com.yy.manager.hdztmanager.controller;


import cn.hutool.core.util.StrUtil;
import com.yy.manager.hdztmanager.service.ISysParameterService;
import com.yy.manager.utils.Convert;
import com.yy.manager.utils.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * name、clazz，value 3个字段值的组合必须唯一  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@RestController
@RequestMapping("/hdztmanager")
public class SysParameterController {

    public static final String CURRENT_ACT = "current_act";

    @Autowired
    private ISysParameterService sysParameterService;

    @GetMapping("/actIds")
    public Response<List<Long>> currentActIds() {
        List<Long> actIds = new ArrayList<>();
        String currentAct = sysParameterService.queryParameterValue(CURRENT_ACT);
        for (String act : currentAct.split( StrUtil.COMMA)) {
            actIds.add(Convert.toLong(act));
        }
        return  Response.success(actIds);
    }
}
