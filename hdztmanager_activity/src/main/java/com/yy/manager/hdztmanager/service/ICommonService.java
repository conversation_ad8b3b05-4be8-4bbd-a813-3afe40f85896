package com.yy.manager.hdztmanager.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.common.collect.Lists;
import com.yy.manager.hdztmanager.bean.CommonOpData;
import com.yy.manager.hdztmanager.entity.FieldInfo;
import com.yy.manager.hdztmanager.mapper.CommonMapper;
import com.yy.manager.utils.MySqlHelper;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.mapping.SqlCommandType;
import org.springframework.util.CollectionUtils;


import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 活动数据版本管理
 * @Date: 2021/11/23 12:41
 * @Modified:
 */
public interface ICommonService extends IService<Object> {

    /**
     * 通用删除
     *
     * @param tableName
     * @param params
     * @param isExecute
     * @return
     */
    Pair<List<String>, Integer> commonDeleteByMap(String tableName, List<Map<String, Object>> params, boolean isExecute);

    /**
     * 通用插入
     *
     * @param tableName
     * @param params
     * @param isExecute
     * @return
     */
    Pair<List<String>, Integer> commonInsertByMap(String tableName, List<Map<String, Object>> params, boolean isExecute);

    /**
     * 通用删除
     *
     * @param commonOpDataList
     * @param isExecute
     * @return
     */
    Pair<List<String>, Integer> commonDelete(List<CommonOpData> commonOpDataList, boolean isExecute);

    /**
     * 通用插入
     *
     * @param commonOpDataList
     * @param isExecute
     * @return
     */
    Pair<List<String>, Integer> commonInsert(List<CommonOpData> commonOpDataList, boolean isExecute);

    /**
     * 通用更新
     *
     * @param commonOpDataList
     * @param isExecute
     * @return
     */
    Pair<List<String>, Integer> commonUpdate(List<CommonOpData> commonOpDataList, boolean isExecute);

    /**
     * 生成指定操作对象
     * @param tableName
     * @param fieldInfoMap
     * @param srcConfigs
     * @param destConfigs
     * @param op
     * @return
     */
    List<CommonOpData> getDataOpMap(String tableName, LinkedHashMap<String, FieldInfo> fieldInfoMap, List<JSONObject> srcConfigs, List<JSONObject> destConfigs, SqlCommandType op);
    /**
     * 生成指定操作对象 （增删改）
     * @param tableName
     * @param fieldInfoMap
     * @param srcConfigs
     * @param destConfigs
     * @return
     */
    Map<SqlCommandType, List<CommonOpData>> getDataOpMap(String tableName, LinkedHashMap<String, FieldInfo> fieldInfoMap, List<JSONObject> srcConfigs, List<JSONObject> destConfigs);

}
