package com.yy.manager.hdztmanager.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@TableName("rank_adjust")
public class RankAdjust implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer actId;

    private String jsonConfig;

    public Integer getActId() {
        return actId;
    }

    public void setActId(Integer actId) {
        this.actId = actId;
    }
    public String getJsonConfig() {
        return jsonConfig;
    }

    public void setJsonConfig(String jsonConfig) {
        this.jsonConfig = jsonConfig;
    }

    @Override
    public String toString() {
        return "RankAdjust{" +
            "actId=" + actId +
            ", jsonConfig=" + jsonConfig +
        "}";
    }
}
