package com.yy.manager.hdztmanager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 角色调整记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
@TableName("role_adjust_record")
public class RoleAdjustRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long actId;

    private String memberId;

    private String srcRole;

    private String destRole;

    private String record;

    /**
     * 是否已执行 0-> 否 1->是
     */
    private Integer executed;

    private String executedResult;

    private LocalDateTime cTime;

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }
    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }
    public String getSrcRole() {
        return srcRole;
    }

    public void setSrcRole(String srcRole) {
        this.srcRole = srcRole;
    }
    public String getDestRole() {
        return destRole;
    }

    public void setDestRole(String destRole) {
        this.destRole = destRole;
    }
    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }
    public Integer getExecuted() {
        return executed;
    }

    public void setExecuted(Integer executed) {
        this.executed = executed;
    }
    public String getExecutedResult() {
        return executedResult;
    }

    public void setExecutedResult(String executedResult) {
        this.executedResult = executedResult;
    }
    public LocalDateTime getcTime() {
        return cTime;
    }

    public void setcTime(LocalDateTime cTime) {
        this.cTime = cTime;
    }

    @Override
    public String toString() {
        return "RoleAdjustRecord{" +
            "actId=" + actId +
            ", memberId=" + memberId +
            ", srcRole=" + srcRole +
            ", destRole=" + destRole +
            ", record=" + record +
            ", executed=" + executed +
            ", executedResult=" + executedResult +
            ", cTime=" + cTime +
        "}";
    }
}
