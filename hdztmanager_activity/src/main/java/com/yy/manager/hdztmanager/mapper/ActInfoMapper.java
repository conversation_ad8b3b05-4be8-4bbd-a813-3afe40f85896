package com.yy.manager.hdztmanager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2021-07-21
 */
public interface ActInfoMapper extends BaseMapper<Object> {


    /**
     *
     * @param name
     * @param value
     * @param remark
     * @return
     */
    @Insert("INSERT INTO ge_parameter (name, clazz, value, alias, shword, remark, created, modified) VALUES (${name}, '', ${value}, '', 0,${remark}, NOW(), NOW())")
    int insertGeParameter(String name,String value,String remark);

    /**
     *
     * @param actId
     * @param attrName
     * @param value
     * @param remark
     * @return
     */
    @Insert("INSERT INTO hdzt_activity_attr (act_id, attrname, attrvalue, remark, ctime, utime) VALUES (${actId}, ${attrName}, ${value}, ${remark}, NOW(), NOW()))")
    int insertHdztActivityAttr(long actId,String attrName,String value,String remark);



}
