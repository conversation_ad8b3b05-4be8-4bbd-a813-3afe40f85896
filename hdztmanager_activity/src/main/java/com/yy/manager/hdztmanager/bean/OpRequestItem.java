package com.yy.manager.hdztmanager.bean;

import org.apache.ibatis.mapping.SqlCommandType;

import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2021/12/15 16:12
 * @Modified:
 */
public class OpRequestItem {

    /**
     * 表名
     */
    private String table;
    /**
     * 操作类型 ，INSERT, UPDATE, DELETE
     */
    private SqlCommandType sqlCommandType;

    /**
     * 操作的参数-插入和更新的新对象
     */
    private Map<String, Object> newObject;

    /**
     * 原来的旧对象
     */
    private Map<String, Object> oldObject;

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public SqlCommandType getSqlCommandType() {
        return sqlCommandType;
    }

    public void setSqlCommandType(SqlCommandType sqlCommandType) {
        this.sqlCommandType = sqlCommandType;
    }

    public Map<String, Object> getNewObject() {
        return newObject;
    }

    public void setNewObject(Map<String, Object> newObject) {
        this.newObject = newObject;
    }

    public Map<String, Object> getOldObject() {
        return oldObject;
    }

    public void setOldObject(Map<String, Object> oldObject) {
        this.oldObject = oldObject;
    }
}
