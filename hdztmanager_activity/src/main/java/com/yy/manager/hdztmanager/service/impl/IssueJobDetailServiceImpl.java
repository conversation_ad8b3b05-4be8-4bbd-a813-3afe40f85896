package com.yy.manager.hdztmanager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yy.manager.hdzt.entity.IssueJobDetail;
import com.yy.manager.hdztmanager.mapper.IssueJobDetailDao;
import com.yy.manager.hdztmanager.service.IIssueJobDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class IssueJobDetailServiceImpl extends ServiceImpl<IssueJobDetailDao, IssueJobDetail> implements IIssueJobDetailService {

    @Resource
    private IssueJobDetailDao issueJobDetailDao;

    @Override
    public List<IssueJobDetail> queryIssueJobDetail(long actId, Long jobId, Integer state, Long uid, Date startTime, Date endTime, int offset, int limit) {
        return issueJobDetailDao.queryIssueJobDetail(actId, jobId, uid, state, startTime, endTime, offset, limit);
    }

    @Override
    public int pageListCount(long actId, Long jobId, Integer state, Long uid, Date startTime, Date endTime) {
        return issueJobDetailDao.pageListCount(actId, jobId, uid, state, startTime, endTime);
    }

    @Override
    public int batchInsertOrUpdate(List<IssueJobDetail> issueJobDetails) {
        return issueJobDetailDao.batchInsertOrUpdate(issueJobDetails);
    }
}
