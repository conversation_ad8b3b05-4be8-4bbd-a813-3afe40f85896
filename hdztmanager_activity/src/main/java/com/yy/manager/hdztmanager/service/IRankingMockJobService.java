package com.yy.manager.hdztmanager.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yy.manager.hdzt.entity.RankMockJobVo;

import java.util.List;


public interface IRankingMockJobService extends IService<RankMockJobVo> {

    List<RankMockJobVo> queryRecentJob(long actId, int status, String startDate);

    List<RankMockJobVo> queryRecentJob(long actId);

    int insertOrUpdate(RankMockJobVo rankMockJobVo);
}
