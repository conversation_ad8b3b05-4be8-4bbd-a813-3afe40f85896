package com.yy.manager.hdztmanager.controller;


import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.manager.hdztmanager.entity.ActConfigVersion;
import com.yy.manager.hdztmanager.service.IActConfigVersionService;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: CXZ
 * @Desciption: 活动数据版本管理
 * @Date: 2021/11/23 12:41
 * @Modified:
 */
@RestController
@RequestMapping("/hdztmanager/actConfigVersion")
public class ActConfigVersionServiceController {

    @Autowired
    private IActConfigVersionService actConfigVersionService;

    @ApiOperation("查询配置列表")
    @PreAuthorize("@ss.hasPermi('activity:actConfigVersion:list')")
    @GetMapping("list")
    public Response<List<ActConfigVersion>> list(Long actId, String dataBase) {
        return Response.success(actConfigVersionService.selectActConfigVersionsNoConfig(actId, dataBase));
    }
    @ApiOperation("备份配置")
    @Log(title = "备份配置", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('activity:actConfigVersion:backup')")
    @GetMapping("backup")
    public Response<Long> backup(Long actId, String dataBase, String desc) {
        return actConfigVersionService.backup(actId, dataBase, desc);
    }
    @ApiOperation("对比配置")
    @PreAuthorize("@ss.hasPermi('activity:actConfigVersion:query')")
    @GetMapping("compare")
    public Response<List<ActConfigVersion>> compare(Long actId, String dataBase, long id1, long id2) {
        return actConfigVersionService.compare(actId, dataBase, id1, id2);
    }
}
