package com.yy.manager.hdztmanager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import java.util.List;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2022/11/23 11:08
 * @Modified:补充Author
 */
@TableName("api_config")
public class ApiData {
    @TableId
    private Integer id;

    private String path;

    private String remark;

    private Date cTime;

    private Date uTime;

    @TableField(exist = false)
    private List<Integer> sqlIdList;

    public List<Integer> getSqlIdList() {
        return sqlIdList;
    }

    public void setSqlIdList(List<Integer> sqlIdList) {
        this.sqlIdList = sqlIdList;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getcTime() {
        return cTime;
    }

    public void setcTime(Date cTime) {
        this.cTime = cTime;
    }

    @Override
    public String toString() {
        return "ApiData{" +
                "id=" + id +
                ", path='" + path + '\'' +
                ", remark='" + remark + '\'' +
                ", cTime=" + cTime +
                ", uTime=" + uTime +
                ", sqlList=" + sqlIdList +
                '}';
    }

    public Date getuTime() {
        return uTime;
    }

    public void setuTime(Date uTime) {
        this.uTime = uTime;
    }
}
