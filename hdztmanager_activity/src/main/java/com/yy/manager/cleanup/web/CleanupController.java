package com.yy.manager.cleanup.web;

import com.alibaba.fastjson.JSON;
import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.common.utils.SysEnvHelper;
import com.yy.manager.cleanup.bean.ResetCommand;
import com.yy.manager.cleanup.bean.ResetCommandVo;
import com.yy.manager.cleanup.service.CleanCommandService;
import com.yy.manager.utils.CommonBaseRspVo;
import com.yy.manager.utils.JsonUtil;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021.06.11 11:16
 */
@RestController
@RequestMapping("/cleanup")
public class CleanupController {
    private Logger logger = LoggerFactory.getLogger(CleanupController.class);

    @Autowired
    private CleanCommandService cleanCommandService;

    @ApiOperation("增加活动恢复指令")
    @Log(title = "增加活动恢复指令", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('activity:command:add')")
    @RequestMapping("/commandListAdd")
    public String commandListAdd(HttpServletRequest request, @RequestBody ResetCommand resetCommand) {
        if (SysEnvHelper.isDeploy()) {
            return JsonUtil.makeJsonResult("1", "正式环境暂不支持", "");
        }
        cleanCommandService.commandListAdd(resetCommand.getActId(), resetCommand.getCommandName(), resetCommand.getCommandList());
        return JSON.toJSONString(new CommonBaseRspVo<>().success("ok"));
    }

    @ApiOperation("删除活动恢复指令")
    @Log(title = "删除活动恢复指令", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('activity:command:del')")
    @RequestMapping("/delCommand.do")
    public String delCommand(long id) {
        cleanCommandService.delCommand(id);
        return "ok";
    }

    @ApiOperation("执行活动恢复指令")
    @Log(title = "执行活动恢复指令", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('activity:command:exc')")
    @RequestMapping(value = "/excCommand.do")
    public Response<String> excCommand(HttpServletRequest request, Long actId, Long command) {
        //参数处理
        Map<String, String> paramMap = request.getParameterMap().keySet().stream()
                .collect(Collectors.toMap(name -> name, name -> request.getParameter(name)));

        return cleanCommandService.extCleanCommandList(actId, command, paramMap);
    }

    @ApiOperation("查询活动恢复指令")
    @PreAuthorize("@ss.hasPermi('activity:command:query')")
    @RequestMapping("/queryCommandListById.do")
    public List<ResetCommand> queryCommandListById(HttpServletRequest request, @RequestParam(defaultValue = "0") long id) {
        return cleanCommandService.commandListById(id);
    }

    @ApiOperation("查询活动恢复指令列表")
    @PreAuthorize("@ss.hasPermi('activity:command:query')")
    @RequestMapping("/queryCommandListByActId.do")
    public List<ResetCommandVo> queryCommandListByActId(HttpServletRequest request, @RequestParam(defaultValue = "0") long actId) {
        return cleanCommandService.queryCommandListByActId(actId);
    }


}
