package com.yy.manager.cleanup.service;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021.06.10 14:27
 */
@Service
public class CleanUpService {
    private Logger logger = LoggerFactory.getLogger(CleanUpService.class);

    public boolean delHashKey(RedisTemplate redisTemplate, String hash, Object... key) {
        Long de = redisTemplate.opsForHash().delete(hash, key);
        logger.info("cleanup delHashKey hash:{}. key:{}, delSize:{}", hash, key, de);
        return true;
    }

    public boolean delZsetKey(RedisTemplate redisTemplate, String name, Object... key) {
        Long remove = redisTemplate.opsForZSet().remove(name, key);
        logger.info("cleanup delZsetKey name:{}. key:{}, delSize:{}", name, key, remove);
        return true;
    }

    private void delKey(RedisTemplate redisTemplate, Collection<String> key) {
        redisTemplate.delete(key);
    }

    public void delKey(RedisTemplate redisTemplate, String key) {
        redisTemplate.delete(key);
    }

    public void delFuzzyKey(RedisTemplate redisTemplate, String key) {
        Set<String> keys = redisTemplate.keys(key);
        if (!CollectionUtils.isEmpty(keys)) {
            logger.info("del main key:{}", JSON.toJSONString(keys));
            delKey(redisTemplate, keys);
        }
    }

    public void delMysqlData(JdbcTemplate jdbcTemplate, String tableName, String afterWhere) {
        if (StringUtils.isBlank(afterWhere)) {
            logger.warn("delMysqlData afterWhere is blank, do nothing");
            return;
        }
        String sql = "delete from %s where %s";
        jdbcTemplate.execute(String.format(sql, tableName, afterWhere));
    }

    public void delFuzzyHashKey(RedisTemplate redisTemplate, String hash, String fuzzyKey) {
        try {

            ScanOptions build = ScanOptions.scanOptions().match(fuzzyKey).build();
            Cursor<Map.Entry<Object, Object>> cursor = redisTemplate.opsForHash().scan(hash, build);
            List<String> keys = new ArrayList<>();
            while (cursor.hasNext()) {
                Map.Entry<Object, Object> next = cursor.next();
                Object key = next.getKey();
                Object valueSet = next.getValue();
                logger.info("opsForHash().scan result, key:{}, value:{}", key, valueSet);

                keys.add(String.valueOf(key));
            }
            //关闭cursor
            cursor.close();
            if (!CollectionUtils.isEmpty(keys)) {
                delHashKey(redisTemplate, hash, keys.toArray());
            }
        } catch (Exception e) {
            logger.error("delFuzzyHashKey error", e);
        }
    }

    public void delFuzzyZsetKey(RedisTemplate redisTemplate, String zsetKey, String fuzzyKey) {
        try {
            ScanOptions build = ScanOptions.scanOptions().match(fuzzyKey).build();
            Cursor<ZSetOperations.TypedTuple<String>> cursor = redisTemplate.opsForZSet().scan(zsetKey, build);
            List<String> keys = new ArrayList<>();
            while (cursor.hasNext()) {
                ZSetOperations.TypedTuple<String> next = cursor.next();
                Object key = next.getValue();
                Object value = next.getScore();
                logger.info("opsForHash().scan result, key:{}, value:{}", key, value);
                keys.add(String.valueOf(key));
            }
            //关闭cursor
            cursor.close();
            if (!CollectionUtils.isEmpty(keys)) {
                delZsetKey(redisTemplate, zsetKey, keys.toArray());
            }
        } catch (Exception e) {
            logger.error("delFuzzyZsetKey error", e);
        }
    }

    public void delFuzzySetKey(RedisTemplate redisTemplate, String zsetKey, String fuzzyKey) {
        try {
            ScanOptions build = ScanOptions.scanOptions().match(fuzzyKey).build();
            Cursor<String> cursor = redisTemplate.opsForSet().scan(zsetKey, build);

            List<String> keys = new ArrayList<>();
            while (cursor.hasNext()) {
                Object key = cursor.next();
                logger.info("opsForHash().scan result, key:{}", key);
                keys.add(String.valueOf(key));
            }
            //关闭cursor
            cursor.close();
            if (!CollectionUtils.isEmpty(keys)) {
                delSetKey(redisTemplate, zsetKey, keys.toArray());
            }
        } catch (Exception e) {
            logger.error("delFuzzySetKey error", e);
        }
    }

    public void delSetKey(RedisTemplate redisTemplate, String setName, Object... key) {
        redisTemplate.opsForSet().remove(setName, key);
    }

}
