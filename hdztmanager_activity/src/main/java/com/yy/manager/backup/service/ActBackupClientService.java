package com.yy.manager.backup.service;

import com.alibaba.fastjson.JSON;
import com.yy.manager.backup.yrpc.BackupServerService;
import com.yy.manager.backup.yrpc.HdptDataBackup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ActBackupClientService {

    @Reference(owner = "platform_activity_backup", protocol = "yrpc", registry = "reg-yrpc", timeout = 5 * 60 * 1000)
    private BackupServerService backupServerService;

    public HdptDataBackup.BackupResponse backup(String seq, long configId, String sourceName, String sourceUri) {
        HdptDataBackup.DataSource dataSource = HdptDataBackup.DataSource.newBuilder()
                .setSourceId(configId)
                .setName(sourceName)
                .setSourceType(HdptDataBackup.SourceType.redis)
                .setSourceUri(sourceUri)
                .build();
        HdptDataBackup.BackupRequest req = HdptDataBackup.BackupRequest.newBuilder()
                .setSeq(seq)
                .setDataSource(dataSource)
                .build();

        HdptDataBackup.BackupResponse resp = backupServerService.backup(req);
        log.info("ActBackupClientService backup with req:{}, resp:{}", req, resp);
        return resp;
    }

    public HdptDataBackup.SimpleResponse sink(String seq, String sourceUri, int storageType, String backupPath, String fileKey, long fenceTime, boolean override, List<String> clearPatterns) {
        HdptDataBackup.DataSource sinkToDs = HdptDataBackup.DataSource.newBuilder()
                .setSourceType(HdptDataBackup.SourceType.redis)
                .setSourceUri(sourceUri)
                .build();
        HdptDataBackup.BackupResource resource = HdptDataBackup.BackupResource.newBuilder()
                .setStorageType(storageType)
                .setStoragePath(backupPath)
                .setFileKey(fileKey)
                .setFenceTime(fenceTime)
                .build();
        String clearKeyPatterns = CollectionUtils.isEmpty(clearPatterns) ? "[]" : JSON.toJSONString(clearPatterns);
        HdptDataBackup.SinkRequest req = HdptDataBackup.SinkRequest.newBuilder()
                .setSeq(seq)
                .setResource(resource)
                .setDataSource(sinkToDs)
                .putExtend("args", override ? "-r" : StringUtils.EMPTY)
                .putExtend("clearKeyPatterns", clearKeyPatterns)
                .build();

        HdptDataBackup.SimpleResponse resp = backupServerService.sink(req);
        log.info("ActBackupClientService sink with req:{}, resp:{}", req, resp);

        return resp;
    }
}
