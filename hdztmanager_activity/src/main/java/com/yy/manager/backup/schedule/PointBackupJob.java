package com.yy.manager.backup.schedule;

import com.yy.manager.backup.service.ActBackupService;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.service.IHdztActivityService;
import com.yy.manager.utils.SysEnvHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@Profile({"prod", "test"})
public class PointBackupJob {

    @Autowired
    private ActBackupService actBackupService;

    @Autowired
    private IHdztActivityService hdztActivityService;

    @Scheduled(initialDelay = 500, fixedRate = 15000)
    public void actTimePointBackup1() {
        List<HdztActivity> effectActInfos = hdztActivityService.selectAllEffectActivity(1);
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(HdztActivity::getActId).map(Long::valueOf).collect(Collectors.toList());

        doBackupActByTimePoint(1, effectActIds);
    }

    @Scheduled(initialDelay = 3500, fixedRate = 15000)
    public void actTimePointBackup2() {
        List<HdztActivity> effectActInfos = hdztActivityService.selectAllEffectActivity(2);
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(HdztActivity::getActId).map(Long::valueOf).collect(Collectors.toList());

        doBackupActByTimePoint(2, effectActIds);
    }

    @Scheduled(initialDelay = 6500, fixedRate = 15000)
    public void actTimePointBackup3() {
        List<HdztActivity> effectActInfos = hdztActivityService.selectAllEffectActivity(3);
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(HdztActivity::getActId).map(Long::valueOf).collect(Collectors.toList());

        doBackupActByTimePoint(3, effectActIds);
    }

    @Scheduled(initialDelay = 9500, fixedRate = 15000)
    public void actTimePointBackup4() {
        List<HdztActivity> effectActInfos = hdztActivityService.selectAllEffectActivity(4);
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(HdztActivity::getActId).map(Long::valueOf).collect(Collectors.toList());

        doBackupActByTimePoint(4, effectActIds);
    }

    @Scheduled(initialDelay = 12500, fixedRate = 15000)
    public void actTimePointBackup5() {
        List<HdztActivity> effectActInfos = hdztActivityService.selectAllEffectActivity(5);
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(HdztActivity::getActId).map(Long::valueOf).collect(Collectors.toList());

        doBackupActByTimePoint(5, effectActIds);
    }

    @Scheduled(initialDelay = 15500, fixedRate = 15000)
    public void actTimePointBackup6() {
        List<HdztActivity> effectActInfos = hdztActivityService.selectAllEffectActivity(6);
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(HdztActivity::getActId).map(Long::valueOf).collect(Collectors.toList());

        doBackupActByTimePoint(6, effectActIds);
    }

    @Scheduled(initialDelay = 17500, fixedRate = 15000)
    public void actTimePointBackup7() {
        List<HdztActivity> effectActInfos = hdztActivityService.selectAllEffectActivity(7);
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(HdztActivity::getActId).map(Long::valueOf).collect(Collectors.toList());

        doBackupActByTimePoint(7, effectActIds);
    }

    private void doBackupActByTimePoint(int group, List<Long> actIds) {
        String curTime = DateFormatUtils.format(new Date(), "yyyyMMddHHmm");
        log.info("doBackupActByTimePoint with group:{}, curTime:{}, actIds:{}", group, curTime, actIds);
        Collections.shuffle(actIds);

        if (SysEnvHelper.isDeploy()) {
            for (long actId : actIds) {
                actBackupService.backupByTimePointConfig(actId, curTime);
            }
        }

    }
}
