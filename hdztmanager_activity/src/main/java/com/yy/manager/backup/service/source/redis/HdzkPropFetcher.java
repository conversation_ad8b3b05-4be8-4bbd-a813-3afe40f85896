package com.yy.manager.backup.service.source.redis;

import com.yy.manager.backup.service.source.PropFetcher;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class HdzkPropFetcher implements PropFetcher {

    @Resource(name = "redisEcology1Properties")
    private RedisProperties redisEcology1Properties;

    @Resource(name = "redisEcology2Properties")
    private RedisProperties redisEcology2Properties;

    @Resource(name = "redisEcology3Properties")
    private RedisProperties redisEcology3Properties;

    @Resource(name = "redisEcology4Properties")
    private RedisProperties redisEcology4Properties;

    @Resource(name = "redisEcology5Properties")
    private RedisProperties redisEcology5Properties;

    @Override
    public RedisProperties getRedisProps(String groupCode) {
        switch (groupCode) {
            case "1":
                return redisEcology2Properties;
            case "2":
                return redisEcology3Properties;
            case "3":
                return redisEcology4Properties;
            case "4":
                return redisEcology5Properties;
            default:
                return redisEcology1Properties;
        }
    }
}
