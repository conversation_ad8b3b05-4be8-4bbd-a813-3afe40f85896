package com.yy.manager.backup.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.google.common.primitives.Longs;
import com.yy.common.utils.SysEnvHelper;
import com.yy.manager.backup.service.ActBackupService;
import com.yy.manager.commonservice.CommonService;
import com.yy.manager.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@Profile("!dev")
public class HdztEventConsumer {

    @Autowired
    private ActBackupService actBackupService;

    @Autowired
    private CommonService commonService;

    @Autowired
    @Qualifier("redisEcologydb1")
    private RedisTemplate redisEcologydb1;

    private static final Set<Long> ACCEPTABLE_URI = ImmutableSet.of(1001L, 1002L, 1003L, 1004L, 1005L, 1006L, 1007L, 1008L);

    @KafkaListener(containerFactory = "hdztWxContainerFactory", topicPattern = "hdzt[1-6]_ranking_events_topic", groupId = "hdpt_manager_bak")
    @KafkaListener(containerFactory = "hdztSzContainerFactory", topicPattern = "hdzt[1-6]_ranking_events_topic", groupId = "hdpt_manager_bak")
    @KafkaListener(containerFactory = "hdzt7WxContainerFactory", topics = "hdzt7_ranking_events_topic", groupId = "hdpt_manager_bak")
    @KafkaListener(containerFactory = "hdzt7SzContainerFactory", topics = "hdzt7_ranking_events_topic", groupId = "hdpt_manager_bak")
    public void onHdztMessageEvents(ConsumerRecord<String, String> consumerRecord) {
        String topic = consumerRecord.topic();
        String payload = consumerRecord.value();
        int pos = payload.indexOf("|");
        if (pos == -1) {
            log.error("onMessage fail@from:{}, invalid message format, pos:{}, payload:{}", topic, pos, payload);
            return;
        }

        log.info("onMessage info @from:{}, message format, pos:{}, payload:{}", topic, pos, payload);
        long uri = Optional.ofNullable(Longs.tryParse(payload.substring(0, pos))).orElse(-1L);
        if (!ACCEPTABLE_URI.contains(uri)) {
            log.info("onHdztMessageEvents handle unacceptable uri:{}", uri);
            return;
        }

        String data = payload.substring(pos + 1);
        JSONObject event = JSON.parseObject(data);
        long actId = event.getLongValue("actId");
        long rankId = event.getLongValue("rankId");
        long phaseId = event.getLongValue("phaseId");
        String ekey = event.getString("ekey");
        String seq = event.getString("seq");

        //灰度的时候不要备份无用的数据
        if (SysEnvHelper.isDeploy() && commonService.isGrey(actId)) {
            return;
        }

        //TODO:消息去重
        String key = "act:" + actId + ":hdpt_manager_back:ranking_event:" + uri;
        String field = StringUtils.isEmpty(ekey) ? seq : ekey;
        String value = DateUtil.today();
        Boolean rs = redisEcologydb1.opsForHash().putIfAbsent(key, field, value);
        if (rs == null || !rs) {
            log.info("onHdztMessageEvents duplicated message handled payload:{}", payload);
            return;
        }

        redisEcologydb1.expire(key, 200, TimeUnit.DAYS);
        //执行备份
        actBackupService.backupByRankingEvents(actId, uri, rankId, phaseId);
    }
}
