/**
 * Autogenerated based on backup_activity_sink_record by My<PERSON>is Plugin.
 * 
 * DO NOT EDIT UNLESS YOU ARE A GREEN HAND.
 */
package com.yy.manager.backup.entity;

import com.yy.manager.backup.enums.SinkState;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class BackupActivitySinkRecord {
    private Long id;

    private Long recordId;

    private String sinkSourceUri;

    private String args;

    private String remark;

    private Date startTime;

    private Date endTime;

    private SinkState sinkState;

    private String failMsg;

    private Date createTime;
}