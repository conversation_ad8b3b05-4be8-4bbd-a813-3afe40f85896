/*
 * Autogenerated based on backup_activity_config by <PERSON><PERSON><PERSON> Plugin.
 *
 * DO NOT EDIT UNLESS YOU ARE A GREEN HAND.
 */
package com.yy.manager.backup.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yy.manager.backup.entity.BackupActivityConfig;
import com.yy.manager.backup.enums.BackupType;
import com.yy.manager.clear.bean.DataSourceEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("hdztmanager")
public interface BackupActivityConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BackupActivityConfig record);

    int insertSelective(BackupActivityConfig record);

    BackupActivityConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BackupActivityConfig record);

    int updateByPrimaryKey(BackupActivityConfig record);

    List<BackupActivityConfig> selectByActMessage(@Param("actId") long actId, @Param("uri") long uri, @Param("rankId") Long rankId, @Param("phaseId") Long phaseId);

    List<BackupActivityConfig> selectByTimerConfig(@Param("actId") long actId, @Param("backupTime") String backupTime);

    List<BackupActivityConfig> selectBackupConfigList(@Param("actId") Long actId, @Param("backType") BackupType backupType, @Param("backupSource") DataSourceEnum backupSource, @Param("offset") int offset, @Param("length") int length);

    int selectBackupConfigCount(@Param("actId") Long actId, @Param("backType") BackupType backupType, @Param("backupSource") DataSourceEnum backupSource);
}