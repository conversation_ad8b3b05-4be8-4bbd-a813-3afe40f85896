package com.yy.manager.backup.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.yy.manager.backup.entity.BackupActivityConfig;
import com.yy.manager.backup.entity.BackupActivityRecord;
import com.yy.manager.backup.entity.BackupActivitySinkRecord;
import com.yy.manager.backup.enums.BackupState;
import com.yy.manager.backup.enums.BackupType;
import com.yy.manager.backup.enums.SinkState;
import com.yy.manager.backup.mapper.BackupActivityConfigMapper;
import com.yy.manager.backup.mapper.BackupActivityRecordMapper;
import com.yy.manager.backup.mapper.BackupActivitySinkRecordMapper;
import com.yy.manager.backup.service.source.SourcePropSupport;
import com.yy.manager.backup.vo.BackupResultVo;
import com.yy.manager.backup.vo.DataSource;
import com.yy.manager.backup.yrpc.BackupServerService;
import com.yy.manager.backup.yrpc.HdptDataBackup;
import com.yy.manager.clear.bean.DataSourceEnum;
import com.yy.manager.hdzt.entity.HdztActivityAttr;
import com.yy.manager.hdzt.service.IHdztActivityAttrService;
import com.yy.manager.hdztmanager.service.ISysParameterService;
import com.yy.manager.utils.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActBackupService {

    private static final String SCHEDULE_BACKUP_ATTR = "schedule_backup_source_";

    private static final String SCHEDULE_BACKUP_LOCK = "act:%d:schedule_timer_lock";

    private static final Map<DataSourceEnum, List<String>> KEY_PATTERNS = ImmutableMap.of(
            DataSourceEnum.Ecology, ImmutableList.of("act:%d:.*"),
            DataSourceEnum.ZTRanking, ImmutableList.of("act:%d:.*", "hdzt_ranking:%d:.*"),
            DataSourceEnum.ZTAward, Collections.emptyList(),
            DataSourceEnum.Stream, Collections.emptyList());

    @Autowired
    private BackupActivityConfigMapper backupActivityConfigMapper;

    @Autowired
    private BackupActivityRecordMapper backupActivityRecordMapper;

    @Autowired
    private BackupActivitySinkRecordMapper backupActivitySinkRecordMapper;

    @Autowired
    private SourcePropSupport sourcePropSupport;

    @Autowired
    @Qualifier("redisEcologydb1")
    private RedisTemplate redisEcologydb1;

    @Autowired
    @Qualifier("backupExecutor")
    private Executor backupExecutor;

    @Autowired
    private IHdztActivityAttrService hdztActivityAttrService;

    @Autowired
    private ActBackupClientService actBackupClientService;

    @Autowired
    private ISysParameterService sysParameterService;

    @Reference(owner = "platform_activity_backup", protocol = "yrpc", registry = "reg-yrpc", timeout = 5 * 60 * 1000)
    private BackupServerService backupServerService;

    public void backupByRankingEvents(long actId, long uri, long rankId, long phaseId) {
        List<BackupActivityConfig> backupConfigs = backupActivityConfigMapper.selectByActMessage(actId, uri, rankId, phaseId);
        boolean actEnd = uri == 1002;
        if (CollectionUtils.isEmpty(backupConfigs)) {
            if (actEnd) {
                BackupActivityConfig config = new BackupActivityConfig();
                config.setId(0L);
                config.setActId(actId);
                config.setUri((int) uri);
                config.setRankId((int) rankId);
                config.setPhaseId((int) phaseId);
                config.setBackupType(BackupType.event);
                for (DataSourceEnum sourceType : DataSourceEnum.values()) {
                    config.setBackupSource(sourceType);
                    doBackup(config);
                }
            }

            return;
        }

        for (BackupActivityConfig config : backupConfigs) {
            doBackup(config);
        }
    }

    public void backupByTimePointConfig(long actId, String curTime) {
        Boolean rs = redisEcologydb1.opsForValue().setIfAbsent("act-time-point-backup:" + actId + ":" + curTime, curTime, 2, TimeUnit.MINUTES);
        if (rs == null || !rs) {
            log.warn("backupByTimerConfig acquire lock fail actId:{}, curTime:{}", actId, curTime);
            return;
        }

        List<BackupActivityConfig> backupConfigs = backupActivityConfigMapper.selectByTimerConfig(actId, curTime);
        if (CollectionUtils.isEmpty(backupConfigs)) {
            return;
        }

        for (BackupActivityConfig config : backupConfigs) {
            doBackup(config);
        }
    }

    public void backupByScheduleTimer(long actId) {
        Boolean lock = redisEcologydb1.opsForValue().setIfAbsent(String.format(SCHEDULE_BACKUP_LOCK, actId), "1", 10, TimeUnit.MINUTES);
        if (lock == null || !lock) {
            log.warn("backupByScheduleTimer acquire lock fail");
            return;
        }

        for (DataSourceEnum sourceType : DataSourceEnum.values()) {
            boolean backup = true;
            HdztActivityAttr attr = hdztActivityAttrService.queryActAttr(actId, SCHEDULE_BACKUP_ATTR + sourceType.getCode());
            if (attr == null) {
                if (sourceType != DataSourceEnum.Ecology && sourceType != DataSourceEnum.ZTRanking) {
                    continue;
                }
            } else {
                String attrValue = attr.getAttrvalue();
                backup = BooleanUtils.toBoolean(attrValue);
            }

            if (backup) {
                BackupActivityConfig config = new BackupActivityConfig();
                config.setId(0L);
                config.setActId(actId);
                config.setBackupSource(sourceType);
                config.setBackupType(BackupType.timer);
                List<String> keyPatterns = KEY_PATTERNS.get(sourceType);
                if (CollectionUtils.isEmpty(keyPatterns)) {
                    config.setKeyPatterns(keyPatterns);
                } else {
                    List<String> patterns = keyPatterns.stream().map(pattern -> String.format(pattern, actId)).collect(Collectors.toList());
                    config.setKeyPatterns(patterns);
                }
                try {
                    doBackup(config);

                    //延迟五秒再继续执行
                    Thread.sleep(5000);
                } catch (Exception e) {
                    log.info("backupByScheduleTimer doBackup fail:", e);
                }
            }
        }
    }

    /**
     * ranking_config中配置的分组也要备份
     * @param actId
     * @param configId
     * @param recordId
     * @param sourceType
     */
    public void backupExtraGroupIfNecessary(long actId, long configId, long recordId, DataSourceEnum sourceType) {
        Set<String> extraGroupCode = sourcePropSupport.extraGroupCode(actId, sourceType);
        if (CollectionUtils.isEmpty(extraGroupCode)) {
            return;
        }

        for (String groupCode : extraGroupCode) {
            String sourceUri = sourcePropSupport.sourceUri(sourceType, groupCode);
            doBackup(configId, actId, BackupType.extra, sourceType, groupCode, sourceUri, "[extra:" + actId + ':' + recordId + ']');
        }
    }

    public Response<Long> backup(long configId) {
        BackupActivityConfig config = backupActivityConfigMapper.selectByPrimaryKey(configId);
        if (config == null) {
            return Response.fail(400, "Invalid configId");
        }

        final long actId = config.getActId();
        final DataSourceEnum sourceType = config.getBackupSource();
        String groupCode = sourcePropSupport.actGroupCode(actId, sourceType);
        String sourceUri = sourcePropSupport.sourceUri(actId, sourceType);
        if (StringUtils.isEmpty(sourceUri)) {
            log.error("doBackup fail with actId:{}, source:{}, groupCode:{}, sourceUri:{}", actId, sourceType, groupCode, sourceUri);
            return Response.fail(420, "could not fetch back up source uri");
        }

        final String sourceUri01 = addSourceUriQueryIfNecessary(sourceUri, config.getKeyPatterns(), config.getDatabases());

        final long recordId = this.addBackupRecord(configId, actId, BackupType.manual, sourceType, groupCode, sourceUri01, "[manual]");

        if (recordId <= 0) {
            log.warn("doBackup fail addBackupRecord fail");
            return Response.fail(500, "could not add back up record");
        }

        final String sourceName = "No." + groupCode + "套" + config.getBackupSource().getDesc();
        backupExecutor.execute(() -> {
            try {
                HdptDataBackup.BackupResponse resp = actBackupClientService.backup("act:redis:backup:" + recordId, config.getId(), sourceName, sourceUri01);
                updateBackupRecord(recordId, resp, null);
            } catch (Throwable e) {
                log.error("backup with recordId:{} exception:", recordId, e);
                updateBackupRecord(recordId, null, e);
            }
        });

        return Response.success(recordId);
    }

    public Response<BackupResultVo> queryBackupResult(long recordId) {
        BackupActivityRecord record = backupActivityRecordMapper.selectByPrimaryKey(recordId);
        if (record == null) {
            return Response.fail(400, "record not exist");
        }

        BackupResultVo result = new BackupResultVo();
        result.setBackupState(record.getBackupState());
        if (record.getBackupState() == BackupState.completed) {
            result.setKey(record.getFileKey());
            result.setPath(record.getBackupPath());
            result.setSourceAddress(record.getSourceNode());
            result.setFileSize(record.getFileSize());
        } else {
            result.setFailMsg(record.getFailMsg());
        }

        return Response.success(result);
    }

    private void doBackup(BackupActivityConfig config) {
        final long actId = config.getActId();
        final DataSourceEnum sourceType = config.getBackupSource();
        String groupCode = sourcePropSupport.actGroupCode(actId, sourceType);
        String sourceUri = sourcePropSupport.sourceUri(actId, sourceType);
        if (StringUtils.isEmpty(sourceUri)) {
            log.error("doBackup fail with actId:{}, source:{}, groupCode:{}, sourceUri:{}", actId, sourceType, groupCode, sourceUri);
            return;
        }

        //
        sourceUri = addSourceUriQueryIfNecessary(sourceUri, config.getKeyPatterns(), config.getDatabases());

        String extraMark;
        if (config.getBackupType() == BackupType.event) {
            extraMark = "[event:" + actId + '|' + config.getUri() + '|' + config.getRankId() + '|' + config.getPhaseId() + ']';
        } else if (config.getBackupType() == BackupType.point) {
            extraMark = "[point:" + actId + '|' + config.getBackupTime() + ']';
        } else {
            extraMark = "[timer:" + actId + "]";
        }

        doBackup(config.getId(), actId, config.getBackupType(), sourceType, groupCode, sourceUri, extraMark);
    }

    private void doBackup(long configId, long actId, BackupType backupType, DataSourceEnum sourceType, String groupCode, String sourceUri, String extraMark) {
        String sourceName = "No." + groupCode + "套" + sourceType.getDesc();

        long recordId = this.addBackupRecord(configId, actId, backupType, sourceType, groupCode, sourceUri, extraMark);

        if (recordId <= 0) {
            log.warn("doBackup fail addBackupRecord fail");
            return;
        }

        boolean success = false;
        try {
            HdptDataBackup.BackupResponse resp = actBackupClientService.backup("act:redis:backup:" + recordId, configId, sourceName, sourceUri);
            updateBackupRecord(recordId, resp, null);
            success = true;
        } catch (Throwable e) {
            log.error("doBackup with recordId:{} exception:", recordId, e);
            updateBackupRecord(recordId, null, e);
        }

        if (backupType != BackupType.extra && success) {
            backupExtraGroupIfNecessary(actId, configId, recordId, sourceType);
        }
    }

    private static String addSourceUriQueryIfNecessary(String sourceUri, List<String> keyPatterns, List<String> databases) {
        List<String> query = new ArrayList<>(6);
        if (CollectionUtils.isNotEmpty(keyPatterns)) {
            for (String keyPattern : keyPatterns) {
                if (StringUtils.isNotBlank(keyPattern)) {
                    String encodedPattern = keyPattern;
                    try {
                        encodedPattern = URLEncoder.encode(keyPattern, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        log.info("encode pattern fail", e);
                    }
                    query.add("keyPattern=" + encodedPattern);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(databases)) {
            for (String database : databases) {
                if (StringUtils.isNumeric(database)) {
                    query.add("database=" + database);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(query)) {
            int fragmentStartIndex = sourceUri.indexOf('#');
            if (fragmentStartIndex == -1) {
                fragmentStartIndex = sourceUri.length();
            }

            String prevPart = sourceUri.substring(0, fragmentStartIndex);
            String postPart = sourceUri.substring(fragmentStartIndex);
            String questionMark = "?";
            String mark = "&";
            final boolean condition = prevPart.contains(questionMark) && (prevPart.endsWith(questionMark) || prevPart.endsWith(mark));
            if (condition) {
                sourceUri = prevPart + StringUtils.join(query, mark) + postPart;
            } else if (prevPart.contains(questionMark)) {
                sourceUri = prevPart + mark + StringUtils.join(query, mark) + postPart;
            } else {
                sourceUri = prevPart + questionMark + StringUtils.join(query, mark) + postPart;
            }
        }

        return sourceUri;
    }

    private long addBackupRecord(long configId, long actId, BackupType backupType, DataSourceEnum sourceType, String groupCode, String sourceUri, String extraMark) {
        String remark = "No." + groupCode + "套" + sourceType.getDesc() + extraMark;
        BackupActivityRecord backupRecord = new BackupActivityRecord();
        Date now = new Date();
        backupRecord.setActId(actId);
        backupRecord.setConfigId(configId);
        backupRecord.setSourceUri(sourceUri);
        backupRecord.setGroupCode(groupCode);
        backupRecord.setBackupType(backupType);
        backupRecord.setBackupSource(sourceType);
        backupRecord.setRemark(remark);
        backupRecord.setEndTime(now);
        backupRecord.setCreateTime(now);
        backupRecord.setBackupState(BackupState.trying);

        int rs = backupActivityRecordMapper.insertSelective(backupRecord);
        log.info("addBackupRecord with configId:{}, rs:{}", configId, rs);
        if (rs <= 0) {
            return 0;
        }

        return backupRecord.getId();
    }

    private void updateBackupRecord(long recordId, HdptDataBackup.BackupResponse resp, Throwable e) {
        BackupActivityRecord backupRecord = new BackupActivityRecord();
        backupRecord.setId(recordId);
        if (e != null) {
            backupRecord.setBackupState(BackupState.error);
            backupRecord.setFailMsg(e.getMessage());
            backupRecord.setBackupPath(StringUtils.EMPTY);
            backupRecord.setFileKey(StringUtils.EMPTY);
            backupRecord.setSourceNode(StringUtils.EMPTY);
        } else {
            int code = resp.getCode();
            if (code == 0) {
                HdptDataBackup.BackupResult result = resp.getResult();
                Date startTime = new Date(result.getReplicationTimeBefore());
                backupRecord.setBackupState(BackupState.completed);
                backupRecord.setFenceTime(startTime);
                backupRecord.setStartTime(startTime);
                backupRecord.setEndTime(new Date(result.getReplicationTimeAfter()));
                backupRecord.setBackupPath(result.getPath());
                backupRecord.setFileKey(result.getKey());
                backupRecord.setFileSize(result.getFileSize());
                backupRecord.setSourceNode(result.getSourceAddress());
            } else {
                backupRecord.setBackupState(BackupState.failed);
                backupRecord.setFailMsg(resp.getMsg());
                backupRecord.setBackupPath(StringUtils.EMPTY);
                backupRecord.setFileKey(StringUtils.EMPTY);
                backupRecord.setSourceNode(StringUtils.EMPTY);
            }
        }

        int rs = backupActivityRecordMapper.updateByPrimaryKeySelective(backupRecord);
        log.info("updateBackupRecord with recordId:{}, resp:{}, rs:{}", recordId, resp, rs);
    }

    private void updateSinkRecord(long sinkRecordId, HdptDataBackup.SimpleResponse resp, Throwable e) {
        BackupActivitySinkRecord sinkRecord = new BackupActivitySinkRecord();
        sinkRecord.setId(sinkRecordId);
        if (e != null) {
            sinkRecord.setSinkState(SinkState.error);
            sinkRecord.setFailMsg(e.getMessage());
        } else {
            int code = resp.getCode();
            if (code == 0) {
                sinkRecord.setSinkState(SinkState.completed);
            } else {
                sinkRecord.setSinkState(SinkState.failed);
                sinkRecord.setFailMsg(resp.getMsg());
            }
        }

        int rs = backupActivitySinkRecordMapper.updateByPrimaryKeySelective(sinkRecord);
        log.info("updateSinkRecord with sinkRecordId:{}, sinkState:{}, rs:{}", sinkRecordId, sinkRecord.getSinkState(), rs);
    }

    public Response<DataSource> queryActDataSource(long actId, DataSourceEnum sourceType) {
        String groupCode = sourcePropSupport.actGroupCode(actId, sourceType);
        String sourceUri = sourcePropSupport.sourceUri(actId, sourceType);
        DataSource data = new DataSource();
        data.setGroupCode(groupCode);
        data.setName(sourceType.getDesc() + "第" + groupCode + "套");
        data.setSourceType(sourceType);
        data.setSourceUri(sourceUri);
        return Response.success(data);
    }

    public Response<DataSource> queryActDataSource(long configId) {
        BackupActivityConfig config = backupActivityConfigMapper.selectByPrimaryKey(configId);
        if (config == null) {
            return Response.fail(400, "Invalid configId");
        }
        String groupCode = sourcePropSupport.actGroupCode(config.getActId(), config.getBackupSource());
        String sourceUri = sourcePropSupport.sourceUri(config.getActId(), config.getBackupSource());
        sourceUri = addSourceUriQueryIfNecessary(sourceUri, config.getKeyPatterns(), config.getDatabases());
        DataSource data = new DataSource();
        data.setGroupCode(groupCode);
        data.setName(config.getBackupSource().getDesc() + "第" + groupCode + "套");
        data.setSourceType(config.getBackupSource());
        data.setSourceUri(sourceUri);
        return Response.success(data);
    }

    public void clearExpiredTimerBackup() {
        Date now = new Date();
        int reserved = sysParameterService.get("reserved_timer_backup_days", Integer.class, 5);
        final Date expiredTime = DateUtils.addDays(now, -reserved);
        doClearExpiredBackup(BackupType.timer, expiredTime);
    }

    public void clearExpiredEventBackup() {
        Date now = new Date();
        final Date expiredTime = DateUtils.addMonths(now, -4);
        doClearExpiredBackup(BackupType.event, expiredTime);
    }

    public void doClearExpiredBackup(BackupType backupType, Date expiredTime) {
        while (true) {
            List<BackupActivityRecord> records = backupActivityRecordMapper.selectExpiredBackupRecords(backupType, expiredTime, 100);
            if (CollectionUtils.isEmpty(records)) {
                return;
            }

            List<Long> ids = records.stream().map(BackupActivityRecord::getId).collect(Collectors.toList());
            List<HdptDataBackup.BackupResource> resources = records.stream()
                    .filter(record -> record.getBackupState() == BackupState.completed)
                    .map(record -> HdptDataBackup.BackupResource.newBuilder()
                            .setStorageType(record.getStorageType())
                            .setStoragePath(record.getBackupPath())
                            .setFileKey(record.getFileKey())
                            .setFenceTime(record.getFenceTime().getTime())
                            .build())
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(resources)) {
                return;
            }

            HdptDataBackup.CleanRequest req = HdptDataBackup.CleanRequest.newBuilder()
                    .setSeq(UUID.randomUUID().toString())
                    .addAllResources(resources)
                    .build();
            HdptDataBackup.SimpleResponse resp = backupServerService.clean(req);

            if (resp.getCode() == 0) {
                //更新状态
                int rs = backupActivityRecordMapper.batchUpdateBackupState(ids, BackupState.deleted);
                log.info("doClearExpiredBackup batchDeleteBackupRecord with backupType:{}, ids:{}, rs:{}", backupType, ids, rs);
            }
        }
    }

    public Response<?> batchDeleteBackupRecord(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Response.ok();
        }

        List<BackupActivityRecord> records = backupActivityRecordMapper.selectByIds(ids);
        List<HdptDataBackup.BackupResource> resources = records.stream()
                .filter(record -> record.getBackupState() == BackupState.completed)
                .map(record -> HdptDataBackup.BackupResource.newBuilder()
                        .setStorageType(record.getStorageType())
                        .setStoragePath(record.getBackupPath())
                        .setFileKey(record.getFileKey())
                        .setFenceTime(record.getFenceTime().getTime())
                        .build())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(resources)) {
            return Response.fail(400, "No valid record can be deleted.");
        }

        HdptDataBackup.CleanRequest req = HdptDataBackup.CleanRequest.newBuilder()
                .setSeq(UUID.randomUUID().toString())
                .addAllResources(resources)
                .build();
        HdptDataBackup.SimpleResponse resp = backupServerService.clean(req);

        if (resp.getCode() == 0) {
            //更新状态
            int rs = backupActivityRecordMapper.batchUpdateBackupState(ids, BackupState.deleted);
            log.info("batchDeleteBackupRecord with ids:{}, rs:{}", ids, rs);
            return Response.ok();
        }

        return Response.fail(500, "Fail to delete:" + resp.getMsg());
    }

    public Response<Long> sink(long recordId, String sourceUri, List<String> clearPatterns, String remark) {
        BackupActivityRecord record = backupActivityRecordMapper.selectByPrimaryKey(recordId);
        if (record == null) {
            return Response.fail(400, "Invalid recordId.");
        }

        BackupActivitySinkRecord sinkRecord = new BackupActivitySinkRecord();
        sinkRecord.setRecordId(recordId);
        sinkRecord.setSinkSourceUri(sourceUri);
        sinkRecord.setRemark(remark);
        sinkRecord.setSinkState(SinkState.trying);
        sinkRecord.setCreateTime(new Date());
        int rs = backupActivitySinkRecordMapper.insertSelective(sinkRecord);
        log.info("sink add sink record with recordId:{}, uri:{}, rs:{}", recordId, sourceUri, rs);

        if (rs <= 0) {
            return Response.fail(500, "fail to add sink record");
        }

        final long sinkRecordId = sinkRecord.getId();
        backupExecutor.execute(() -> {
            try {
                HdptDataBackup.SimpleResponse resp = actBackupClientService.sink("act:redis:sink:" + sinkRecordId, sourceUri, record.getStorageType().intValue(), record.getBackupPath(), record.getFileKey(), record.getFenceTime().getTime(), true, clearPatterns);
                updateSinkRecord(sinkRecordId, resp, null);
            } catch (Exception e) {
                log.error("sink with sink record id:{} exception:", sinkRecordId, e);
                updateSinkRecord(sinkRecordId, null, e);
            }
        });

        return Response.success(sinkRecordId);
    }

    public Response<SinkState> querySinkResult(long sinkRecordId) {
        BackupActivitySinkRecord sinkRecord = backupActivitySinkRecordMapper.selectByPrimaryKey(sinkRecordId);
        if (sinkRecord == null) {
            return Response.fail(400, "sink record not exist");
        }

        return Response.success(sinkRecord.getSinkState());
    }
}
