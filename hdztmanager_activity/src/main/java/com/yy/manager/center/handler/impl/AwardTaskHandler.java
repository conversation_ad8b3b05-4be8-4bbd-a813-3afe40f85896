package com.yy.manager.center.handler.impl;

import com.yy.manager.center.entity.CenterConfigCategory;
import com.yy.manager.center.handler.AbstractConfigHandler;
import com.yy.manager.center.vo.ConfigItem;
import com.yy.manager.datasource.database.DynamicDataSourceContextHolder;
import com.yy.manager.datasource.database.HdztDataSourceContextHolder;
import com.yy.manager.hdzt.entity.AwardTask;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.service.IAwardTaskService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("awardTaskChandler")
public class AwardTaskHandler extends AbstractConfigHandler<AwardTask> {

    @Autowired
    private IAwardTaskService awardTaskService;

    @Override
    protected Map<String, AwardTask> getOriginalItems(HdztActivity activity) {
        List<AwardTask> awardTasks;
        try {
            HdztDataSourceContextHolder.setDataSourceTypeByActId(activity.getActId());
            awardTasks = awardTaskService.listAwardTask(activity.getActId());
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }

        if (CollectionUtils.isEmpty(awardTasks)) {
            return Collections.emptyMap();
        }

        return awardTasks.stream().collect(Collectors.toMap(task -> String.valueOf(task.getTaskId()), Function.identity()));
    }

    @Override
    protected ConfigItem buildConfigItem(long actId, AwardTask original) {
        ConfigItem item = new ConfigItem();
        item.setActId(actId);
        item.setItemId(String.valueOf(original.getTaskId()));
        item.setItemName(original.getTaskName());
        item.setItemDesc(original.getRemark());

        return item;
    }
}
