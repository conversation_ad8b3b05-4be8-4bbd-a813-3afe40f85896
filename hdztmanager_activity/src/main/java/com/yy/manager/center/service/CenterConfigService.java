package com.yy.manager.center.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.common.utils.StringUtils;
import com.yy.manager.center.constant.CheckState;
import com.yy.manager.center.entity.CenterConfigCategory;
import com.yy.manager.center.handler.ConfigHandler;
import com.yy.manager.center.mapper.CenterConfigCategoryMapper;
import com.yy.manager.center.state.CheckStatePersist;
import com.yy.manager.center.vo.ActConfigInfo;
import com.yy.manager.center.vo.ConfigItem;
import com.yy.manager.center.vo.ConfigSnippet;
import com.yy.manager.ecology.service.IGeActAttrService;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.service.IHdztActivityAttrService;
import com.yy.manager.hdzt.service.IHdztActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CenterConfigService {

    public static final String ACTIVITY_GREY_STATUS = "activity_grey_status";

    public static final String AWARD_GREY_STATUS = "award_grey_status";

    @Resource
    private CenterConfigCategoryMapper centerConfigCategoryMapper;

    @Autowired
    private Map<String, ConfigHandler> configHandlers;

    @Autowired
    private IHdztActivityService hdztActivityService;

    @Autowired
    private CheckStatePersist checkStatePersist;

    @Autowired
    private IHdztActivityAttrService hdztActivityAttrService;

    @Autowired
    private IGeActAttrService geActAttrService;

    public ActConfigInfo queryActivityInfo(int actId) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity == null) {
            throw new IllegalArgumentException("activity not exist!");
        }

        String ztGreyStatus = hdztActivityAttrService.getActAttr(actId, ACTIVITY_GREY_STATUS, "1");
        String awardGreyStatus = hdztActivityAttrService.getActAttr(actId, AWARD_GREY_STATUS, "1");

        String hdzkGreyStatus = geActAttrService.queryActivityGreyNew(actId, ACTIVITY_GREY_STATUS);

        ActConfigInfo result = new ActConfigInfo();
        result.setActId(actId);
        result.setActName(activity.getActName());
        result.setActStatus(activity.getStatus());
        result.setBeginTime(activity.getBeginTime());
        result.setEndTime(activity.getEndTime());
        result.setBeginShowTime(activity.getBeginTimeShow());
        result.setEndShowTime(activity.getEndTimeShow());
        result.setZtGreyStatus(Integer.parseInt(ztGreyStatus));
        result.setAwardGreyStatus(Integer.parseInt(awardGreyStatus));
        int zkGreyStatus = StringUtils.isNumeric(hdzkGreyStatus) ? Integer.parseInt(hdzkGreyStatus) : 0;
        result.setZkGreyStatus(zkGreyStatus);

        return result;
    }

    public List<CenterConfigCategory> queryConfigCategory() {
        QueryWrapper<CenterConfigCategory> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("show_order");
        return centerConfigCategoryMapper.selectList(wrapper);
    }

    public List<ConfigItem> queryConfigItems(long actId, int categoryId) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity == null) {
            throw new IllegalArgumentException("queryConfigItems fail, activity not exist!");
        }

        CenterConfigCategory configCategory = centerConfigCategoryMapper.selectById(categoryId);
        if (configCategory == null) {
            throw new IllegalArgumentException("queryConfigItems fail, category not exist!");
        }

        ConfigHandler configHandler = getConfigHandler(configCategory);

        return configHandler.queryItems(activity, configCategory);
    }

    public List<ConfigSnippet> queryConfigSnippets(int actId, int categoryId, String itemId) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity == null) {
            throw new IllegalArgumentException("queryConfigSnippets fail, activity not exist!");
        }

        CenterConfigCategory configCategory = centerConfigCategoryMapper.selectById(categoryId);
        if (configCategory == null) {
            throw new IllegalArgumentException("queryConfigSnippets fail, category not exist!");
        }

        ConfigHandler configHandler = getConfigHandler(configCategory);

        return configHandler.querySnippets(activity, configCategory, itemId);
    }

    public String querySnippetValue(int actId, int categoryId, String itemId, String snippetId) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity == null) {
            throw new IllegalArgumentException("querySnippetValue fail, activity not exist!");
        }

        CenterConfigCategory configCategory = centerConfigCategoryMapper.selectById(categoryId);
        if (configCategory == null) {
            throw new IllegalArgumentException("querySnippetValue fail, category not exist!");
        }

        ConfigHandler configHandler = getConfigHandler(configCategory);

        return configHandler.querySnippetValue(activity, configCategory, itemId, snippetId);
    }

    public Integer saveSnippetState(int actId, int categoryId, String itemId, String snippetId, CheckState state) {
        HdztActivity activity = hdztActivityService.selectByActId(actId);
        if (activity == null) {
            throw new IllegalArgumentException("saveSnippetState fail, activity not exist!");
        }

        CenterConfigCategory configCategory = centerConfigCategoryMapper.selectById(categoryId);
        if (configCategory == null) {
            throw new IllegalArgumentException("saveSnippetState fail, category not exist!");
        }

        ConfigHandler configHandler = getConfigHandler(configCategory);
        String snippetValue = configHandler.querySnippetValue(activity, configCategory, itemId, snippetId);

        return checkStatePersist.saveCheckState(actId, categoryId, itemId, snippetId, snippetValue, state);
    }

    @NotNull
    private ConfigHandler getConfigHandler(@NotNull CenterConfigCategory configCategory) {
        ConfigHandler configHandler = configHandlers.get(configCategory.getHandlerName());
        if (configHandler == null) {
            throw new IllegalStateException("category config handler not implemented");
        }

        return configHandler;
    }
}
