package com.yy.manager.center.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yy.manager.center.entity.CenterConfigCheckResult;
import org.apache.ibatis.annotations.Param;

@DS("hdztmanager")
public interface CenterConfigCheckResultMapper extends BaseMapper<CenterConfigCheckResult> {

    CenterConfigCheckResult getCheckResult(@Param("actId") int actId,
                                           @Param("categoryId") int categoryId,
                                           @Param("itemId") String itemId,
                                           @Param("snippetId") String snippetId,
                                           @Param("snippetValueMd5") String snippetValueMd5);


    int saveCheckResult(CenterConfigCheckResult result);

    int updateCheckResult(@Param("id") long id, @Param("checkState") int checkState);
}
