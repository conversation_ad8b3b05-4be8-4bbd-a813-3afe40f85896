package com.yy.manager.center.converter.impl;

import com.google.common.base.Splitter;
import com.yy.common.utils.StringUtils;
import com.yy.manager.center.converter.SnippetConverter;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("snippetWhitelistConverter")
public class SnippetWhitelistConverter implements SnippetConverter<String> {

    @Override
    public String convert(String snippetValue) {
        if (StringUtils.isEmpty(snippetValue)) {
            return "0";
        }

        List<String> whitelist = Splitter.on(',').omitEmptyStrings().trimResults().splitToList(snippetValue);
        return String.valueOf(whitelist.size());
    }
}
