package com.yy.manager.center.handler;

import com.yy.manager.center.entity.CenterConfigCategory;
import com.yy.manager.center.entity.CenterConfigSnippet;
import com.yy.manager.center.vo.ConfigItem;
import com.yy.manager.center.vo.ConfigSnippet;
import com.yy.manager.hdzt.entity.HdztActivity;

import java.util.List;

public interface ConfigHandler {

    /**
     * 查询所有配置item，item里包含snippets
     * @param activity
     * @param category
     * @return
     */
    List<ConfigItem> queryItems(HdztActivity activity, CenterConfigCategory category);

    /**
     * 查询单个配置item
     * @param activity
     * @param category
     * @param itemId
     * @return
     */
    default List<ConfigSnippet> querySnippets(HdztActivity activity, CenterConfigCategory category, String itemId) {
        throw new UnsupportedOperationException(("handler unsupported"));
    }

    /**
     * 查询单个snippet的值，一般用于一些比较大的配置值
     * @param activity
     * @param category
     * @param itemId
     * @param snippetId
     * @return
     */
    default String querySnippetValue(HdztActivity activity, CenterConfigCategory category, String itemId, String snippetId) {
        throw new UnsupportedOperationException("handler unsupported");
    }

}
