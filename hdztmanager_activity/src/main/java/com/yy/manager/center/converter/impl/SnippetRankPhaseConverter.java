package com.yy.manager.center.converter.impl;

import com.yy.manager.center.converter.SnippetConverter;
import org.springframework.stereotype.Component;

@Component("snippetRankPhaseConverter")
public class SnippetRankPhaseConverter implements SnippetConverter<Integer> {
    @Override
    public String convert(Integer snippetValue) {
        return "https://webtest.yy.com/ge_brid/#/configEdit/" + snippetValue;
    }
}
