package com.yy.manager.center.handler.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yy.manager.center.handler.AbstractConfigHandler;
import com.yy.manager.center.vo.ConfigItem;
import com.yy.manager.datasource.database.DynamicDataSourceContextHolder;
import com.yy.manager.datasource.database.GameecologyDataSourceContextHolder;
import com.yy.manager.ecology.entity.ActLayerViewDefine;
import com.yy.manager.ecology.service.IActLayerViewDefineService;
import com.yy.manager.hdzt.entity.HdztActivity;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("layerViewDefineChandler")
public class LayerViewDefineHandler extends AbstractConfigHandler<ActLayerViewDefine> {

    @Autowired
    private IActLayerViewDefineService layerViewDefineService;

    @Override
    protected Map<String, ActLayerViewDefine> getOriginalItems(HdztActivity activity) {
        QueryWrapper<ActLayerViewDefine> wrapper = new QueryWrapper<>();
        wrapper.eq("act_id", activity.getActId());
        List<ActLayerViewDefine> defines;
        try {
            GameecologyDataSourceContextHolder.setDataSourceTypeByActId(activity.getActId());
            defines = layerViewDefineService.list(wrapper);
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }

        if (CollectionUtils.isEmpty(defines)) {
            return Collections.emptyMap();
        }

        return defines.stream().collect(Collectors.toMap(define -> String.valueOf(define.getItemTypeKey()), Function.identity()));
    }

    @Override
    protected ConfigItem buildConfigItem(long actId, ActLayerViewDefine original) {
        ConfigItem item = new ConfigItem();
        item.setActId(actId);
        item.setItemId(String.valueOf(original.getItemTypeKey()));
        item.setItemName("【" + original.getRemark() + "】挂件配置");
        item.setItemDesc(original.getRemark());

        return item;
    }
}
