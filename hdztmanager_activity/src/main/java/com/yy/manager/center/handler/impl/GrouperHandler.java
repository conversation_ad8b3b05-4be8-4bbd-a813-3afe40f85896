package com.yy.manager.center.handler.impl;

import com.yy.manager.center.handler.AbstractConfigHandler;
import com.yy.manager.center.vo.ConfigItem;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.thrift.client.FtsGroupServiceClient;
import com.yy.manager.thrift.fts_group_center.ActivityInfo;
import com.yy.manager.utils.SysEnvHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

@Component("grouperChandler")
public class GrouperHandler extends AbstractConfigHandler<ActivityInfo> {

    @Autowired
    private FtsGroupServiceClient ftsGroupServiceClient;

    @Override
    protected Map<String, ActivityInfo> getOriginalItems(HdztActivity activity) {
        ActivityInfo activityInfo = ftsGroupServiceClient.getGroupActInfo(activity.getActId());
        if (activityInfo == null) {
            return Collections.emptyMap();
        }

        return Map.of(String.valueOf(activity.getActId()), activityInfo);
    }

    @Override
    protected ConfigItem buildConfigItem(long actId, ActivityInfo original) {
        ConfigItem item = new ConfigItem();
        item.setActId(actId);
        item.setItemId(String.valueOf(actId));
        item.setItemName("分组系统配置");
        item.setItemDesc("boss后台分组系统时间配置");

        return item;
    }

}
