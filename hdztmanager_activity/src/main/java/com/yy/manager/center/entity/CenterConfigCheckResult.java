package com.yy.manager.center.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 记录手动设置的校验结果
 */
@Getter
@Setter
public class CenterConfigCheckResult {

    @TableId
    protected Long id;

    protected Integer actId;

    protected Integer categoryId;

    protected String itemId;

    protected String snippetId;

    protected String snippetValueMd5;

    protected Integer checkState;

    protected Date createTime;

    protected Date updateTime;
}
