package com.yy.manager.center.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CenterConfigSnippet {

    @TableId
    protected Integer id;

    protected Integer categoryId;

    protected String snippetId;

    protected Integer snippetType;

    protected String snippetName;

    protected String snippetDesc;

    protected Integer required;

    protected String checker;

    protected String converter;

    protected Integer inUse;

    protected Integer showOrder;

    protected Date createTime;
}
