package com.yy.manager.center.constant;

public enum CheckState {

    success, fail, unknown;

    CheckState() {}

    public static CheckState of(boolean checkResult) {
        return checkResult ? success : fail;
    }

    public static CheckState of(int code) {
        if (values().length <= code) {
            throw new IllegalArgumentException("code is invalid!");
        }

        return values()[code];
    }
}
