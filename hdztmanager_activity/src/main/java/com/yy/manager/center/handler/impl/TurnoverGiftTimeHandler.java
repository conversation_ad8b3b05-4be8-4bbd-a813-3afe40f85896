package com.yy.manager.center.handler.impl;

import com.yy.manager.center.handler.AbstractConfigHandler;
import com.yy.manager.center.vo.ConfigItem;
import com.yy.manager.hdzt.entity.HdztActivity;
import com.yy.manager.hdzt.entity.RankingItemTransform;
import com.yy.manager.hdzt.service.IRankingItemTransformService;
import com.yy.manager.thrift.client.TPropStoreServiceClient;
import com.yy.manager.thrift.to_service.THudongPropTimer;
import com.yy.manager.time.checker.TurnoverGiftTimeChecker;
import com.yy.manager.utils.Convert;
import com.yy.manager.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component("turnoverGiftTimeChandler")
public class TurnoverGiftTimeHandler extends AbstractConfigHandler<TurnoverGiftTimeChecker.TurnoverGiftTimeConfig> {

    @Autowired
    private IRankingItemTransformService rankingItemTransformService;

    @Autowired
    private TPropStoreServiceClient tPropStoreServiceClient;

    @Override
    protected Map<String, TurnoverGiftTimeChecker.TurnoverGiftTimeConfig> getOriginalItems(HdztActivity activity) {
        List<RankingItemTransform> rankingItemTransforms =
                rankingItemTransformService.selectByActId(activity.getActId());
        Set<Integer> appids = new HashSet<>();
        Map<Integer, Long> giftIdTimeMap = new HashMap<>();
        Map<Integer, THudongPropTimer> giftMap = new HashMap<>();
        for (RankingItemTransform rankingItemTransform : rankingItemTransforms) {
            appids.add(getTAppIdByBusiId(rankingItemTransform.getBusiId()));
            int giftId = Convert.toInt(rankingItemTransform.getBusiItemId());
            if(giftId > 1000000) {
                continue;
            }
            if(giftId > 0) {
                giftIdTimeMap.put(giftId, 0L);
            }
        }

        List<Integer> appidList = new ArrayList<>(appids);
        for (Integer appid : appidList) {
            List<THudongPropTimer> tHudongPropTimers = tPropStoreServiceClient.GetTHudongPropTimer(appid);
            for (THudongPropTimer tHudongPropTimer : tHudongPropTimers) {
                if(giftIdTimeMap.containsKey(tHudongPropTimer.getPropId())) {
                    giftIdTimeMap.put(tHudongPropTimer.getPropId(), tHudongPropTimer.getExecuteTime());
                    giftMap.put(tHudongPropTimer.getPropId(), tHudongPropTimer);
                }
            }
        }
        Map<String, TurnoverGiftTimeChecker.TurnoverGiftTimeConfig> map = new HashMap<>();
        for (Integer giftId : giftIdTimeMap.keySet()) {
            TurnoverGiftTimeChecker.TurnoverGiftTimeConfig config = new TurnoverGiftTimeChecker.TurnoverGiftTimeConfig();
            long executeTime = giftIdTimeMap.get(giftId);
            config.setExecuteTime(executeTime/1000);
            config.setStartTime(executeTime/1000);
            config.setPropIds(List.of((long)giftId));
            THudongPropTimer tHudongPropTimer = giftMap.get(giftId);
            if(tHudongPropTimer != null) {
                config.setVisible(tHudongPropTimer.getVisible());
                config.setCanBuy(tHudongPropTimer.getCanBuy());
                config.setPropName(tHudongPropTimer.getPropName());
            }
            map.put(giftId.toString(), config);
        }
        return map;
    }

    @Override
    protected ConfigItem buildConfigItem(long actId, TurnoverGiftTimeChecker.TurnoverGiftTimeConfig original) {
        ConfigItem item = new ConfigItem();
        item.setActId(actId);
        item.setItemId(String.valueOf(original.getPropIds().get(0)));
        item.setItemName("营收活动礼物检查【" + original.getPropIds() + "】");
        item.setItemDesc("营收活动礼物时间配置");
        return item;
    }

    public static int getTAppIdByBusiId(int busiId) {
        return switch (busiId) {
            case 400 -> 36;
            case 500 -> 2;
            case 810 -> 34;
            default -> 0;
        };

    }
}
