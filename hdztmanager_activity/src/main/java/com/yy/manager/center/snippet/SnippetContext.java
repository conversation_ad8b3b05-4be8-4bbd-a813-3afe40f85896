package com.yy.manager.center.snippet;

import com.yy.manager.center.entity.CenterConfigCategory;
import com.yy.manager.center.entity.CenterConfigSnippet;
import com.yy.manager.hdzt.entity.HdztActivity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SnippetContext {

    protected HdztActivity activity;

    protected CenterConfigCategory configCategory;

    protected String itemId;

    protected CenterConfigSnippet configSnippet;

    public SnippetContext() {
    }

    public SnippetContext(HdztActivity activity, CenterConfigCategory configCategory, String itemId, CenterConfigSnippet configSnippet) {
        this.activity = activity;
        this.configCategory = configCategory;
        this.itemId = itemId;
        this.configSnippet = configSnippet;
    }

    public static SnippetContext of(HdztActivity activity, CenterConfigCategory configCategory, String itemId, CenterConfigSnippet configSnippet) {
        return new SnippetContext(activity, configCategory, itemId, configSnippet);

    }
}
