package com.yy.manager.resource.controller;

import com.yy.manager.resource.entity.ActResource;
import com.yy.manager.resource.service.ActResourceService;
import com.yy.manager.utils.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("actResource")
public class ActResourceController {

    @Autowired
    private ActResourceService actResourceService;

    @GetMapping("getActResources")
    public Response<List<ActResource>> getActResources(@RequestParam("actId") long actId) {
        var actResources = actResourceService.getActResourceByActId(actId);
        return Response.success(actResources);
    }

    @PutMapping("addActResource")
    public Response<?> addActResource(@RequestBody ActResource actResource) {
        var actId = actResource.getActId();
        if (actId == null || actId <= 0) {
            return Response.fail(400, "活动ID不能为空");
        }

        return actResourceService.addActResource(actResource);
    }

    @PostMapping("updateActResource")
    public Response<?> updateActResource(@RequestBody ActResource actResource) {
        var actId = actResource.getActId();
        if (actId == null || actId <= 0) {
            return Response.fail(400, "活动ID不能为空");
        }

        return actResourceService.updateActResource(actResource);
    }

    @DeleteMapping("deleteActResource")
    public Response<?> deleteActResource(@RequestParam("actId") long actId,
                                         @RequestParam("resourceKey") String resourceKey) {
        return actResourceService.deleteActResource(actId, resourceKey);
    }

    @RequestMapping("copyActResources")
    public Response<?> copyActResources(@RequestParam("actId") long actId,
                                        @RequestParam("copyActId") long copyActId) {
        return actResourceService.copyActResources(actId, copyActId);
    }
}
