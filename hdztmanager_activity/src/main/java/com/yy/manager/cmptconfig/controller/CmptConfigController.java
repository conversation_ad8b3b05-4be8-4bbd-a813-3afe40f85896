package com.yy.manager.cmptconfig.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.common.annotation.Log;
import com.yy.common.enums.BusinessType;
import com.yy.common.utils.SysEnvHelper;
import com.yy.manager.clear.bean.DropDownVo;
import com.yy.manager.cmptconfig.bean.ComponentAttrDefineVo;
import com.yy.manager.cmptconfig.bean.ComponentConfigVo;
import com.yy.manager.cmptconfig.bean.CopyHdzjComponentDTO;
import com.yy.manager.cmptconfig.bean.DeleteActComponentDTO;
import com.yy.manager.cmptconfig.bean.SaveActComponentDTO;
import com.yy.manager.cmptconfig.bean.SaveComponentAttrDTO;
import com.yy.manager.cmptconfig.service.ComponentConfigService;
import com.yy.manager.ecology.entity.HdzjComponent;
import com.yy.manager.exception.SuperException;
import com.yy.manager.utils.Response;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.common.config.SystemConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/4 14:40
 **/
@RestController
@RequestMapping("cmptConfig")
public class CmptConfigController {
    private static Logger logger = LoggerFactory.getLogger(CmptConfigController.class);
    @Autowired
    private ComponentConfigService componentConfigService;

    /**
     * 获取多个活动下使用到的组件
     **/
    @ApiOperation("查询多个活动组件列表")
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:listCmptByActIds')")
    @GetMapping("listCmptByActIds")
    public Response<List<HdzjComponent>> listCmptByActIds(@RequestParam(value = "actIds", defaultValue = "") String actIds) {
        if (SysEnvHelper.isDeploy()) {
            return Response.fail(-1,"生产环境不可操作");
        }
        return Response.success(componentConfigService.listCmptByActIds(actIds));
    }

    /**
     * 复制多个活动组件
     **/
    @ApiOperation("查询多个活动组件列表")
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:mutliCopyCmpt')")
    @PostMapping("mutliCopyCmpt")
    public Response mutliCopyCmpt(@RequestBody JSONObject data) {
        if (SysEnvHelper.isDeploy()) {
            return Response.fail(-1,"生产环境不可操作");
        }
        List<HdzjComponent> list = JSON.parseArray(data.getString("list"), HdzjComponent.class);
        return Response.success(componentConfigService.mutliCopyCmpt(list, data.getIntValue("targetActId")));
    }

    /**
     * 获取该活动下使用到的组件
     **/
    @ApiOperation("查询活动组件列表")
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:queryActCom')")
    @GetMapping("list")
    public Response<List<List<ComponentConfigVo>>> list(@RequestParam(value = "actId", defaultValue = "0") long actId,
                                                        @RequestParam(value = "componentId", defaultValue = "0") long componentId,
                                                        @RequestParam(value = "moduleName") String moduleName) {
        return Response.success(componentConfigService.listActComponent(actId, componentId, moduleName));
    }

    /**
     * 复制组件
     **/
    @ApiOperation("复制组件")
    @Log(title = "复制组件", businessType = BusinessType.COPY)
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:copy')")
    @PostMapping("copyActComponent")
    public Response<String> copyActComponent(@RequestBody CopyHdzjComponentDTO param) {
        try {
            if (param == null) {
                return Response.fail(999, "参数异常");
            }
            componentConfigService.copyHdzjComponent(param);

            return Response.success("复制成功");
        } catch (Exception ex) {
            logger.error("copyActComponent error,param={}", param, ex);
            return Response.fail(500, ex.getMessage());
        }
    }

    /**
     * 获取组件的属性定义
     **/
    @ApiOperation("查询组件属性定义")
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:queryAttrDefine')")
    @PostMapping("listComponentAttrDefine")
    public Response<List<ComponentAttrDefineVo>> listComponentAttrDefine(@RequestParam(value = "componentId", defaultValue = "0") int componentId
            , @RequestParam(value = "actId", defaultValue = "0") long actId
            , @RequestParam(value = "useIndex", defaultValue = "0") int useIndex) {
        try {
            return Response.success(componentConfigService.listComponentAttrDefine(actId, componentId, useIndex));
        } catch (SuperException ex) {
            logger.error("listComponentAttrDefine error,actId={},componentId={},useIndex={}", actId, componentId, useIndex, ex);
            return Response.fail(500, ex.getMessage());
        }
    }

    /**
     * 获取已有组件下拉选项
     **/
    @ApiOperation("查询全部组件")
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:queryAll')")
    @PostMapping("listComponentDefine")
    public Response<List<DropDownVo>> listComponentDefine() {
        return Response.success(componentConfigService.listComponentDefine());
    }

    /**
     * 保存活动组件
     **/
    @ApiOperation("保存组件")
    @Log(title = "保存组件", businessType = BusinessType.SAVE)
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:edit')")
    @PostMapping("saveActComponent")
    public Response<String> saveActComponent(@RequestBody SaveActComponentDTO param) {
        try {
            if (param == null) {
                return Response.fail(999, "参数异常");
            }
            componentConfigService.saveActComponent(param);
            return Response.success("保存成功");
        } catch (Exception ex) {
            logger.error("saveActComponent error,param={}", param, ex);
            return Response.fail(500, ex.getMessage());
        }
    }

    /**
     * 删除组件
     **/
    @ApiOperation("删除组件")
    @Log(title = "删除组件", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:del')")
    @PostMapping("deleteActComponent")
    public Response<String> deleteActComponent(@RequestBody DeleteActComponentDTO param) {
        if (SysEnvHelper.isDeploy()) {
            return Response.fail(999, "正式环境不允许删除");
        }
        try {
            if (param == null) {
                return Response.fail(999, "参数异常");
            }
            componentConfigService.deleteActComponent(param);
        } catch (Exception ex) {
            logger.error("deleteActComponent error,param={}", param, ex);
            return Response.fail(500, ex.getMessage());
        }

        return Response.success("删除成功");
    }

    @ApiOperation("保存组件属性")
    @Log(title = "保存组件属性", businessType = BusinessType.SAVE)
    @PreAuthorize("@ss.hasPermi('activity:cmptConfig:savaAttr')")
    @PostMapping("saveComponentAttrValue")
    public Response<String> saveComponentAttrValue(@RequestBody SaveComponentAttrDTO param) {
        try {
            if (param == null) {
                return Response.fail(999, "参数异常");
            }
            componentConfigService.saveComponentAttrValues(param);
        } catch (Exception ex) {
            logger.error("saveComponentAttrValue error,param={}", param, ex);
            return Response.fail(500, ex.getMessage());
        }

        return Response.success("保存成功");
    }
}
