package com.yy.manager.cmptconfig.service;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yy.manager.awardconfig.bean.AwardTaskVo;
import com.yy.manager.awardconfig.service.AwardTaskConfigService;
import com.yy.manager.cmptconfig.bean.DropTypeData;
import com.yy.manager.hdzt.entity.AwardModel;
import com.yy.manager.hdzt.entity.AwardPackage;
import com.yy.manager.hdzt.service.IAwardPackageService;
import com.yy.manager.hdzt.service.impl.AwardModelServiceImpl;
import com.yy.manager.utils.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class WelfareDropType implements DropTypeDataFetch {

    @Autowired
    private AwardModelServiceImpl awardModelService;

    @Autowired
    private AwardTaskConfigService awardTaskConfigService;

    @Autowired
    private IAwardPackageService awardPackageService;

    @Override
    @DS("#{@ad.getHdztDs(#actId)}")
    public List<DropTypeData> getDropTypeList(long actId, Map<String, Object> param) {
        List<AwardTaskVo> tasks = awardTaskConfigService.listAwardTask(actId);
        List<AwardModel> models = new ArrayList<>();
        List<DropTypeData> list = new ArrayList<>();
        for (AwardTaskVo task : tasks) {
            List<AwardModel> awardModels = awardModelService.listAwards(actId, task.getTaskId());
            List<Integer> packageIds = awardModels.stream().map(AwardModel::getPackageId).collect(Collectors.toList());
            List<AwardPackage> awardPackages = awardPackageService.listPackage(packageIds);
            Map<Integer, String> packageName = new HashMap<>();
            for (AwardPackage awardPackage : awardPackages) {
                packageName.put(awardPackage.getPackageId(), awardPackage.getPackageName());
            }
            for (AwardModel model : awardModels) {
                DropTypeData dropTypeData = new DropTypeData();
                dropTypeData.setShow(model.getTaskId()+":"+model.getPackageId()+":"+packageName.get(model.getPackageId()));
                dropTypeData.setValue(Convert.toString(model.getPackageId()));
                list.add(dropTypeData);
            }
        }
        return list;
    }

    @Override
    public Map<String, Object> buildParam(String dataSourceVal, long actId) {
        Map<String, Object> param = new HashMap<>();
        param.put("actId", actId);
        param.put("taskId", Convert.toLong(dataSourceVal));
        return param;
    }

    @Override
    public String getKey() {
        return "welfare";
    }
}
