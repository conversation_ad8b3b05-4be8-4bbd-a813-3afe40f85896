package com.yy.manager.cmptconfig.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/21 11:04
 **/
@Data
public class ComponentDefineVos {
    /**
     * 最通用稳定[1001~1999]
     **/
    private List<ComponentDefineVo> levelOne;
    /**
     * 较通用稳定[2000~3999]
     **/
    private List<ComponentDefineVo> levelTwo;
    /**
     * 发展完善中[5000~9999]
     **/
    private List<ComponentDefineVo> levelThree;
    /**
     * 活动定制组件
     **/
    private List<ComponentDefineVo> levelFour;
}
