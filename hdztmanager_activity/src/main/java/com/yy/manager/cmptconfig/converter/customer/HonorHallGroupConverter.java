package com.yy.manager.cmptconfig.converter.customer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yy.manager.cmptconfig.converter.ValueChecker;
import com.yy.manager.ecology.entity.ActResultGroup;
import com.yy.manager.ecology.entity.HdzjComponentAttrDefine;
import com.yy.manager.ecology.service.IActResultGroupService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 荣耀殿堂 act_result_group 解析
 *
 * <AUTHOR>
 * @date 2022/2/17 16:39
 **/
@Component
public class HonorHallGroupConverter implements CustomerAttrValueConverter {
    private static Logger logger = LoggerFactory.getLogger(HonorHallGroupConverter.class);
    @Autowired
    private IActResultGroupService actResultGroupService;

    @Override
    public Integer supportComponentId() {
        return 5027;
    }

    @Override
    public String supportPropName() {
        return "groupDefines";
    }

    @Override
    public Object readFromDbAndParse(long actId, HdzjComponentAttrDefine attrDefine) {
        List<ActResultGroup> groups = actResultGroupService.select(actId);
        if (CollectionUtils.isEmpty(groups)) {
            return new ArrayList<>();
        }
        JSONArray array = new JSONArray();
        for (ActResultGroup group : groups) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("group_id", group.getGroupId());
            jsonObject.put("group_name", group.getGroupName());
            jsonObject.put("type", group.getType());

            array.add(jsonObject);
        }

        return array;
    }

    @Override
    public void parseAndSaveToDb(long actId, Object data) {
        logger.info("actId={},data={}", actId, data);
        JSONArray array = ValueChecker.removeEmpty(data);
        if (array.isEmpty()) {
            return;
        }

        List<ActResultGroup> groups = new ArrayList<>();
        for (int index = 0; index < array.size(); index++) {
            JSONObject jsonObject = array.getJSONObject(index);
            ActResultGroup group = new ActResultGroup();
            group.setActId(actId);
            group.setGroupId(jsonObject.getString("group_id"));
            group.setGroupName(jsonObject.getString("group_name"));
            group.setType(jsonObject.getLongValue("type"));
            group.setSort(-1L);

            groups.add(group);
        }

        actResultGroupService.remove(actId);
        actResultGroupService.saveBatch(actId, groups);
    }
}
