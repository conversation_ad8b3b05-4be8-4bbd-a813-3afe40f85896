package com.yy.manager.cmptconfig.bean;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/18 10:07
 **/
@Data
public class ComponentAttrVo {
    /**
     * 属性名称
     **/
    private String name;
    /**
     * 属性说明
     **/
    private String desc;
    /**
     * 属性类型
     **/
    private String type;
    /**
     * 配置示例
     **/
    private String configExample;
    /**
     * 默认值
     **/
    private String defaultValue;

    private List<ComponentAttrVo> subAttrList;
}
