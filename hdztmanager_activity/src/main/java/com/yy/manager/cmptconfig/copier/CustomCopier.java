package com.yy.manager.cmptconfig.copier;

import com.google.common.collect.Maps;
import com.yy.manager.exception.SuperException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/18 14:21
 **/
@Component
public class CustomCopier {
    private static Map<Integer, CustomComponentCopier> copierMap = Maps.newHashMap();

    @Autowired
    private CustomComponentCopier[] copiers;

    @PostConstruct
    public void init() {
        for (CustomComponentCopier copier : copiers) {
            if (copierMap.containsKey(copier.supportComponentId())) {
                throw new SuperException("already exist copier for componentId:" + copier.supportComponentId());
            }
            copierMap.put(copier.supportComponentId(), copier);
        }
    }

    public void doCopy(int componentId, long fromActId, long toActId) {
        CustomComponentCopier copier = copierMap.get(componentId);
        if (copier != null) {
            copier.copyConfig(fromActId, toActId);
        }
    }
}
