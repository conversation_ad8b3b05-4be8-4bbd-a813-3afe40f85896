package com.yy.manager.cmptconfig.bean;

import com.alibaba.fastjson.JSONObject;
import com.yy.manager.clear.bean.DropDownVo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 界面数据结构
 *
 * <AUTHOR>
 * @date 2022/1/6 11:07
 **/
@Data
public class ComponentAttrDefineVo {
    /**
     * 组件标识
     **/
    private Integer cmptId;
    /**
     * 属性名字
     **/
    private String propName;
    /**
     * 父id
     **/
    private String pid;
    /**
     * 标签文案
     **/
    private String labelText;
    /**
     * 控件类型
     **/
    private String propType;
    private Boolean repeat;
    private String placeholder;
    private String remark;
    /**
     * 子属性
     **/
    private List<ComponentAttrDefineVo> props;
    /**
     * 下拉选项
     **/
    private List<DropDownVo> options;
    /**
     * 数据
     **/
    private Object data;
    /**
     * 初始化给前端的结构
     **/
    private JSONObject selectRow;
    /**
     * 日期控件相关配置
     **/
    private String format;
    private String valueFormat;

    /**
     * 使用表格进行弹窗配置,只针对表格有效
     **/
    private boolean useDialog;

    /**
     * 标签，用于组件属性分类
     */
    private String tag;

    private Map<String, Object> ext;

    private long max = 9007199254740990L;

    private long min = -9007199254740990L;
}
