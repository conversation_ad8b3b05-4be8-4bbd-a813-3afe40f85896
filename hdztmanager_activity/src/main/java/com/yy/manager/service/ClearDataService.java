package com.yy.manager.service;

import com.google.common.collect.Maps;
import com.yy.manager.cleanup.service.CleanCommandService;
import com.yy.manager.clear.bean.CleanKeyParam;
import com.yy.manager.clear.service.mysql.ActMysqlDataClearService;
import com.yy.manager.clear.service.redis.ActRedisDataClearService;
import com.yy.manager.consts.ClearDataCommand;
import com.yy.manager.hdzt.service.IHdztActivityAttrService;
import com.yy.manager.utils.Clock;
import com.yy.manager.utils.Const;
import com.yy.manager.utils.Convert;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * @Author: 曾文帜
 * @Desciption:
 * @Date: 2022/11/23 11:08
 * @Modified:补充Author
 */
@Service
public class ClearDataService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActRedisDataClearService actRedisDataClearService;

    @Autowired
    private ActMysqlDataClearService actMysqlDataClearService;


    @Autowired
    private CleanCommandService cleanCommandService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private IHdztActivityAttrService hdztActivityAttrService;

    public void clearData(long actId, String clearDataCommand) {
        Clock clock = new Clock();
        String technical = hdztActivityAttrService.getActTechnical(actId);
        List<String> uids = StringUtils.isBlank(technical)?Collections.emptyList() : List.of(technical);

        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(Const.RL_DEFAULT_CONFIG, String.format("数据清理开始1,actId:%s,command:%s", actId, clearDataCommand), uids);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(Const.RL_DEFAULT_CONFIG, String.format("数据清理开始2,actId:%s,command:%s", actId, clearDataCommand), uids);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(Const.RL_DEFAULT_CONFIG, String.format("数据清理开始3,actId:%s,command:%s", actId, clearDataCommand), uids);


        if (ClearDataCommand.ALL.equals(clearDataCommand)) {

            actMysqlDataClearService.cleanGreyData(actId + "");
            log.info("cleanAll mysql ok,actId:{}", actId);

            CleanKeyParam param = new CleanKeyParam();
            param.setActId(actId + "");
            actRedisDataClearService.cleanAll(param);
            log.info("cleanAll redis ok,actId:{}", actId);

        } else {
            Map<String, String> para = Maps.newHashMap();
            para.put("actId", actId + "");
            cleanCommandService.extCleanCommandList(actId, Convert.toLong(clearDataCommand), para);
        }

        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(Const.RL_DEFAULT_CONFIG, String.format("数据清理完成,actId:%s,command:%s,cost:%s", actId, clearDataCommand, clock.tag()), uids);
    }
}
