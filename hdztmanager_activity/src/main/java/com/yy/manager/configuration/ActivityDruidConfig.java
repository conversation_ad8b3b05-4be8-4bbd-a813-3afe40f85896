package com.yy.manager.configuration;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.yy.manager.datasource.entity.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

@Configuration
public class ActivityDruidConfig {


    @Autowired
    public void mybatisPlusInterceptor(MybatisPlusInterceptor interceptor) {
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        //这里为不同的表设置对应表名处理器
        dynamicTableNameInnerInterceptor.setTableNameHandler(new ActIdTableNameParse());
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);

    }

    @Deprecated
    @Bean("hdztJdbcTemplate")
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZT.getDs()));
        return jdbcTemplate;
    }

    @Deprecated
    @Bean("hdztJdbcTemplate2")
    public JdbcTemplate hdztJdbcTemplate2(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZT2.getDs()));
        return jdbcTemplate;
    }

    @Deprecated
    @Bean("hdztJdbcTemplate3")
    public JdbcTemplate hdztJdbcTemplate3(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZT3.getDs()));
        return jdbcTemplate;
    }

    @Deprecated
    @Bean("hdztJdbcTemplate4")
    public JdbcTemplate hdztJdbcTemplate4(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZT4.getDs()));
        return jdbcTemplate;
    }

    @Deprecated
    @Bean("hdztJdbcTemplate5")
    public JdbcTemplate hdztJdbcTemplate5(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZT5.getDs()));
        return jdbcTemplate;
    }

    @Deprecated
    @Bean("hdztJdbcTemplate6")
    public JdbcTemplate hdztJdbcTemplate6(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZT6.getDs()));
        return jdbcTemplate;
    }

    @Bean("hdztJdbcTemplate7")
    public JdbcTemplate hdztJdbcTemplate7(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZT7.getDs()));
        return jdbcTemplate;
    }


    @Deprecated
    @Primary
    @Bean("hdztManagerJdbcTemplate")
    public JdbcTemplate hdztManagerJdbcTemplate(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZTMANAGER.getDs()));
        return jdbcTemplate;
    }

    @Deprecated
    @Bean("ecologyJdbcTemplate")
    public JdbcTemplate ecologyJdbcTemplate(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZK.getDs()));
        return jdbcTemplate;
    }

    @Bean("ecologyJdbcTemplate7")
    public JdbcTemplate ecologyJdbcTemplate7(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.HDZK7.getDs()));
        return jdbcTemplate;
    }

    @Bean("hdptTemplateJdbcTemplate")
    public JdbcTemplate hdptTemplateJdbcTemplate(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(((DynamicRoutingDataSource) dataSource).getDataSource(DataSourceType.TEMPLATE.getDs()));
        return jdbcTemplate;
    }


}
