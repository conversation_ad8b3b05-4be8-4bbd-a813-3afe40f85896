package com.yy.manager.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ExecutorsConfig {

    @Bean("backupExecutor")
    public ThreadPoolTaskExecutor backupExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(20);
        executor.setAllowCoreThreadTimeOut(false);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("act-backup");
        executor.setAwaitTerminationSeconds(3 * 60);
        return executor;
    }

    @Primary
    @Bean("activitiExecutor")
    public ThreadPoolTaskExecutor activitiExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(100);
        executor.setAllowCoreThreadTimeOut(false);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("activiti-executor-");
        executor.setAwaitTerminationSeconds(60);
        return executor;
    }
}
