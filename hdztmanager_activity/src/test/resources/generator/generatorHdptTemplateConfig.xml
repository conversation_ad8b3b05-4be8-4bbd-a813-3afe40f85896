<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
		"http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<classPathEntry location="E:/maven/mysql/mysql-connector-java/5.1.38/mysql-connector-java-5.1.38.jar" />
	<context id="DB2Tables" targetRuntime="MyBatis3">
		<plugin type="org.mybatis.generator.plugins.CaseInsensitiveLikePlugin"></plugin>
		<plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"></plugin>

		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
						connectionURL="****************************************************************************************" userId="udb_data" password="z9tnj5kiev52bH8FoVOVwJLA">
		</jdbcConnection>
		<javaTypeResolver>
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>

		<!--todo 修改 targetPackage 参数 数据javabean的包名-->
		<javaModelGenerator targetPackage="com.yy.manager.acttemplate.entity" targetProject="./src/main/java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<sqlMapGenerator targetPackage="mapper/acttemplate" targetProject="./src/main/resources">
			<property name="enableSubPackages" value="false"/>
		</sqlMapGenerator>

		<!--todo 修改 targetPackage 参数 表mapper的包名-->
		<javaClientGenerator type="XMLMAPPER"
							 targetPackage="com.yy.manager.acttemplate.mapper"
							 targetProject="./src/main/java">
			<property name="enableSubPackages" value="true" />
			<property name="useLegacyBuilder" value="false" />
		</javaClientGenerator>

		<table tableName="act_skin"  enableCountByExample="false"
			   enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"
			   enableInsert="false" enableSelectByPrimaryKey="false" enableUpdateByPrimaryKey="false"/>

		<table tableName="act_skin_use"  enableCountByExample="false"
			   enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"
			   enableInsert="false" enableSelectByPrimaryKey="false" enableUpdateByPrimaryKey="false"/>

	</context>
</generatorConfiguration>
