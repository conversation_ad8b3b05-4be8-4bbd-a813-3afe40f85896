package com.yy;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * <AUTHOR>
 * @since 2025/5/14 17:41
 */
@SpringBootApplication
public class TestMain {

    public static void main(String[] args) {
        try {
            System.setProperty("spring.devtools.restart.enabled", "false");
            SpringApplication application = new SpringApplication(TestMain.class);
            application.setApplicationStartup(new BufferingApplicationStartup(2048));
            application.run(args);
        } catch (Throwable e) {
            throw e;
        }
    }
}
