package com.yy.manager.thrift.client;

import com.yy.java.component.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @since 2025/5/23 16:57
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {FtsPrizeEnvoyClient.class})
@EnableDubbo
@Slf4j
public class FtsPrizeEnvoyClientTest {

    @Autowired
    private FtsPrizeEnvoyClient client;

    @Test
    public void getRewardActInfo() {
        var info = client.getRewardActInfo(2025056003L);
        System.out.println(JsonUtils.serialize(info));
    }
}