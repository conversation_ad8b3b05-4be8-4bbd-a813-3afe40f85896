package com.yy.oss.service.impl;




import com.baidubce.auth.BceCredentials;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.baidubce.services.bos.model.ObjectMetadata;
import com.baidubce.services.bos.model.PutObjectRequest;
import com.yy.common.constant.Constants;
import com.yy.common.utils.StringUtils;
import com.yy.oss.entity.UploadResult;
import com.yy.oss.enumd.OssEnumd;
import com.yy.oss.exception.OssException;
import com.yy.oss.properties.OssProperties;
import com.yy.oss.service.abstractd.AbstractOssStrategy;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 百度智能云存储策略
 *
 * <AUTHOR>
 */
public class BceOssStrategy extends AbstractOssStrategy {

	private BosClient client;

	@Override
	public void init(OssProperties cloudStorageProperties) {
		properties = cloudStorageProperties;
		try {
			BceCredentials credentialProvider = new DefaultBceCredentials(properties.getAccessKey(), properties.getSecretKey());

			client = new BosClient(new BosClientConfiguration()
					.withEndpoint(properties.getEndpoint())
					.withCredentials(credentialProvider));

			createBucket();
		} catch (Exception e) {
			throw new OssException("百度智能云存储配置错误! 请检查系统配置:[" + e.getMessage() + "]");
		}
	}

	@Override
	public void createBucket() {
		try {
			String bucketName = properties.getBucketName();
			if (client.doesBucketExist(bucketName)) {
				return;
			}
			client.createBucket(bucketName);
		} catch (Exception e) {
			throw new OssException("创建Bucket失败, 请核对百度智能云配置信息:[" + e.getMessage() + "]");
		}
	}

	@Override
	public String getServiceType() {
		return OssEnumd.BCE.getValue();
	}

	@Override
	public UploadResult upload(byte[] data, String path, String contentType) {
		return upload(new ByteArrayInputStream(data), path, contentType);
	}

	@Override
	public UploadResult upload(InputStream inputStream, String path, String contentType) {
		try {
			ObjectMetadata metadata = new ObjectMetadata();
			metadata.setContentType(contentType);
			client.putObject(new PutObjectRequest(properties.getBucketName(), path, inputStream, metadata));
		} catch (Exception e) {
			throw new OssException("上传文件失败，请检查百度智能云配置信息:[" + e.getMessage() + "]");
		}
		return new UploadResult().setUrl(getEndpointLink() + "/" + path).setFilename(path);
	}

	@Override
	public void delete(String path) {
		path = path.replace(getEndpointLink() + "/", "");
		try {
			client.deleteObject(properties.getBucketName(), path);
		} catch (Exception e) {
			throw new OssException("删除文件失败，请检查百度智能云配置信息:[" + e.getMessage() + "]");
		}
	}

	@Override
	public UploadResult uploadSuffix(byte[] data, String suffix, String contentType) {
		return upload(data, getPath(properties.getPrefix(), suffix), contentType);
	}

	@Override
	public UploadResult uploadSuffix(InputStream inputStream, String suffix, String contentType) {
		return upload(inputStream, getPath(properties.getPrefix(), suffix), contentType);
	}

	@Override
	public String getEndpointLink() {
		return Constants.HTTPS + properties.getBucketName() + "." + properties.getEndpoint();
	}
}
