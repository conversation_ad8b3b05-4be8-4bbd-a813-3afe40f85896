### 获取活动全量配置信息
GET {{host}}/template/base/config?@uid=50048391&ignorePermissionCheck=true&actId=2025056003
Accept: application/json

### 复制预览
POST {{host}}/template/copy/activity/preview2?@uid=50048391&ignorePermissionCheck=true
Accept: application/json
Content-Type: application/json

{
  "actId": 2025066001,
  "baseActId": 2025056003,
  "actName": "贴吧阵营大比拼6月",
  "beginTime": "2025-06-18 17:00:00",
  "endTime": "2025-07-01 00:00:00",
  "remark": "贴吧阵营大比拼6月"
}


### 复制活动
POST {{host}}/template/copy/activity?@uid=50048391&ignorePermissionCheck=true
Accept: application/json
Content-Type: application/json

{
  "actId": 2025066001,
  "baseActId": 2025056003,
  "actName": "贴吧阵营大比拼6月",
  "beginTime": "2025-06-18 17:00:00",
  "endTime": "2025-07-01 00:00:00",
  "remark": "贴吧阵营大比拼6月",
  "projectId": 2361
}

### 更新活动信息
POST {{host}}/template/update/activity?@uid=50048391&ignorePermissionCheck=true
Accept: application/json
Content-Type: application/json

{
  "actId": 2025066001,
  "actName": "贴吧阵营大比拼6月",
  "beginTime": "2025-06-18 17:00:00",
  "endTime": "2025-07-01 00:00:00",
  "remark": "贴吧阵营大比拼6月"
}


### 删除活动配置
GET {{host}}/template/delete/config?@uid=50048391&ignorePermissionCheck=true&actId=2025066001
Accept: application/json
Content-Type: application/json