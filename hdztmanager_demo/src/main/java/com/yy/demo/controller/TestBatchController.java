package com.yy.demo.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yy.common.core.controller.BaseController;
import com.yy.common.core.domain.AjaxResult;
import com.yy.demo.domain.TestDemo;
import com.yy.demo.service.ITestDemoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试批量方法
 *
 * <AUTHOR> Li
 * @date 2021-05-30
 */
@Api(value = "测试批量方法", tags = {"测试批量方法"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/demo/batch")
public class TestBatchController extends BaseController {

    private final ITestDemoService iTestDemoService;

    /**
     * 新增批量方法 可完美替代 saveBatch 秒级插入上万数据 (对mysql负荷较大)
     */
    @ApiOperation(value = "新增批量方法")
    @PostMapping("/add")
//	@DataSource(DataSourceType.SLAVE)
    public AjaxResult<Void> add() {
        List<TestDemo> list = new ArrayList<>();
        int size = 1000;
        for (int i = 0; i < size; i++) {
            list.add(new TestDemo().setOrderNum(-1L).setTestKey("批量新增").setValue("测试新增"));
        }
        return toAjax(iTestDemoService.saveAll(list) ? 1 : 0);
    }

    /**
     * 新增或更新 可完美替代 saveOrUpdateBatch 高性能
     */
    @ApiOperation(value = "新增或更新批量方法")
    @PostMapping("/addOrUpdate")
//	@DataSource(DataSourceType.SLAVE)
    public AjaxResult<Void> addOrUpdate() {
        List<TestDemo> list = new ArrayList<>();
        int size = 1000;
        for (int i = 0; i < size; i++) {
            list.add(new TestDemo().setOrderNum(-1L).setTestKey("批量新增").setValue("测试新增"));
        }
        iTestDemoService.saveAll(list);
        for (int i = 0; i < list.size(); i++) {
            TestDemo testDemo = list.get(i);
            testDemo.setTestKey("批量新增或修改").setValue("批量新增或修改");
            if (i % 2 == 0) {
                testDemo.setId(null);
            }
        }
        return toAjax(iTestDemoService.saveOrUpdateAll(list) ? 1 : 0);
    }

    /**
     * 删除批量方法
     */
    @ApiOperation(value = "删除批量方法")
    @DeleteMapping()
//	@DataSource(DataSourceType.SLAVE)
    public AjaxResult<Void> remove() {
        return toAjax(iTestDemoService.remove(new LambdaQueryWrapper<TestDemo>()
            .eq(TestDemo::getOrderNum, -1L)) ? 1 : 0);
    }

}
